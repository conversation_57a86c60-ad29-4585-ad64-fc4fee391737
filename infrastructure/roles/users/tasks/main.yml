---
- name: Set hostname to nord-client
  hostname:
    name: "{{ hostname }}"

- name: Create administrative users
  user:
    name: "{{ item.username }}"
    state: present
    password: "{{ item.initial_password | password_hash('sha512') }}"
    update_password: on_create
    groups: "{{ item.groups }}"
  loop: "{{ users }}"
  register: result_users
  notify: "Expire new users' passwords"

- name: Update user SSH keys
  authorized_key:
    user: "{{ item.username }}"
    key: "https://github.com/{{ item.github }}.keys"
  loop: "{{ users }}"
