---
- name: Create nord admin user
  user:
    name: "nord"
    state: "present"
    password: "!"
    home: "/srv/nord/"

- name: Add nord SSH key to root
  authorized_key:
    user: "root"
    key: "{{ ssh.public_key }}"

- name: Add nord SSH key
  authorized_key:
    user: "nord"
    key: "{{ ssh.public_key }}"

- name: Update nginx configuration
  template:
    src: nginx.conf.j2
    dest: "/etc/nginx/sites-available/default"
  notify: "Reload Nginx"

- name: Update deploy.sh configuration
  template:
    src: deploy.sh.j2
    dest: "/srv/nord/deploy.sh"



