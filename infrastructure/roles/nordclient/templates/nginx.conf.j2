upstream backend {
  server localhost:8888;
}

server {
  listen 443 ssl;
  server_name {{ domain }};
  root /srv/nord/nord-client/build/;
  index index.html index.htm;

  location / {

  # Authenticaion needs to be deployed only on staging environment
    auth_basic "🔑 Please authenticate to access this staging location 👮";
    auth_basic_user_file /srv/nord/.htpasswd;

    add_header Content-Security-Policy "default-src 'self'; connect-src 'self' https://login.microsoftonline.com https://raw.githubusercontent.com https://api.exchangeratesapi.io https://cnbc.p.rapidapi.com; base-uri 'self'; block-all-mixed-content; font-src 'self' https: data:; frame-ancestors 'self'; img-src 'self' https://image.cnbcfm.com data:; object-src 'none'; script-src 'self'; worker-src 'self' blob:; script-src-elem 'self' https://cdnjs.cloudflare.com; script-src-attr 'none'; style-src 'self' https: 'unsafe-inline'; upgrade-insecure-requests";
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    try_files $uri /index.html;

  # This header needs to be deployed only on production
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
  }

  location /api/ {
    proxy_pass http://backend;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_cache_bypass $http_upgrade;
  }

  # gzip
  gzip on;
  gzip_vary on;
  gzip_proxied any;
  gzip_comp_level 6;
  gzip_types text/plain text/css text/xml application/javascript
  application/rss+xml application/atom+xml image/svg+xml;

  # SSL configuration
  ssl_certificate /etc/letsencrypt/live/{{ domain }}/fullchain.pem;
  ssl_certificate_key /etc/letsencrypt/live/{{ domain }}/privkey.pem;
  ssl_trusted_certificate /etc/letsencrypt/live/{{ domain }}/chain.pem;
}

server {
  listen 443 ssl http2;
  listen [::]:443 ssl http2;

  server_name {{ www_domain }};

  # SSL configuration
  ssl_certificate /etc/letsencrypt/live/{{ www_domain }}/fullchain.pem;
  ssl_certificate_key /etc/letsencrypt/live/{{ www_domain }}/privkey.pem;
  ssl_trusted_certificate /etc/letsencrypt/live/{{ www_domain }}/chain.pem;

  return 301 https://{{ domain }}$request_uri;
}

# HTTP redirects
server {
  server_name {{ domain }};

  listen 80 default_server;
  listen [::]:80 default_server;

  location / {
    return 301 https://{{ domain }}$request_uri;
  }
}