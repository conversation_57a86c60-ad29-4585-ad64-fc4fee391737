#!/bin/bash
NODE_VERSION=14.17.1
CLIENT_REPOSITORY_PATH="/srv/nord/nord-client"
BACKEND_REPOSITORY_PATH="/srv/nord/nord-api"
BUILD_DIRECTORY="/srv/nord/nord-client/build"
GIT_BRANCH="develop"

echo "$USER"
set -e

function log {
        echo -e "\n\e[2m[$(date)]\e[0m -- \e[1m$@\e[0m"
}

function git_checkout {
        git -C "$1" checkout $2
        git -C "$1" reset --hard
        git -C "$1" pull -r
}

function deploy_client {
        log "Checking out to client repository and pulling changes"
        git_checkout $CLIENT_REPOSITORY_PATH $GIT_BRANCH
        echo "$CLIENT_REPOSITORY_PATH"
        echo "$USER"
        npm --version
        log "npm install client"
        npm install --cwd "$CLIENT_REPOSITORY_PATH" --prefix "$CLIENT_REPOSITORY_PATH"

        log "npm run build"
        npm run build --cwd "$CLIENT_REPOSITORY_PATH" --prefix "$CLIENT_REPOSITORY_PATH"
}

function deploy_backend {
        log "Checking out to client repository and pulling changes"
        git_checkout $BACKEND_REPOSITORY_PATH $GIT_BRANCH

        log "npm install backend"
        cd "$BACKEND_REPOSITORY_PATH"
        npm install
        npx sequelize-cli db:migrate
        npx sequelize-cli db:seed:all
        pm2 stop www
        pm2 delete www
        pm2 start bin/www
}
$1