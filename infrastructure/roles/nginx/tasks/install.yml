---
- name: Update apt-get repo and cache
  apt:
   update_cache=yes
   force_apt_get=yes
   cache_valid_time=3600

- name: Install Nginx
  apt:
   name=nginx
   state=latest

- name: Generate Diffie-Hellman parameters
  openssl_dhparam:
    path: /etc/nginx/dhparam.pem
    size: 2048

- name: Update nginx.conf
  copy:
    src: nginx.conf
    dest: /etc/nginx/nginx.conf
    mode: u=rw,g=r,o=r
  notify: Reload Nginx

