---
- hosts: all
  remote_user: root
  roles:
    - role: users
      vars:
        hostname: "nord"
        users:
          - username: bmi<PERSON><PERSON>
            initial_password: 'changeme'
            groups: ['sudo']
            github: bmijac

          - username: t<PERSON><PERSON><PERSON><PERSON><PERSON>
            initial_password: 'changeme'
            groups: [ 'sudo' ]
            github: t<PERSON><PERSON><PERSON><PERSON><PERSON>

          - username: <PERSON><PERSON><PERSON><PERSON>
            initial_password: 'changeme'
            groups: [ 'sudo' ]
            github: r<PERSON><PERSON><PERSON><PERSON>

      tags: [ setup ]
    - role: nginx
      tags: [ nginx ]
    - role: ufw
      tags: [ setup ]
    - role: geerlingguy.certbot
      vars:
        certbot_admin_email: "<EMAIL>"
        certbot_auto_renew: false
        certbot_create_method: standalone
        certbot_create_if_missing: true
        certbot_create_standalone_stop_services: [ nginx ]
        certbot_certs:
          - domains:
              - "{{ domain }}"
          - domains:
              - "{{ www_domain }}"
      tags: [ certbot ]
    - role: certbot_cron
      tags: [ certbot ]
    - role: nordclient
      tags: [ nordclient ]
    - role: nodejs
      tags: [ setup ]
    - role: pm2
      tags: [ setup ]

  # These will run before roles, so that we can be sure that we have `pip`
  # ready.
  pre_tasks:
    - name: "Install pip"
      apt:
        update_cache: yes
        name: python3-pip

