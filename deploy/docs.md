# CI/CD of TPAccurate

We are currently in a transitioning period of CI/CD for Accurate.
Current state of things is as follows.

1. Deployment of `production backend and frontend` is done using a `bash script (/srv/nord/deploy.sh)` located on the production server. On deploy the function gets called (from GitLab) and builds either the backend or the frontend.
2. Deployment of `staging backend` is done on a GitLab Runner.
3. Deployment of `staging frontend` is done on a GitLab Runner. Building a React app on the staging server would fail because it would run our of memory.
4. Deployment of Landing page is done by hand on both staging and production.
5. Deployment of the Solver API is done the same way as production backend and frontend, that is, using the bash script located on the staging/production server.

Since the `staging frontend` is being built on GitLab it needs to be provided all the .env variables. This is done by echoing the `CLIENT_ENV_VARIABLES` GitLab variable into a `.env` file which is then used in the build process. After the build process is done the entire `build/` is copied to the staging server. Same should be done for both `staging backend` and `production frontend and backend` in the future.
