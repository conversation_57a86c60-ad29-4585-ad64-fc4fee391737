export enum CreditRatingValueEnum {
  'AAA/Aaa' = 'AAA/Aaa',
  'AA+/Aa1' = 'AA+/Aa1',
  'AA/Aa2' = 'AA/Aa2',
  'AA-/Aa3' = 'AA-/Aa3',
  'A+/A1' = 'A+/A1',
  'A/A2' = 'A/A2',
  'A-/A3' = 'A-/A3',
  'BBB+/Baa1' = 'BBB+/Baa1',
  'BBB/Baa2' = 'BBB/Baa2',
  'BBB-/Baa3' = 'BBB-/Baa3',
  'BB+/Ba1' = 'BB+/Ba1',
  'BB/Ba2' = 'BB/Ba2',
  'BB-/Ba3' = 'BB-/Ba3',
  'B+/B1' = 'B+/B1',
  'B/B2' = 'B/B2',
  'B-/B3' = 'B-/B3',
  'CCC+/Caa1' = 'CCC+/Caa1',
  'CCC/Caa2' = 'CCC/Caa2',
  'CCC-/Caa3' = 'CCC-/Caa3',
  'CC/Ca' = 'CC/Ca',
  'C/Ca' = 'C/Ca',
}

export const industries = [
  'Consumer Discretionary',
  'Consumer Staples',
  'Energy',
  'Financials',
  'Healthcare',
  'Industrials',
  'Materials',
  'Media & Communications',
  'Quasi Government',
  'Real Estate & REITs',
  'Sovereign',
  'Technology',
  'Utilities',
];

const getIsAnyImplicitQuestionAnswered = (row: any) => {
  for (let i = 1; i <= 11; i++) {
    if (row[`Implicit Support Q #${i}`] === 'X') {
      return true;
    }
  }
  return false;
};

const creditRatings = Object.values(CreditRatingValueEnum);

export async function getCompaniesFromSheet(sheet: any, countriesByName: any) {
  if (sheet.length === 0) {
    throw new Error('Sheet is empty. Please upload populated sheet.');
  }

  const companies = [];
  for (let i = 0, len = sheet.length; i < len; i++) {
    const company = sheet[i];

    const name = company['Company Name*'];
    const industry = company['Sector*'];
    const country = company['Country*'];
    const rating = company['Issuer Rating'] ?? null;
    const probabilityOfDefault = company['Probability of Default'] ?? null;
    const parentCompanyName = company['Parent Company Name'] ?? null;
    const note = company['Note'] ?? null;

    if (typeof name === 'number') throw new Error('Company Name cannot be a number.');
    if (typeof parentCompanyName === 'number') throw new Error('Parent Company Name cannot be a number.');

    const ringFencing = company['Implicit Support Q #1'] === 'X';
    const question1 = company['Implicit Support Q #2'] === 'X';
    const question2 = company['Implicit Support Q #3'] === 'X';
    const question3 = company['Implicit Support Q #4'] === 'X';
    const question4 = company['Implicit Support Q #5'] === 'X';
    const question5 = company['Implicit Support Q #6'] === 'X';
    const question6 = company['Implicit Support Q #7'] === 'X';
    const question7 = company['Implicit Support Q #8'] === 'X';
    const question8 = company['Implicit Support Q #9'] === 'X';
    const question9 = company['Implicit Support Q #10'] === 'X';
    const question10 = company['Implicit Support Q #11'] === 'X';

    const isAnyImplicitQuestionAnswered = getIsAnyImplicitQuestionAnswered(company);

    if (!name?.trim() && !industry?.trim() && !country?.trim() && !rating?.trim() && !probabilityOfDefault?.trim()) {
      continue;
    }

    if (!name && !industry && !country) {
      throw new Error('Company name, sector and country are required fields.');
    }
    if (name == null) {
      throw new Error('Company name cannot be empty.');
    } else if (name.length > 255) {
      throw new Error(`Company: ${name} too long. Company name should have less then 255 characters.`);
    }

    if (industry == null) {
      throw new Error('Company industry cannot be empty.');
    } else if (!industries.includes(industry)) {
      throw new Error(`Industry ${industry} is invalid.`);
    }

    if (country == null) {
      throw new Error('Company country cannot be empty.');
    } else if (!countriesByName[country]) {
      throw new Error(`Country ${country} is invalid.`);
    }

    if (rating && !creditRatings.includes(rating)) {
      throw new Error(`Issuer rating ${rating} is invalid.`);
    }

    if (probabilityOfDefault != null && (probabilityOfDefault < 0 || probabilityOfDefault > 100)) {
      throw new Error(`Probability of default ${probabilityOfDefault} needs to be number in range 0-100.`);
    }

    companies.push({
      parentCompanyName,
      name,
      industry,
      country,
      note,
      creditRating: {
        rating,
        ratingAdj: null,
        probabilityOfDefault,
        probabilityOfDefaultAdj: null,
      },
      assessment: isAnyImplicitQuestionAnswered
        ? {
            answers: {
              ringFencing,
              question1,
              question2,
              question3,
              question4,
              question5,
              question6,
              question7,
              question8,
              question9,
              question10,
            },
          }
        : null,
    });
  }

  return companies;
}
