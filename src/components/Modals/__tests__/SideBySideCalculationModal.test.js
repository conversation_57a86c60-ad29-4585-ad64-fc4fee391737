import React from 'react';
import { render, screen } from '@testing-library/react';
import { UserInfoContext } from '~/context/user';
import SideBySideCalculationModal from '../SideBySideCalculationModal';

// Mock the dependencies
jest.mock('../ReportViewPage/CalculationResults/BoundsGraph', () => {
  return function MockBoundsGraph() {
    return <div data-testid="bounds-graph">BoundsGraph</div>;
  };
});

jest.mock('../ReportViewPage/ReportCharacteristics', () => ({
  ReportCharacteristicsColumn: function MockReportCharacteristicsColumn() {
    return <div data-testid="report-characteristics">ReportCharacteristics</div>;
  },
}));

const mockUserInfo = {
  decimalPoint: '.',
  features: {},
};

const mockOriginalData = {
  id: 1,
  issueDate: '2023-01-01',
  maturityDate: '2024-01-01',
  createdAt: '2023-01-01',
  rateType: { type: 'fixed' },
  report: {
    finalInterestRate: 5.5,
    upperBound: 6.0,
    lowerBound: 4.0,
    midPoint: 5.0,
  },
  fixedRateIncrement: false,
};

const mockUpdatedData = {
  id: 2,
  issueDate: '2023-02-01',
  maturityDate: '2024-02-01',
  createdAt: '2023-02-01',
  rateType: { type: 'fixed' },
  report: {
    finalInterestRate: 5.8,
    upperBound: 6.2,
    lowerBound: 4.2,
    midPoint: 5.2,
  },
  fixedRateIncrement: false,
};

const defaultProps = {
  originalData: mockOriginalData,
  updatedData: mockUpdatedData,
  isShowing: true,
  onHide: jest.fn(),
  title: 'Test Modal',
  onOriginalClick: jest.fn(),
  onUpdatedClick: jest.fn(),
};

const renderWithContext = (props = {}) => {
  return render(
    <UserInfoContext.Provider value={{ userInfo: mockUserInfo }}>
      <SideBySideCalculationModal {...defaultProps} {...props} />
    </UserInfoContext.Provider>
  );
};

describe('SideBySideCalculationModal', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render without crashing when finalInterestRate is present', () => {
    renderWithContext();
    expect(screen.getByText('Test Modal')).toBeInTheDocument();
  });

  it('should handle missing report data gracefully', () => {
    const dataWithoutReport = {
      ...mockOriginalData,
      report: undefined,
    };
    
    renderWithContext({
      originalData: dataWithoutReport,
    });
    
    expect(screen.getByText('Test Modal')).toBeInTheDocument();
  });

  it('should handle missing finalInterestRate gracefully', () => {
    const dataWithoutFinalInterestRate = {
      ...mockOriginalData,
      report: {
        upperBound: 6.0,
        lowerBound: 4.0,
        midPoint: 5.0,
      },
    };
    
    renderWithContext({
      originalData: dataWithoutFinalInterestRate,
    });
    
    expect(screen.getByText('Test Modal')).toBeInTheDocument();
  });

  it('should not render when isShowing is false', () => {
    renderWithContext({ isShowing: false });
    expect(screen.queryByText('Test Modal')).not.toBeInTheDocument();
  });
});
