import { Button, DateRangeInput, Modal } from 'ui';

type ExportCashPoolsModalProps = {
  isShowing?: boolean;
  dataTestId?: string;
  handleOnClick: () => void;
  handleOnHide: () => void;
  dateRange: { startDate: Date | null; endDate: Date | null };
  setDateRange: React.Dispatch<React.SetStateAction<{ startDate: Date | null; endDate: Date | null }>>;
  isExporting: boolean;
};

function ExportCashPoolsModal({
  isShowing = true,
  dataTestId = '',
  handleOnClick,
  handleOnHide,
  dateRange,
  setDateRange,
  isExporting,
}: ExportCashPoolsModalProps) {
  if (!isShowing) return null;

  return (
    <Modal
      actionButtons={<Button disabled={isExporting} text="Export" onClick={handleOnClick} dataTestId={dataTestId} />}
      title="Export Cash Pool Data"
      width="s"
      onHide={handleOnHide}
    >
      {/* <Text color="deep-sapphire" variant="m-spaced" sx={{ lineHeight: '21px' }}>
        Select the desired date range
      </Text> */}
      <DateRangeInput
        dateRange={dateRange}
        onChange={(range: typeof dateRange) => setDateRange(range)}
        label="Date Range"
        height="40px"
        width="fullWidth"
      />
    </Modal>
  );
}

export default ExportCashPoolsModal;
