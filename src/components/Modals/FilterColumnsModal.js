import _ from 'lodash';
import { useContext, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';

import { updateTableColumns } from '~/api';
import { UserInfoContext } from '~/context';
import { TableColumnContext } from '~/context/tableColumn';
import { reportEnum } from '~/enums';
import {
  backToBackLoanColumns,
  updateColumns as updateBackToBackLoanColumns,
} from '~/reducers/tableColumns/backToBackLoanColumns.slice';
import {
  guaranteeColumns,
  updateColumns as updateGuaranteeColumns,
} from '~/reducers/tableColumns/guaranteeColumns.slice';
import { loanColumns, updateColumns as updateLoanColumns } from '~/reducers/tableColumns/loanColumns.slice';
import { routesEnum } from '~/routes';
import { Button, FlexLayout, Modal } from '~/ui';
import { getOrderedColumnKeys } from '~/utils/columnOrdering';

const typeToActionMapper = {
  [reportEnum.LOAN]: {
    reportColumns: loanColumns,
    updateColumns: updateLoanColumns,
  },
  [reportEnum.BACK_TO_BACK_LOAN]: {
    reportColumns: backToBackLoanColumns,
    updateColumns: updateBackToBackLoanColumns,
  },
  [reportEnum.GUARANTEE]: {
    reportColumns: guaranteeColumns,
    updateColumns: updateGuaranteeColumns,
  },
};

function FilterColumnsModal({ type, handleOnHide }) {
  const { dbTableColumns, setTrigger } = useContext(TableColumnContext);
  const { userInfo } = useContext(UserInfoContext);

  const location = useLocation();
  const dispatch = useDispatch();
  const columns = useSelector(typeToActionMapper[type].reportColumns);
  const isAnalyses = location.pathname === routesEnum.ANALYSES;
  const [columnsCopy, setColumnsCopy] = useState(
    dbTableColumns[type] ? { ...columns, ...dbTableColumns[type] } : columns
  );

  async function handleOnSelectClick() {
    const updateColumns = typeToActionMapper[type].updateColumns;
    await updateTableColumns({ [type]: columnsCopy });
    setTrigger({});
    dispatch(updateColumns(columnsCopy));
    handleOnHide();
  }

  useEffect(() => {
    if (userInfo.features.prepayment && type === reportEnum.LOAN) {
      setColumnsCopy((_state) => {
        return {
          ..._state,
          'Prepayment premium': dbTableColumns?.[type]?.['Prepayment premium'] ?? false,
          'Borrower prepayment option': dbTableColumns?.[type]?.['Borrower prepayment option'] ?? false,
        };
      });
    }
  }, [userInfo, type, dbTableColumns]);

  return (
    <Modal
      actionButtons={<Button text="Select" onClick={handleOnSelectClick} />}
      title="Add or remove columns."
      width="s"
      onHide={handleOnHide}
    >
      <FlexLayout alignItems="center" flexWrap="wrap" justifyContent="flex-start" space={4} sx={{ mt: -4 }}>
        {getOrderedColumnKeys(isAnalyses ? _.omit(columnsCopy, 'Note') : columnsCopy, type).map((column) => (
          <Button
            key={column}
            size="s"
            sx={{ mt: 4 }}
            text={column}
            variant={columnsCopy[column] ? 'primary' : 'gray'}
            onClick={() =>
              setColumnsCopy((_state) => {
                return { ..._state, [column]: !_state[column] };
              })
            }
          />
        ))}
      </FlexLayout>
    </Modal>
  );
}

export default FilterColumnsModal;
