import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import { updateParticipantUniqueId } from '../../api/cashPool';
import { Button, Modal, TextInput } from '../../ui';
import { showToast, showErrorToast } from '../../ui/components/Toast';
import { errorHandler } from '../../utils/errors';
import { updateAccounts, cashPoolTopCurrencyAccountSelector } from '../../reducers/cashPoolTopCurrencyAccount.slice';

type AddUniqueIdModalPropsType = {
  dataTestId?: string;
  participant: any;
  onHide: () => void;
  onSuccess?: () => void;
};

function AddUniqueIdModal({ participant, dataTestId, onHide, onSuccess }: AddUniqueIdModalPropsType) {
  const [uniqueId, setUniqueId] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { cashPoolId } = useParams<{ cashPoolId: string }>();
  const dispatch = useDispatch();
  const topCurrencyAccount = useSelector(cashPoolTopCurrencyAccountSelector);
  const account = topCurrencyAccount?.accounts?.find((account: any) => account?.companyId === participant?.id);

  const handleOnSubmitClick = async () => {
    if (!cashPoolId || !participant?.id) return;

    setIsSubmitting(true);
    try {
      await updateParticipantUniqueId({
        cashPoolId,
        participantId: participant.id,
        data: { uniqueId: uniqueId.trim() || null },
      });

      if (account) {
        const updatedAccount = {
          ...account,
          participant: {
            ...account.participant,
            uniqueId: uniqueId.trim() || null,
          },
        };
        dispatch(updateAccounts({ ...updatedAccount, value: true }));
      }

      showToast('Unique ID updated successfully');
      onSuccess?.();
      onHide();
    } catch (error) {
      errorHandler(error);
      showErrorToast('Failed to update unique ID');
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    if (account?.participant?.uniqueId) {
      setUniqueId(account.participant.uniqueId);
    }

    return () => {
      setUniqueId('');
    };
  }, [account]);

  if (!participant) return null;

  return (
    <Modal
      actionButtons={
        <Button text="Submit" onClick={handleOnSubmitClick} dataTestId={dataTestId} loading={isSubmitting} />
      }
      title={`Add Unique ID for ${account?.participant?.company?.name || participant?.name}`}
      width="s"
      onHide={onHide}
    >
      <TextInput
        label="Unique ID"
        width="fullWidth"
        value={uniqueId}
        onChange={setUniqueId}
        placeholder="Enter unique ID"
      />
    </Modal>
  );
}

export default AddUniqueIdModal;
