import React, { useContext, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import { deleteCompanyAuditTrail, getCompanyAuditTrail } from '~/api';
import { DeleteModal } from '~/components/Modals';
import { ThreeDotActionMenu, UserLog } from '~/components/Shared';
import { UserInfoContext } from '~/context/user';
import { isAdmin, NOTIFICATION_ACTIONS } from '~/enums';
import { company, resetCompany, setCompany } from '~/reducers/company.slice';
import { setNotification } from '~/reducers/notifications.slice';
import { Button, FlexLayout, LoadingSpinner, PageLayout } from '~/ui';
import { showToast, showErrorToast } from '~/ui/components/Toast';
import { cancellable } from '~/utils/promise';

import CompanyInfoCard from '../CompanyCreatePage/CompanyInfoCard';
import CreditRatingCard from '../CompanyCreatePage/CreditRatingCard';
import ImplicitSupportCard from '../CompanyCreatePage/ImplicitSupportCard';
import NotesCard from '../Shared/NotesCard';

function CompanyAuditTrailViewPage() {
  const dispatch = useDispatch();
  const history = useHistory();
  const data = useSelector(company);
  const { companyId, auditTrailId } = useParams();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const { userInfo } = useContext(UserInfoContext);

  useEffect(() => {
    const cancellablePromise = cancellable(getCompanyAuditTrail({ companyId, auditId: auditTrailId }));
    cancellablePromise.promise
      .then((res) => {
        const { id, parentCompanyId } = res;
        dispatch(setCompany({ ...res, isParent: id === parentCompanyId }));
      })
      .catch((err) => {
        if (err.cancelled) {
          cancellablePromise.cancel();
        }
        history.push(`/group-information/${companyId}/audit-trail`);
      });

    return () => {
      cancellablePromise.cancel();
      dispatch(resetCompany());
    };
  }, [dispatch, companyId, history, auditTrailId]);

  function onItemDelete() {
    deleteCompanyAuditTrail({ companyId, auditId: auditTrailId })
      .then(() => {
        history.push(`/group-information/${companyId}/audit-trail`);
        setShowDeleteModal(false);
        showToast('Audit trail has been successfully deleted.');
      })
      .catch(() => showErrorToast());
  }

  const getActionOptions = () => {
    const options = [];

    if (isAdmin(userInfo.role)) {
      options.push({ label: 'Delete', onClick: () => setShowDeleteModal(true) });
    } else {
      options.push({
        label: 'Notify admin to delete the audit trail',
        onClick: () =>
          dispatch(
            setNotification({
              action: NOTIFICATION_ACTIONS.DELETE_AUDIT_TRAIL,
              title: 'Notify admin to delete the audit trail',
              id: data.id,
            })
          ),
      });
    }

    return options;
  };

  const isLoading = !Object.keys(data).length;

  if (isLoading) return <LoadingSpinner />;

  return (
    <>
      <PageLayout
        title="Company Information"
        rightTitleContent={
          <FlexLayout alignItems="center" justifyContent="flex-end" space={8}>
            <UserLog label="Created by:" user={data?.createdBy} />
            <ThreeDotActionMenu options={getActionOptions()} />
          </FlexLayout>
        }
      >
        <CompanyInfoCard editMode={false} />
        <CreditRatingCard editMode={false} />
        <ImplicitSupportCard editMode={false} />
        <NotesCard editMode={false} description="Include any notes on the company here." note={data?.note} />
        <FlexLayout alignItems="flex-end" flexGrow="1" justifyContent="space-between">
          <Button text="Back" variant="gray" onClick={history.goBack} />
        </FlexLayout>
      </PageLayout>
      {showDeleteModal && (
        <DeleteModal
          item="audit trail"
          handleOnDeleteClick={onItemDelete}
          handleOnHide={() => setShowDeleteModal(false)}
        />
      )}
    </>
  );
}

export default CompanyAuditTrailViewPage;
