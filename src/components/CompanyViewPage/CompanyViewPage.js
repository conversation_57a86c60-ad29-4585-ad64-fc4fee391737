import { useContext, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import { deleteCompany, getCompany, updateCompany } from '~/api';
import { MovedModal } from '~/components/Modals';
import { ThreeDotActionMenu, UserLog } from '~/components/Shared';
import { UserInfoContext } from '~/context/user';
import { isAdmin, NOTIFICATION_ACTIONS } from '~/enums';
import { useCompanies, useUnsavedChangesWarning } from '~/hooks';
import { changeCompany, removeCompany } from '~/reducers/companies.slice';
import {
  company,
  isCompanyFormValid,
  resetCompany,
  setCompany,
  setIsPristine,
  transform,
  updateField,
} from '~/reducers/company.slice';
import { setNotification } from '~/reducers/notifications.slice';
import { routesEnum } from '~/routes';
import { But<PERSON>, FlexLayout, LoadingSpinner, PageLayout } from '~/ui';
import { showToast, showErrorToast } from '~/ui/components/Toast';
import { cancellable } from '~/utils/promise';
import { errorHandler } from '~/utils/errors';

import CompanyInfoCard from '../CompanyCreatePage/CompanyInfoCard';
import CreditRatingCard from '../CompanyCreatePage/CreditRatingCard';
import ImplicitSupportCard from '../CompanyCreatePage/ImplicitSupportCard';
import DeleteCompanyModal from '../GroupInformationPage/DeleteCompanyModal';
import NotesCard from '../Shared/NotesCard';
import AuditTrailModal from '~/components/Shared/AuditTrailModal';

function CompanyViewPage() {
  const dispatch = useDispatch();
  useCompanies(true); // refresh companies list
  const history = useHistory();
  const data = useSelector(company);
  const [Prompt] = useUnsavedChangesWarning({ isDirty: data.isDirty });
  const { companyId } = useParams();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showAuditTrailModal, setShowAuditTrailModal] = useState(false);
  const [isCompanyDeletedModalOpen, setIsCompanyDeletedModalOpen] = useState(false);
  const { userInfo } = useContext(UserInfoContext);

  useEffect(() => {
    const cancellablePromise = cancellable(getCompany(companyId));
    cancellablePromise.promise
      .then((res) => {
        const { id, parentCompanyId } = res;
        dispatch(setCompany({ ...res, isParent: id === parentCompanyId }));
      })
      .catch((err) => {
        if (err.cancelled) {
          cancellablePromise.cancel();
        }
        if (err?.response?.status === 404) {
          setIsCompanyDeletedModalOpen(true);
        }
      });

    return () => {
      cancellablePromise.cancel();
      dispatch(resetCompany());
    };
  }, [dispatch, companyId, history]);

  function onItemCreate() {
    const requestData = transform(data);

    updateCompany({ id: companyId, data: { ...requestData, createAuditTrail: true } })
      .then((res) => {
        dispatch(changeCompany(res));
        dispatch(setIsPristine());
        showToast(`Audit trail for "${requestData.name}" has been successfully created.`);
        history.push(routesEnum.GROUP_INFORMATION);
      })
      .catch(() => showErrorToast());
  }

  function onItemUpdate() {
    const requestData = transform(data);

    updateCompany({ id: companyId, data: { ...requestData, createAuditTrail: false } })
      .then((res) => {
        dispatch(changeCompany(res));
        dispatch(setIsPristine());
        showToast(`Company "${requestData.name}" has been successfully updated.`);
        history.push(routesEnum.GROUP_INFORMATION);
      })
      .catch(() => showErrorToast());
  }

  function onItemDelete() {
    deleteCompany(data.id)
      .then(() => {
        dispatch(removeCompany(data));
        dispatch(setIsPristine());
        showToast(`Company "${data.name}" has been successfully deleted.`);
        history.push(routesEnum.GROUP_INFORMATION);
      })
      .catch(errorHandler);
  }

  const getActionOptions = () => {
    const options = [];

    options.push({ label: 'Audit Trail', onClick: () => history.push(`/group-information/${data.id}/audit-trail`) });

    if (isAdmin(userInfo.role)) {
      options.push({ label: 'Delete', onClick: () => setShowDeleteModal(true) });
    } else {
      options.push({
        label: 'Notify admin to delete company',
        onClick: () =>
          dispatch(
            setNotification({
              action: NOTIFICATION_ACTIONS.DELETE_COMPANY,
              title: 'Notify admin to delete company',
              id: data.id,
            })
          ),
      });
    }

    return options;
  };

  if (isCompanyDeletedModalOpen) {
    return (
      <MovedModal
        title="Company was deleted"
        handleReportMovedClick={() => history.push(routesEnum.GROUP_INFORMATION)}
        isShowing={isCompanyDeletedModalOpen}
      />
    );
  }

  const auditTrailModalMessageIfParentRatingChanged = [
    'Clicking Overwrite will overwrite the current entry and entries of subsidiaries impacted by the change (i.e., their implicit support adjusted ratings).',
    <br />,
    <br />,
    'Clicking Create New will create an audit trail entry for the current entry and the entries of impacted subsidiaries.',
  ];

  const isLoading = !Object.keys(data).length;
  if (isLoading) return <LoadingSpinner />;

  return (
    <>
      <PageLayout
        title="Company Information"
        rightTitleContent={
          <FlexLayout alignItems="center" justifyContent="flex-end" space={8}>
            <FlexLayout alignItems="center" space={6}>
              <UserLog label="Created by:" user={data?.createdBy} />
              {data?.updatedBy && <UserLog label="Updated by:" user={data?.updatedBy} />}
            </FlexLayout>
            <ThreeDotActionMenu options={getActionOptions()} />
          </FlexLayout>
        }
      >
        <CompanyInfoCard />
        <CreditRatingCard renderAvailableCreditRatings />
        <ImplicitSupportCard />
        <NotesCard
          description="Include any notes on the company here."
          note={data.note}
          onChange={(value) => dispatch(updateField({ note: value }))}
        />
        <FlexLayout alignItems="flex-end" flexGrow="1" justifyContent="space-between">
          <Button text="Back" variant="gray" onClick={history.goBack} />
          <Button disabled={!isCompanyFormValid(data)} text="Update" onClick={() => setShowAuditTrailModal(true)} />
        </FlexLayout>
      </PageLayout>
      {Prompt}
      {showAuditTrailModal && (
        <AuditTrailModal
          handleOnCreateClick={onItemCreate}
          handleOnUpdateClick={onItemUpdate}
          modalInfoMessage={data.isParent && data.isRatingDirty && auditTrailModalMessageIfParentRatingChanged}
          onHide={() => setShowAuditTrailModal(false)}
        />
      )}
      {showDeleteModal && (
        <DeleteCompanyModal
          selectedItem={data}
          handleOnDeleteClick={onItemDelete}
          handleOnHide={() => setShowDeleteModal(false)}
        />
      )}
    </>
  );
}

export default CompanyViewPage;
