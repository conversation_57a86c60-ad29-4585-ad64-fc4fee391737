import { deleteUser, demoteToUser, getUsers, promoteToAdmin } from '~/api';
import { ThreeDotActionMenu } from '~/components/Shared';
import { rolesEnum } from '~/enums';
import { showErrorToast } from '~/ui/components/Toast';
import { formatDateString } from '~/utils/dates';

const fullName = 'fullName';
const username = 'username';
const email = 'email';
const role = 'role';
const client = 'client';
const type = 'type';
const lastLogin = 'lastLogin';

const fullNameColumn = { label: 'Full name', sortBy: 'fullName', value: fullName };
const usernameColumn = { label: 'Username', sortBy: 'username', value: username };
const emailColumn = { label: 'Email', sortBy: 'email', value: email, width: 300 };
const roleColumn = { label: 'Role', sortBy: 'role', value: role };
const clientColumn = { label: 'Client', sortBy: 'client', value: client };
const typeColumn = { label: 'Type', sortBy: 'type', value: type };
const lastLoginColumn = { label: 'Last Login', sortBy: 'lastLogin', value: lastLogin };

const adminColumns = [fullNameColumn, usernameColumn, emailColumn, roleColumn];
const getAdminUserData = ({ id, username, fullName, email, role }) => ({
  id,
  username,
  fullName,
  email,
  role,
});
const getAdminUsersData = (data = []) => data.map(getAdminUserData);

const superAdminColumns = [
  fullNameColumn,
  usernameColumn,
  emailColumn,
  clientColumn,
  roleColumn,
  typeColumn,
  lastLoginColumn,
];
const getSuperAdminUserData = ({
  id,
  username,
  fullName,
  email,
  role,
  client,
  socialLogins,
  lastLogin,
  dateFormat,
}) => {
  return {
    id,
    username,
    fullName,
    email,
    role,
    client: client.name,
    type: socialLogins && socialLogins.length > 0 ? 'SSO' : 'User/Pass',
    lastLogin: lastLogin ? formatDateString(lastLogin, dateFormat, true) : '-',
  };
};
const getSuperAdminUsersData = (data = [], userInfo) =>
  data.map((user) => getSuperAdminUserData({ ...user, dateFormat: userInfo.dateFormat }));

export const getColumnsAndData = (role, userInfo) =>
  role === rolesEnum.ADMIN
    ? [adminColumns, getAdminUsersData]
    : [superAdminColumns, (data) => getSuperAdminUsersData(data, userInfo)];

export const renderTableActionColumn = (item, users, setUsers, id, role) => {
  const options = [];

  if (item.role === rolesEnum.USER) {
    options.push({
      label: 'Promote to Admin',
      onClick: async () => {
        promoteToAdmin(item.id)
          .then(() => getUsers().then(setUsers))
          .catch(() => showErrorToast());
      },
    });
  }

  if (role === rolesEnum.SUPERADMIN && item.role === rolesEnum.ADMIN) {
    options.push({
      label: 'Demote to User',
      onClick: () => {
        demoteToUser(item.id)
          .then(() => getUsers().then(setUsers))
          .catch(() => showErrorToast());
      },
    });
  }

  if (role === rolesEnum.SUPERADMIN && item.id !== id) {
    options.push({
      label: 'Delete',
      onClick: () => {
        deleteUser(item.id)
          .then(() => setUsers(users.filter((u) => u.id !== item.id)))
          .catch(() => showErrorToast());
      },
    });
  }

  if (!options.length) return null;

  return <ThreeDotActionMenu options={options} />;
};
