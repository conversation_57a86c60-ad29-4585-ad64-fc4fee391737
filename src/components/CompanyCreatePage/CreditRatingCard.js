import axios from 'axios';
import React, { useContext, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { updateImplicitProbabilityOfDefault, updateImplicitRating } from '~/api';
import { CreditRatingSingleSelect } from '~/components/Shared';
import { UserInfoContext } from '~/context/user';
import { useCompanies } from '~/hooks';
import {
  company,
  updateIsProbabilityOfDefaultAdjLoading,
  updateProbabilityOfDefault,
  updateProbabilityOfDefaultAdj,
  updateRating,
  updateRatingAdj,
} from '~/reducers/company.slice';
import { Box, Card, FlexLayout, NumberInput, Table } from '~/ui';
import { showToast, showErrorToast } from '~/ui/components/Toast';
import { isParentCompany } from '~/utils/companies';
import { errorHandler } from '~/utils/errors';

import { getCreditRatingsColumns, getCreditRatingsData, tooltips } from './CreditRatingCard.utils';

function CreditRatingCard({ editMode = true, renderAvailableCreditRatings = false }) {
  const data = useSelector(company);
  const dispatch = useDispatch();
  const companies = useCompanies(false);
  const { userInfo } = useContext(UserInfoContext);
  const [probabilityOfDefaultTimeout, setProbabilityOfDefaultTimeout] = useState(0);
  const [probabilityOfDefaultCancelToken, setProbabilityOfDefaultCancelToken] = useState(null);

  const isParent = isParentCompany(data);

  function handleOnCreditRatingChange(value) {
    try {
      dispatch(updateRating(value));
      if (data?.assessment?.name && !isParent) {
        const { answers, name } = data?.assessment;
        const parentCompanyId = data?.parentCompanyId || companies.find((c) => c.id === c.parentCompanyId)?.id;

        updateImplicitRating({
          ringFencing: answers.ringFencing,
          assessmentName: name,
          creditRating: { rating: value, probabilityOfDefault: data?.creditRating?.probabilityOfDefault },
          parentCompanyId,
        }).then(({ creditRating: { ratingAdj, probabilityOfDefaultAdj } }) => {
          dispatch(updateRatingAdj(ratingAdj));
          if (probabilityOfDefaultAdj != null && !data?.creditRating?.probabilityOfDefaultAdj?.overriden) {
            dispatch(updateProbabilityOfDefaultAdj({ value: probabilityOfDefaultAdj }));
          }
          showToast('Issuer rating has been successfully updated.');
        });
      }
    } catch (err) {
      errorHandler(err);
    }
  }

  function handleOnProbabilityOfDefaultChange(value) {
    const cancelToken = axios.CancelToken.source();
    dispatch(updateProbabilityOfDefault(value));
    if (probabilityOfDefaultTimeout) {
      clearTimeout(probabilityOfDefaultTimeout);
      probabilityOfDefaultCancelToken.cancel();
      setProbabilityOfDefaultTimeout(0);
      setProbabilityOfDefaultCancelToken(null);
    }
    if (value == null && !data?.creditRating?.probabilityOfDefaultAdj?.overriden) {
      dispatch(updateProbabilityOfDefaultAdj({ value: null }));
    } else {
      if (
        data?.creditRating?.rating &&
        data?.creditRating?.ratingAdj &&
        !data?.creditRating?.probabilityOfDefaultAdj?.overriden
      ) {
        setProbabilityOfDefaultCancelToken(cancelToken);
        setProbabilityOfDefaultTimeout(
          setTimeout(() => {
            updateImplicitProbabilityOfDefault(
              {
                creditRating: {
                  rating: data?.creditRating?.rating,
                  ratingAdj: data?.creditRating?.ratingAdj,
                  probabilityOfDefault: value,
                },
              },
              cancelToken.token
            )
              .then(({ creditRating: { probabilityOfDefaultAdj } }) => {
                if (!data?.creditRating?.probabilityOfDefaultAdj?.overriden) {
                  dispatch(updateProbabilityOfDefaultAdj({ value: probabilityOfDefaultAdj }));
                  showToast('Probabilities have been successfully updated.');
                } else {
                  showToast('Probability of default has been successfully updated.');
                }
              })
              .catch((err) => {
                if (axios.isCancel(err)) {
                } else {
                  if (err?.response?.status === 400) {
                    showErrorToast('Cannot update probability of default (est.).');
                  } else {
                    showErrorToast();
                  }
                }
                dispatch(updateIsProbabilityOfDefaultAdjLoading(false));
              });
          }, 1000)
        );
      }
    }
  }

  return (
    <Card p={6} title={isParent ? 'Consolidated Group Rating' : 'Issuer Rating'}>
      <FlexLayout disabled={!editMode} flexDirection="column" space={8}>
        <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(4, 1fr)' }}>
          <CreditRatingSingleSelect
            label={isParent ? 'Consolidated Group Rating' : 'Issuer Rating'}
            tooltip={tooltips.issuerRating}
            value={data?.creditRating?.rating}
            width="fullWidth"
            onChange={handleOnCreditRatingChange}
          />
          <NumberInput
            allowNegatives={false}
            inputType="float"
            label="Probability of default (1 yr.)"
            tooltip={tooltips.probabilityOfDefault}
            value={data?.creditRating?.probabilityOfDefault}
            unit="%"
            width="fullWidth"
            onChange={(value) => handleOnProbabilityOfDefaultChange(value)}
          />
        </Box>
        {renderAvailableCreditRatings && (
          <Table
            columns={getCreditRatingsColumns()}
            data={getCreditRatingsData(data?.availableCreditRatings, userInfo)}
            isSearchable={false}
          />
        )}
      </FlexLayout>
    </Card>
  );
}

export default CreditRatingCard;
