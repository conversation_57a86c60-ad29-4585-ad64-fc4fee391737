import { format } from 'date-fns';

import { creditRatings } from '~/components/Shared/CreditRatingSingleSelect';
import { displayNumber } from '~/utils/strings';
import { dateFnsFormatMapper } from '~/utils/dates';
import * as tableUtils from '~/utils/tables';

export function getCreditRatingsData(creditRatings = [], userInfo) {
  return creditRatings.map((item) => {
    const { id, closingDate, creditRating, probabilityOfDefault, createdAt } = item;

    const formatString = dateFnsFormatMapper(userInfo.dateFormat);

    return {
      id,
      fiscalYear: format(new Date(closingDate), formatString),
      rating: creditRating?.rating || 'n/a',
      probabilityOfDefault: `${displayNumber(probabilityOfDefault, userInfo.decimalPoint)}%`,
      createdAt: format(new Date(createdAt), formatString),
    };
  });
}

export function getCreditRatingsColumns() {
  const allCreditRatingVariations = [];

  creditRatings.forEach((item) => {
    const parts = item.split('/');
    allCreditRatingVariations.push(item);
    parts.forEach((part) => allCreditRatingVariations.push(part));
  });

  return [
    {
      label: 'Rating',
      sortBy: 'rating',
      sortType: 'array',
      sortArray: allCreditRatingVariations,
      value: 'rating',
    },
    {
      label: 'Fiscal Year',
      sortBy: 'fiscalYear',
      value: 'fiscalYear',
      sortingFn: tableUtils.dateSortingFunction,
    },
    {
      label: 'Probability of Default (1 yr.)',
      sortBy: 'probabilityOfDefault',
      value: 'probabilityOfDefault',
      sortingFn: tableUtils.percentageSortingFunction,
    },
    {
      label: 'Created',
      sortBy: 'createdAt',
      value: 'createdAt',
      sortingFn: tableUtils.dateSortingFunction,
    },
  ];
}

export const tooltips = {
  issuerRating:
    'Select the stand-alone issuer rating of the company.<br/>' +
    'This can be a rating calculated in this platform or one assigned by a credit rating agency.<br/>' +
    'The data applied to the company will be specific to the selected issuer rating ' +
    '(if a stand-alone pricing approach is chosen).',
  probabilityOfDefault:
    'Enter the one year probability of default associated with the issuer rating.<br/>' +
    'This can be the probability of default associated with a rating calculated in this platform or one calculated by a credit rating agency.<br/>' +
    'The probability of default is used in pricing intercompany guarantees.',
  closingDate: "Select the closing date of the company's financials that were used for the credit rating.",
};
