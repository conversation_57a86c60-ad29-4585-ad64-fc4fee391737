export const ringFencingOptionsArray = [
  {
    label: "The company's assets or profits are separated from the parent and other members of the group",
    value: 'ringFencing',
  },
];

export const parentalOptionsArray = [
  'The parent has the ability to provide support to the company',
  "The parent is legally bound to provide support to the company, for example through a guarantee agreement covering the company's obligations",
  "The parent has provided the company with a current letter of comfort or support covering the company's obligations",
  'The parent has provided support to the company in previous times of distress',
  'The parent is the ultimate owner of 75% or more of the company',
].map((option, index) => {
  return { label: option, value: `question${index + 1}` };
});

export const integrationOptionsArray = [
  'The company is closely related to the group by name or reputation',
  "The company functions as an integral part of the group's supply chain or holds important assets necessary for the group's operations",
  'The company operates in the same line of business as the group',
  "The company generates a significant share of the group's consolidated revenue",
  'The company has been operating as a member of the group for five years or longer',
].map((option, index) => {
  return { label: option, value: `question${parentalOptionsArray.length + index + 1}` };
});

export const tooltips = {
  implicitSupport: `Implicit support refers to the concept that the credit rating of a company that is part of a group<br />
    may be stronger than it would be if it was a stand-alone company.<br/><br/>
    The answers determine how much support the company could expect to receive from its parent.<br/>
    Depending on the outcome, the company's stand-alone issuer rating may be adjusted upwards<br />
    to arrive at the company's implicit support adjusted rating.<br/>
    The data applied to the company will be specific to the implicit support adjusted rating<br /> 
    (if an implicit support adjusted pricing approach is chosen).`,
  ringFencing:
    "Ring fencing relates specifically to regulatory mandated or structural separation of the company's assets or profits (e.g., Utilities, SPVs).",
  parentalCommitment: 'Assess the level of commitment the parent has towards the company.',
  levelOfIntegration: 'Assess the level of integration of the company within the greater group.',
  probabilityOfDefaultAdj: 'Estimated probability of default for the implicit support adjusted credit rating.',
};
