import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { createCompany } from '~/api';
import { NotesCard } from '~/components/Shared';
import { useCompanies, useUnsavedChangesWarning } from '~/hooks';
import { addCompany } from '~/reducers/companies.slice';
import {
  company,
  isCompanyFormValid,
  resetCompany,
  setCompany,
  setIsPristine,
  transform,
  updateField,
} from '~/reducers/company.slice';
import { routesEnum } from '~/routes';
import { Button, FlexLayout, LoadingSpinner, PageLayout } from '~/ui';
import { showToast, showErrorToast } from '~/ui/components/Toast';

import CompanyInfoCard from './CompanyInfoCard';
import CreditRatingCard from './CreditRatingCard';
import ImplicitSupportCard from './ImplicitSupportCard';

function CompanyCreatePage() {
  const companies = useCompanies();
  const dispatch = useDispatch();
  const history = useHistory();
  const data = useSelector(company);
  const [Prompt] = useUnsavedChangesWarning({ isDirty: data.isDirty });

  useEffect(() => {
    dispatch(setCompany());

    return () => {
      dispatch(resetCompany());
    };
  }, [dispatch]);

  function handleOnSubmitClick() {
    const parentCompany = companies.find((company) => company.id === company.parentCompanyId);
    const requestData = transform(data);

    const overriddenParentCompanyId = data.parentCompanyId;
    const parentCompanyId = parentCompany?.id; // The one marked with a dot in Group Information

    createCompany({ ...requestData, parentCompanyId: overriddenParentCompanyId ?? parentCompanyId ?? null })
      .then((res) => {
        dispatch(addCompany(res));
        dispatch(setIsPristine());
        showToast(`Company "${res.name}" has been successfully created.`);
        history.push(routesEnum.GROUP_INFORMATION);
      })
      .catch(() => showErrorToast());
  }

  const loading = !Object.keys(data).length;

  return loading ? (
    <LoadingSpinner />
  ) : (
    <>
      <PageLayout title="Group Information">
        <CompanyInfoCard />
        <CreditRatingCard />
        <ImplicitSupportCard />
        <NotesCard
          description="Include any notes on the company here."
          note={data.note}
          onChange={(value) => dispatch(updateField({ note: value }))}
        />
        <FlexLayout alignItems="flex-end" flexGrow="1" justifyContent="space-between">
          <Button text="Cancel" variant="gray" onClick={history.goBack} />
          <Button disabled={!isCompanyFormValid(data)} text="Submit" onClick={handleOnSubmitClick} />
        </FlexLayout>
      </PageLayout>
      {Prompt}
    </>
  );
}

export default CompanyCreatePage;
