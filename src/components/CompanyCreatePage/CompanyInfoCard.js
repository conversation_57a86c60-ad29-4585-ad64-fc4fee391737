import { useHistory } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import { CompanySingleSelect, CountrySingleSelect, IndustrySingleSelect } from '~/components/Shared';
import { useCompanies } from '~/hooks';
import { company, updateField } from '~/reducers/company.slice';
import { Box, Card, FlexLayout, Switch, TextInput } from '~/ui';
import { withLabel } from '~/ui/hocs';

import tooltips from './CompanyInfoCard.utils';

function CompanyInfoCard({ editMode = true }) {
  const companies = useCompanies(false);
  const dispatch = useDispatch();
  const history = useHistory();
  const isCreatingNew = history.location.pathname.includes('/new');
  const { country, id, industry, isParent, name, parentCompanyId } = useSelector(company);

  const isParentCompany = id === parentCompanyId;
  const currentParentCompany = companies.find((company) => company.id === company.parentCompanyId); // global parent company
  const parentCompanyExists = !!currentParentCompany;

  const SwitchWithLabel = withLabel(Switch);

  const getParentCompanyValue = () => {
    if (isCreatingNew) {
      if (parentCompanyId) return parentCompanyId;
      return currentParentCompany?.id;
    }

    return parentCompanyId;
  };

  return (
    <Card py={6}>
      <FlexLayout disabled={!editMode} flexDirection="column" space={8}>
        <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(4, 1fr)' }}>
          <TextInput
            label="Company Name"
            value={name}
            width="fullWidth"
            onChange={(name) => dispatch(updateField({ name }))}
          />
          <IndustrySingleSelect
            tooltip={tooltips.industry}
            value={industry}
            width="fullWidth"
            onChange={(industry) => dispatch(updateField({ industry }))}
          />
          <CountrySingleSelect
            tooltip={tooltips.country}
            value={country}
            width="fullWidth"
            onChange={(country) => dispatch(updateField({ country }))}
          />
          <FlexLayout space={2}>
            <SwitchWithLabel
              label="Parent Company"
              sx={{ width: '100%' }}
              isShowing={!parentCompanyExists || isParentCompany}
              isActive={isParent}
              onChange={(isParent) => dispatch(updateField({ isParent }))}
            />
            <CompanySingleSelect
              label="Parent company"
              tooltip={tooltips.parentCompany}
              sx={{ width: '100%' }}
              isShowing={!isParent && currentParentCompany?.id !== id}
              width="fullWidth"
              excludedValues={[id]}
              value={getParentCompanyValue()}
              onChange={(company) => dispatch(updateField({ parentCompanyId: company.id }))}
            />
          </FlexLayout>
        </Box>
      </FlexLayout>
    </Card>
  );
}

export default CompanyInfoCard;
