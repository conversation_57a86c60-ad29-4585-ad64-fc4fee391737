import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { updateImplicitSupport } from '~/api';
import { AssessmentGraph, CreditRatingSingleSelect, TextWithTooltip } from '~/components/Shared';
import { useCompanies } from '~/hooks';
import {
  company,
  updateAssessmentAnswers,
  updateAssessmentName,
  updateProbabilityOfDefaultAdj,
  updateRatingAdj,
} from '~/reducers/company.slice';
import { Box, Button, CheckboxGroup, CollapsibleCard, FlexLayout, NumberInput, WithTooltip } from '~/ui';
import { showErrorToast, showToast } from '~/ui/components/Toast';

import {
  integrationOptionsArray,
  parentalOptionsArray,
  ringFencingOptionsArray,
  tooltips,
} from './ImplicitSupportCard.utils';

function ImplicitSupportCard({ editMode = true }) {
  const companies = useCompanies();
  const dispatch = useDispatch();
  const { assessment, creditRating, id, parentCompanyId } = useSelector(company);
  const [collapsed, setCollapsed] = useState(true);

  const defaultAssessmentAnswers = {
    ringFencing: false,
    question1: false,
    question2: false,
    question3: false,
    question4: false,
    question5: false,
    question6: false,
    question7: false,
    question8: false,
    question9: false,
    question10: false,
  };

  function handleOnResetClick() {
    dispatch(updateAssessmentAnswers(defaultAssessmentAnswers));
  }

  function handleOnSaveClick() {
    const parentCompany = companies.find((company) =>
      parentCompanyId ? company.id === parentCompanyId : company.id === company.parentCompanyId
    );

    updateImplicitSupport({
      answers: assessment?.answers,
      creditRating: { rating: creditRating?.rating, probabilityOfDefault: creditRating?.probabilityOfDefault },
      parentCompanyId: parentCompany.id,
    })
      .then((res) => {
        const {
          assessment: { name },
          creditRating: { ratingAdj, probabilityOfDefaultAdj },
        } = res;

        dispatch(updateAssessmentName(name));
        dispatch(updateRatingAdj(ratingAdj));
        if (probabilityOfDefaultAdj != null) {
          dispatch(updateProbabilityOfDefaultAdj({ value: probabilityOfDefaultAdj, overriden: false }));
        }
        setCollapsed(true);
        showToast('Implicit support has been successfully updated.');
      })
      .catch(() => showErrorToast());
  }

  function updateAssessmentField(values) {
    const updatedAssessmentAnswers = assessment?.answers ? { ...assessment.answers } : defaultAssessmentAnswers;

    Object.entries(updatedAssessmentAnswers).forEach(([key, _value]) => {
      updatedAssessmentAnswers[key] = values.includes(key);
    });

    dispatch(updateAssessmentAnswers(updatedAssessmentAnswers));
  }

  function getAssessmentAnswersValues() {
    return assessment?.answers
      ? Object.entries(assessment.answers)
          .filter(([_key, value]) => !!value)
          .map(([key, _value]) => key)
      : [];
  }

  const parentCompanyExists = !!companies.find((company) => company.id === company.parentCompanyId);
  const isParentCompany = id === parentCompanyId;
  if (!parentCompanyExists || isParentCompany) {
    return null;
  }

  function QuestionGroup({ label, options = [], tooltip, values = [] }) {
    return (
      <FlexLayout flexDirection="column" flexGrow="1" space={6}>
        <TextWithTooltip color="shakespeare" label={label} tooltip={tooltip} variant="l-spaced-medium" />
        <CheckboxGroup options={options} values={values} onChange={updateAssessmentField} />
      </FlexLayout>
    );
  }

  return (
    <CollapsibleCard
      collapsed={collapsed}
      description="Answer the following questions to assess the level of implicit support this company would be expected to receive."
      subtitle={
        assessment ? (
          <>
            <AssessmentGraph assessment={assessment} />
            {creditRating?.rating && creditRating?.ratingAdj ? (
              <Box disabled={!editMode} sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(4, 1fr)' }}>
                <CreditRatingSingleSelect
                  disabled
                  label="Implicit Support Adjusted Rating"
                  width="fullWidth"
                  value={creditRating.ratingAdj}
                  onChange={() => {}}
                />
                <NumberInput
                  allowNegatives={false}
                  inputType="float"
                  isLoading={creditRating?.probabilityOfDefaultAdj?.loading}
                  label="Probability of default (1 yr.)"
                  tooltip={tooltips.probabilityOfDefaultAdj}
                  value={creditRating?.probabilityOfDefaultAdj?.value}
                  unit="%"
                  width="fullWidth"
                  withLock
                  onChange={(value) => dispatch(updateProbabilityOfDefaultAdj({ value, overriden: true }))}
                />
              </Box>
            ) : null}
          </>
        ) : null
      }
      title="Implicit Support"
      tooltip={tooltips.implicitSupport}
      onCollapseChange={() => setCollapsed(!collapsed)}
    >
      <FlexLayout flexDirection="column" disabled={!editMode} space={8}>
        <FlexLayout alignItems="flex-start" space={6}>
          <FlexLayout flexDirection="column" flexGrow="1" space={8} sx={{ flexBasis: '0' }}>
            <QuestionGroup
              label="Ring Fencing"
              options={ringFencingOptionsArray}
              tooltip={tooltips.ringFencing}
              values={getAssessmentAnswersValues()}
            />
            <QuestionGroup
              label="Parental Commitment"
              options={parentalOptionsArray}
              tooltip={tooltips.parentalCommitment}
              values={getAssessmentAnswersValues()}
            />
          </FlexLayout>
          <FlexLayout flexDirection="column" flexGrow="1" space={8} sx={{ flexBasis: '0' }}>
            <QuestionGroup
              label="Level of Integration"
              options={integrationOptionsArray}
              tooltip={tooltips.levelOfIntegration}
              values={getAssessmentAnswersValues()}
            />
          </FlexLayout>
        </FlexLayout>
        {editMode && (
          <FlexLayout alignItems="flex-end" flexGrow="1" justifyContent="space-between">
            <Button disabled={!assessment} text="Reset" variant="gray" onClick={handleOnResetClick} />
            <WithTooltip
              disabled={!assessment || !creditRating?.rating}
              label="calculateImplicitSupport"
              tooltip="Issuer Rating must be selected before calculating implicit support."
            >
              <Button
                disabled={!assessment || !creditRating?.rating}
                text="Calculate"
                variant="secondary"
                onClick={handleOnSaveClick}
              />
            </WithTooltip>
          </FlexLayout>
        )}
      </FlexLayout>
    </CollapsibleCard>
  );
}

export default ImplicitSupportCard;
