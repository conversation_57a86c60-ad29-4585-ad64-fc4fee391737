import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import { updateCreditRating } from '~/api';
import {
  creditRating,
  isCreditRatingFormValid,
  setIsPristine,
  transform,
  updateField,
} from '~/reducers/creditRating.slice';
import { reportEnum } from '~/enums';
import { Button, FlexLayout } from '~/ui';
import { showToast, showErrorToast } from '~/ui/components/Toast';

import CreditRatingForm from '../CreditRatingPage/CreditRatingForm';

function CreditRatingEdit() {
  const dispatch = useDispatch();
  const data = useSelector(creditRating);
  const history = useHistory();
  const { reportId } = useParams();
  const [isSubmitting, setIsSubmitting] = useState(false);

  function onItemUpdate() {
    const errors = isCreditRatingFormValid(data);
    if (Object.keys(errors).length) {
      dispatch(updateField({ showErrors: true, errors: errors }));
    } else {
      setIsSubmitting(true);
      const requestData = transform(data);
      updateCreditRating({ id: reportId, data: requestData })
        .then(() => {
          dispatch(setIsPristine());
          showToast('Credit rating has been successfully updated.');
          history.replace(`${history.location.pathname.split('/edit')[0]}?reportType=${reportEnum.CREDIT_RATING}`);
        })
        .catch((err) => {
          if (err?.response?.status >= 500) return showErrorToast();
          showErrorToast(err?.response?.data?.message);
          setIsSubmitting(false);
        });
    }
  }

  return (
    <CreditRatingForm
      actionButtons={
        <FlexLayout alignItems="flex-end" flexGrow="1" justifyContent="space-between">
          <Button text="Cancel" variant="gray" onClick={history.goBack} />
          <Button
            disabled={data?.showErrors && Object.keys(data?.errors).length !== 0}
            loading={isSubmitting}
            text="Update"
            onClick={isSubmitting ? null : onItemUpdate}
          />
        </FlexLayout>
      }
    />
  );
}

export default CreditRatingEdit;
