import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import {
  getCreditRating,
  getGuarantee,
  getLoan,
  updateImportedCreditRating,
  updateImportedGuarantee,
  updateImportedLoan,
} from '~/api';
import CreditRatingForm from '~/components/ImportedReportCreatePage/CreditRatingForm';
import { REPORT_TYPE, reportEnum } from '~/enums';
import { useQuery, useUnsavedChangesWarning } from '~/hooks';
import { importedReportForm, isFormValid, setIsPristine } from '~/reducers/importedReportForm.slice';
import { Button, Card, FlexLayout, PageLayout } from '~/ui';
import { showToast, showErrorToast } from '~/ui/components/Toast';
import { capitalize } from '~/utils/strings';

import GuaranteeForm from '../ImportedReportCreatePage/GuaranteeForm';
import LoanForm from '../ImportedReportCreatePage/LoanForm';

function ImportedReportEdit() {
  const dispatch = useDispatch();
  const history = useHistory();
  const query = useQuery();
  const { reportId } = useParams();
  const formData = useSelector(importedReportForm);
  const [Prompt] = useUnsavedChangesWarning({ isDirty: formData.isDirty });

  const reportType = query.get(REPORT_TYPE) || reportEnum.LOAN;

  const apis = {
    [reportEnum.LOAN]: {
      get: getLoan,
      update: updateImportedLoan,
    },
    [reportEnum.GUARANTEE]: {
      get: getGuarantee,
      update: updateImportedGuarantee,
    },
    [reportEnum.CREDIT_RATING]: {
      get: getCreditRating,
      update: updateImportedCreditRating,
    },
  };

  function onItemUpdate() {
    const updateReport = apis[reportType]?.update;

    const {
      reportType: typeOfReport,
      currentCreditRating,
      currentGuarantorRating,
      currentPrincipalRating,
      isDirty,
      ...data
    } = formData;
    updateReport({ id: reportId, data })
      .then(() => {
        dispatch(setIsPristine());
        showToast(`${capitalize(typeOfReport)} has been successfully updated.`);
        history.replace(`${history.location.pathname.split('/edit')[0]}?reportType=${reportType}`);
      })
      .catch((err) => {
        if (err?.response?.status >= 500) return showErrorToast();
        showErrorToast(err?.response?.data?.message);
      });
  }

  return (
    <>
      <PageLayout title={capitalize(reportType)}>
        <Card pb={3} pt={6}>
          {formData.reportType === reportEnum.LOAN && <LoanForm />}
          {formData.reportType === reportEnum.GUARANTEE && <GuaranteeForm />}
          {formData.reportType === reportEnum.CREDIT_RATING && <CreditRatingForm />}
        </Card>
        <FlexLayout alignItems="flex-end" flexGrow="1" justifyContent="space-between">
          <Button text="Cancel" variant="gray" onClick={history.goBack} />
          <Button disabled={!isFormValid(formData)} text="Update" onClick={onItemUpdate} />
        </FlexLayout>
      </PageLayout>
      {Prompt}
    </>
  );
}

export default ImportedReportEdit;
