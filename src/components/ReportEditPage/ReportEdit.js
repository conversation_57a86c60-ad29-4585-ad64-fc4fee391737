import { unwrapResult } from '@reduxjs/toolkit';
import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import { PricingApproachModal } from '~/components/Modals';
import { getPricingOptions } from '~/components/PriceAndBenchmarkPage/PriceAndBenchmarkPage.utils';
import { REPORT_TYPE, reportEnum } from '~/enums';
import { useQuery, useUnsavedChangesWarning } from '~/hooks';
import {
  isReportFormValid,
  report,
  setIsPristine,
  setIsSubmitting,
  updateField,
  updateReportThunk,
  resetReport,
} from '~/reducers/report.slice';
import { Button, Card, FlexLayout, PageLayout } from '~/ui';
import { showErrorToast, showToast } from '~/ui/components/Toast';
import { areRublesDiscontinued } from '~/utils/report';
import { capitalize } from '~/utils/strings';
import { deleteGuaranteeFile, deleteLoanFile, deleteB2BLoanFile } from '~/api';
import { getAgreements } from '~/components/ReportViewPage/ReportView.utils';

import GuaranteeForm from '../PriceAndBenchmarkPage/GuaranteeForm';
import LoanForm from '../PriceAndBenchmarkPage/LoanForm';
import BackToBackLoanForm from '../PriceAndBenchmarkPage/BackToBackLoanForm';

function ReportEdit() {
  const dispatch = useDispatch();
  const history = useHistory();
  const query = useQuery();
  const { reportId } = useParams();
  const data = useSelector(report);
  const [showModal, setShowModal] = useState(false);
  const [pricingOptions, setPricingOptions] = useState([]);
  const [Prompt] = useUnsavedChangesWarning({ isDirty: data.isDirty });
  const reportType = query.get(REPORT_TYPE) || reportEnum.LOAN;
  const isLoan = reportType === reportEnum.LOAN;
  const isGuarantee = reportType === reportEnum.GUARANTEE;
  const isBackToBackLoan = reportType === reportEnum.BACK_TO_BACK_LOAN;

  function handleOnItemUpdate() {
    const deleteFileApiMethodMapper = {
      [reportEnum.LOAN]: deleteLoanFile,
      [reportEnum.BACK_TO_BACK_LOAN]: deleteB2BLoanFile,
      [reportEnum.GUARANTEE]: deleteGuaranteeFile,
    };

    if (areRublesDiscontinued({ date: data.issueDate, currency: data.currency })) {
      return showErrorToast('RUB data is discontinued from 15 March 2022');
    }
    dispatch(updateReportThunk({ id: reportId, data, reportType }))
      .then(unwrapResult)
      .then(async () => {
        dispatch(setIsPristine());
        showToast(`${capitalize(reportType)} has been successfully updated.`);
        setShowModal(false);
        dispatch(resetReport());
        // for B2BLoans agreements are deleted on update so the below only applies to loans and guarantees
        if (!isBackToBackLoan) {
          const [agreement] = getAgreements(data.files);
          if (agreement) {
            const deleteFile = deleteFileApiMethodMapper[reportType];
            await deleteFile({ fileId: agreement.id, reportId }).catch(() => {});
          }
        }
        history.replace(`${history.location.pathname.split('/edit')[0]}?reportType=${reportType}`);
      })
      // Error handled in thunk
      .catch(() => {})
      .finally(() => dispatch(setIsSubmitting(false)));
  }

  function handleShowModal() {
    if (data.reportType === reportEnum.BACK_TO_BACK_LOAN) {
      return handleOnItemUpdate();
    }
    const { pricingOptions } = getPricingOptions(data);
    setPricingOptions(pricingOptions);
    setShowModal(true);
  }

  return (
    <>
      <PageLayout title="Report">
        <Card pb={3} pt={6}>
          {isLoan && <LoanForm />}
          {isGuarantee && <GuaranteeForm />}
          {isBackToBackLoan && <BackToBackLoanForm />}
        </Card>
        <FlexLayout alignItems="flex-end" flexGrow="1" justifyContent="space-between">
          <Button text="Cancel" variant="gray" onClick={history.goBack} />
          <Button disabled={!isReportFormValid(data)} text="Update" onClick={handleShowModal} />
        </FlexLayout>
      </PageLayout>
      {Prompt}
      <PricingApproachModal
        filterNonStandardValues={isLoan}
        pricingOptions={pricingOptions}
        onHide={() => setShowModal(false)}
        onSubmitClick={handleOnItemUpdate}
        loading={data.isSubmitting}
        isShowing={showModal}
        data={data}
        updateField={updateField}
        isLoan={isLoan}
        isGuarantee={isGuarantee}
        checkCompaniesData
      />
    </>
  );
}

export default ReportEdit;
