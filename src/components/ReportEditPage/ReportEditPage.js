import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';
import _ from 'lodash';

import { getCreditRating, getGuarantee, getLoan, getB2BLoan } from '~/api';
import { REPORT_TYPE, reportEnum } from '~/enums';
import { useCompanies, useQuery } from '~/hooks';
import { setCreditRating } from '~/reducers/creditRating.slice';
import { setForm } from '~/reducers/importedReportForm.slice';
import { setReport } from '~/reducers/report.slice';
import { routesEnum } from '~/routes';
import { LoadingSpinner } from '~/ui';
import { checkIsImported } from '~/utils/report';

import CreditRatingEdit from './CreditRatingEdit';
import ImportedReportEdit from './ImportedReportEdit';
import ReportEdit from './ReportEdit';

const EditForm = ({ isLoading, isImportedState, reportType }) => {
  if (isLoading) return <LoadingSpinner />;

  if (isImportedState) return <ImportedReportEdit />;

  if (reportType === reportEnum.CREDIT_RATING) return <CreditRatingEdit />;
  else return <ReportEdit />;
};

function ReportEditPage() {
  const dispatch = useDispatch();
  const history = useHistory();
  const query = useQuery();
  const { reportId } = useParams();
  const companies = useCompanies(); // refresh company list
  const [isLoading, setIsLoading] = useState(true);
  const [isImportedState, setIsImportedState] = useState(true);

  const reportType = query.get(REPORT_TYPE) || reportEnum.LOAN;

  useEffect(() => {
    (async () => {
      const getDataAndFormReducer = async (isCreditRatingReport) => {
        if (isCreditRatingReport) return [await getCreditRating(reportId), setCreditRating];

        const getReportApiMethodMapper = {
          [reportEnum.LOAN]: getLoan,
          [reportEnum.BACK_TO_BACK_LOAN]: getB2BLoan,
          [reportEnum.GUARANTEE]: getGuarantee,
        };
        const getReport = getReportApiMethodMapper[reportType];
        return [await getReport(reportId), setReport];
      };

      const setFormValues = (isImported, data, formReducer) => {
        if (isImported) dispatch(setForm(data));
        else dispatch(formReducer({ ...data, isRenumerationSectionShown: !_.isEmpty(data.standardRemuneration) }));
      };

      try {
        if (companies.length !== 0) {
          const isCreditRatingReport = reportType === reportEnum.CREDIT_RATING;
          const [res, formReducer] = await getDataAndFormReducer(isCreditRatingReport);
          const isImported = checkIsImported(res.report, res.attributes, isCreditRatingReport);
          setIsImportedState(isImported);
          setFormValues(isImported, { ...res, reportType, companies }, formReducer);
        }
      } catch (err) {
        history.push(routesEnum.ANALYSES);
      } finally {
        setIsLoading(false);
      }
    })();
  }, [dispatch, history, reportId, reportType, companies]);

  return <EditForm isLoading={isLoading} isImportedState={isImportedState} reportType={reportType} />;
}

export default ReportEditPage;
