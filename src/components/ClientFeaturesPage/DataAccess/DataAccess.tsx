import { useDispatch, useSelector } from 'react-redux';

import { Card, FlexLayout, Text } from 'ui';
import { CurrencyMultiSelect, QuestionGroup } from 'components/Shared';
import { clientFeatureSelector, updateField } from 'reducers/clientFeature.slice';
import { GeographyDataType } from 'types';

import BoxTitle from '../BoxTitle';
import GeographyDataSingleSelect from '../GeographyDataSingleSelect';

type DataAccessPropsType = {
  isClientView: boolean;
  options: any;
  updateFeatureAvailabilityFields: (values: string[]) => void;
  getFeatureAnswerValues: () => string[];
};

const DataAccess = ({
  isClientView,
  options,
  updateFeatureAvailabilityFields,
  getFeatureAnswerValues,
}: DataAccessPropsType) => {
  const dispatch = useDispatch();
  const { currency, geographyData, errors } = useSelector(clientFeatureSelector);

  return (
    <Card pb={6} pt={6} title={<BoxTitle title="DATA ACCESS" />}>
      <FlexLayout flexDirection="column" space={6} disabled={isClientView}>
        <QuestionGroup
          options={options.currency}
          values={getFeatureAnswerValues()}
          updateFields={updateFeatureAvailabilityFields}
        />
        <Text variant="s-spaced" color="bali-hai" sx={{ marginTop: '-8px' }}>
          Currencies available when pricing loans and guarantees
        </Text>
        <CurrencyMultiSelect
          error={errors.currencyError}
          includeSelectAll
          onChange={(currencies: string[]) => dispatch(updateField({ currency: { currencies } }))}
          value={currency?.currencies}
        />
        <QuestionGroup
          options={options.geographyData}
          values={getFeatureAnswerValues()}
          updateFields={updateFeatureAvailabilityFields}
        />
        <Text variant="s-spaced" color="bali-hai" sx={{ marginTop: '-8px' }}>
          Country level (full) or regional level (basic) geographic data access
        </Text>
        <GeographyDataSingleSelect
          value={geographyData}
          onChange={(geographyData: GeographyDataType) => dispatch(updateField({ geographyData }))}
        />
        {!isClientView && (
          <>
            <QuestionGroup
              options={options.isTemplateCashPoolBatchUpload}
              values={getFeatureAnswerValues()}
              updateFields={updateFeatureAvailabilityFields}
            />
            <Text variant="s-spaced" color="bali-hai" sx={{ marginTop: '-8px' }}>
              Should be enabled for clients that use templates to upload cash pool batch data
            </Text>
          </>
        )}
      </FlexLayout>
    </Card>
  );
};

export default DataAccess;
