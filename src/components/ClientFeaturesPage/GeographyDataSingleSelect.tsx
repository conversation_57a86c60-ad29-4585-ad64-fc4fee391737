import { SingleSelect } from 'ui';
import type { GeographyDataType, SingleSelectWidthType } from 'types';

const options = [
  { label: 'Basic', value: 'basic' },
  { label: 'Full', value: 'full' },
];

type GeographyDataSingleSelectProps = {
  label?: string;
  width?: SingleSelectWidthType;
  tooltip?: string;
  disabled?: boolean;
  value: GeographyDataType;
  onChange: Function;
};

function GeographyDataSingleSelect({
  label = 'Geography Data',
  width = 'm',
  tooltip,
  disabled,
  value,
  onChange,
}: GeographyDataSingleSelectProps) {
  return (
    <SingleSelect
      label={label}
      options={options}
      tooltip={tooltip}
      value={value}
      width={width}
      disabled={disabled}
      onChange={onChange}
    />
  );
}

export default GeographyDataSingleSelect;
