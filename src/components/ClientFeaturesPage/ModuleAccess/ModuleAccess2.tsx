import { Card, FlexLayout, Text } from 'ui';
import { QuestionGroup } from 'components/Shared';

import BoxTitle from '../BoxTitle';

type ModuleAccess2PropsType = {
  isClientView: boolean;
  options: any;
  updateFeatureAvailabilityFields: (values: string[]) => void;
  getFeatureAnswerValues: () => string[];
};

const ModuleAccess2 = ({
  isClientView,
  options,
  updateFeatureAvailabilityFields,
  getFeatureAnswerValues,
}: ModuleAccess2PropsType) => {
  return (
    <Card pb={6} pt={6} title={<BoxTitle title="MODULE ACCESS" />}>
      <FlexLayout flexDirection="column" space={6} disabled={isClientView}>
        <QuestionGroup
          options={options.loan}
          values={getFeatureAnswerValues()}
          updateFields={updateFeatureAvailabilityFields}
        />
        <Text variant="s-spaced" color="bali-hai" sx={{ marginTop: '-8px' }}>
          Pricing, monitoring and documentation of intercompany loans based on OECD guidance.
        </Text>
        <QuestionGroup
          options={options.backToBackLoan}
          values={getFeatureAnswerValues()}
          updateFields={updateFeatureAvailabilityFields}
        />
        <Text variant="s-spaced" color="bali-hai" sx={{ marginTop: '-8px' }}>
          Pricing, monitoring and documentation of intercompany back-to-back loans based on OECD guidance.
        </Text>
        <QuestionGroup
          options={options.guarantee}
          values={getFeatureAnswerValues()}
          updateFields={updateFeatureAvailabilityFields}
        />
        <Text variant="s-spaced" color="bali-hai" sx={{ marginTop: '-8px' }}>
          Pricing, monitoring and documentation of intercompany guarantees based on OECD guidance.
        </Text>
      </FlexLayout>
    </Card>
  );
};

export default ModuleAccess2;
