import { useDispatch, useSelector } from 'react-redux';

import { Box, FlexLayout, NumberInput } from 'ui';
import { QuestionGroup } from 'components/Shared';
import { clientFeatureSelector, updateField } from 'reducers/clientFeature.slice';

type ClientFeaturesInputLimitsPropsType = {
  isClientView: boolean;
  options: any;
  updateFeatureAvailabilityFields: (values: string[]) => void;
  getFeatureAnswerValues: () => string[];
};

const ClientFeaturesInputLimits = ({
  isClientView,
  options,
  updateFeatureAvailabilityFields,
  getFeatureAnswerValues,
}: ClientFeaturesInputLimitsPropsType) => {
  const dispatch = useDispatch();
  const {
    loanNumber,
    guaranteeNumber,
    backToBackLoanNumber,
    loanGuaranteeNumber,
    userNumber,
    cashPoolNumber,
    creditRatingNumber,
    errors,
  } = useSelector(clientFeatureSelector);

  if (!isClientView) return null;

  return (
    <>
      <Box sx={{ display: 'grid', gridGap: 6, gridTemplateColumns: 'repeat(2, 1fr)' }}>
        <FlexLayout flexDirection="column" space={4}>
          <QuestionGroup
            options={options.userNumber}
            values={getFeatureAnswerValues()}
            updateFields={updateFeatureAvailabilityFields}
          />
          <NumberInput
            width="fullWidth"
            allowNegatives={false}
            error={errors.userNumberError}
            value={userNumber}
            onChange={(userNumber: number) => dispatch(updateField({ userNumber }))}
          />
        </FlexLayout>
        <FlexLayout flexDirection="column" space={4}>
          <QuestionGroup
            options={options.loanGuaranteeNumber}
            values={getFeatureAnswerValues()}
            updateFields={updateFeatureAvailabilityFields}
          />
          <NumberInput
            width="fullWidth"
            allowNegatives={false}
            error={errors.loanGuaranteeNumberError}
            value={loanGuaranteeNumber}
            onChange={(loanGuaranteeNumber: number) => dispatch(updateField({ loanGuaranteeNumber }))}
          />
        </FlexLayout>
      </Box>

      <Box sx={{ display: 'grid', gridGap: 6, gridTemplateColumns: 'repeat(2, 1fr)' }}>
        <FlexLayout flexDirection="column" space={4}>
          <QuestionGroup
            options={options.loanNumber}
            values={getFeatureAnswerValues()}
            updateFields={updateFeatureAvailabilityFields}
          />
          <NumberInput
            width="fullWidth"
            allowNegatives={false}
            error={errors.loanNumberError}
            value={loanNumber}
            onChange={(loanNumber: number) => dispatch(updateField({ loanNumber }))}
          />
        </FlexLayout>
        <FlexLayout flexDirection="column" space={4}>
          <QuestionGroup
            options={options.guaranteeNumber}
            values={getFeatureAnswerValues()}
            updateFields={updateFeatureAvailabilityFields}
          />
          <NumberInput
            width="fullWidth"
            allowNegatives={false}
            error={errors.guaranteeNumberError}
            value={guaranteeNumber}
            onChange={(guaranteeNumber: number) => dispatch(updateField({ guaranteeNumber }))}
          />
        </FlexLayout>
      </Box>

      <Box sx={{ display: 'grid', gridGap: 6, gridTemplateColumns: 'repeat(2, 1fr)' }}>
        <FlexLayout flexDirection="column" space={4}>
          <QuestionGroup
            options={options.backToBackLoanNumber}
            values={getFeatureAnswerValues()}
            updateFields={updateFeatureAvailabilityFields}
          />
          <NumberInput
            width="fullWidth"
            allowNegatives={false}
            error={errors.backToBackLoanNumberError}
            value={backToBackLoanNumber}
            onChange={(backToBackLoanNumber: number) => dispatch(updateField({ backToBackLoanNumber }))}
          />
        </FlexLayout>
      </Box>

      <Box sx={{ display: 'grid', gridGap: 6, gridTemplateColumns: 'repeat(2, 1fr)' }}>
        <FlexLayout flexDirection="column" space={4}>
          <QuestionGroup
            options={options.creditRatingNumber}
            values={getFeatureAnswerValues()}
            updateFields={updateFeatureAvailabilityFields}
          />
          <NumberInput
            width="fullWidth"
            allowNegatives={false}
            error={errors.creditRatingNumberError}
            value={creditRatingNumber}
            onChange={(creditRatingNumber: number) => dispatch(updateField({ creditRatingNumber }))}
          />
        </FlexLayout>
        <FlexLayout flexDirection="column" space={4}>
          <QuestionGroup
            options={options.cashPoolNumber}
            values={getFeatureAnswerValues()}
            updateFields={updateFeatureAvailabilityFields}
          />
          <NumberInput
            width="fullWidth"
            allowNegatives={false}
            error={errors.cashPoolNumberError}
            value={cashPoolNumber}
            onChange={(cashPoolNumber: number) => dispatch(updateField({ cashPoolNumber }))}
          />
        </FlexLayout>
      </Box>
    </>
  );
};

export default ClientFeaturesInputLimits;
