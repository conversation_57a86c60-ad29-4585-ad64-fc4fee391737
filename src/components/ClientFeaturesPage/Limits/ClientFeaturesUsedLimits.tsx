import ReactTooltip from 'react-tooltip';

import { FlexLayout, Text, Icon } from 'ui';
import { useAppSelector } from 'hooks';
import { clientFeatureSelector } from 'reducers/clientFeature.slice';
import type { ClientFeatureUsedNumbersType } from 'types';

type ClientFeatureUsedLimitPropsType = {
  usedNumbers: ClientFeatureUsedNumbersType;
  isClientView: boolean;
};

const ClientFeatureUsedLimit = ({ usedNumbers, isClientView }: ClientFeatureUsedLimitPropsType) => {
  const {
    featureAvailability,
    loanNumber,
    guaranteeNumber,
    backToBackLoanNumber,
    loanGuaranteeNumber,
    userNumber,
    cashPoolNumber,
    creditRatingNumber,
  } = useAppSelector(clientFeatureSelector);
  const UNLIMITED = 'unlimited';

  if (!isClientView) return null;

  return (
    <FlexLayout flexDirection="column" space={4}>
      <Text color="bali-hai">
        <Text variant="m-spaced-bold">Users used:</Text> {usedNumbers.usedUsersNumber} of{' '}
        {featureAvailability.userNumber ? userNumber : UNLIMITED}
      </Text>
      {(featureAvailability.loan || featureAvailability.guarantee || featureAvailability.backToBackLoan) && (
        <FlexLayout alignItems="center" space={2}>
          <Text color="bali-hai" variant="m-spaced-bold">
            Analyses used{' '}
            <Text>
              {`${
                usedNumbers.usedLoansNumber + usedNumbers.usedGuaranteesNumber + usedNumbers.usedBackToBackLoansNumber
              } of ${featureAvailability.loanGuaranteeNumber ? loanGuaranteeNumber : UNLIMITED}`}
            </Text>
          </Text>
          <Icon data-for="analysesUsedTooltipId" data-tip="" icon="helpTooltip" size="xs" />
          <ReactTooltip html={true} id="analysesUsedTooltipId">
            {
              'Limit on total number of loans and guarantees. <br /> For example, a limit of 20 can be reached with 10 loans and 10 guarantees, 20 loans, or 20 guarantees.'
            }
          </ReactTooltip>
        </FlexLayout>
      )}
      {featureAvailability.loan && (
        <Text color="bali-hai" px={4}>
          <Text variant="m-spaced-bold">Loans used:</Text> {usedNumbers.usedLoansNumber} of{' '}
          {featureAvailability.loanNumber ? loanNumber : UNLIMITED}
        </Text>
      )}
      {featureAvailability.backToBackLoan && (
        <Text color="bali-hai" px={4}>
          <Text variant="m-spaced-bold">Back-to-Back Loans used:</Text> {usedNumbers.usedBackToBackLoansNumber} of{' '}
          {featureAvailability.backToBackLoanNumber ? backToBackLoanNumber : UNLIMITED}
        </Text>
      )}
      {featureAvailability.guarantee && (
        <Text color="bali-hai" px={4}>
          <Text variant="m-spaced-bold">Guarantees used:</Text> {usedNumbers.usedGuaranteesNumber} of{' '}
          {featureAvailability.guaranteeNumber ? guaranteeNumber : UNLIMITED}
        </Text>
      )}
      {featureAvailability.creditRating && (
        <Text color="bali-hai">
          <Text variant="m-spaced-bold">Credit Ratings used:</Text> {usedNumbers.usedCreditRatingsNumber} of{' '}
          {featureAvailability.creditRatingNumber ? creditRatingNumber : UNLIMITED}
        </Text>
      )}
      {featureAvailability.cashPool && (
        <Text color="bali-hai">
          <Text variant="m-spaced-bold">Cash pools used:</Text> {usedNumbers.usedCashPoolsNumber} of{' '}
          {featureAvailability.cashPoolNumber ? cashPoolNumber : UNLIMITED}
        </Text>
      )}
    </FlexLayout>
  );
};

export default ClientFeatureUsedLimit;
