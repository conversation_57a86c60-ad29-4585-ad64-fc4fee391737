import { Card, FlexLayout } from 'ui';
import { ClientFeatureUsedNumbersType } from 'types';

import ClientFeaturesUsedLimits from './ClientFeaturesUsedLimits';
import ClientFeaturesInputLimits from './ClientFeaturesInputLimits';
import BoxTitle from '../BoxTitle';

type LimitsPropsType = {
  isClientView: boolean;
  options: any;
  updateFeatureAvailabilityFields: (values: string[]) => void;
  getFeatureAnswerValues: () => string[];
  usedNumbers: ClientFeatureUsedNumbersType;
};

const Limits = ({
  isClientView,
  options,
  updateFeatureAvailabilityFields,
  getFeatureAnswerValues,
  usedNumbers,
}: LimitsPropsType) => {
  return (
    <Card pb={6} pt={6} title={<BoxTitle title="LIMITS" />}>
      <FlexLayout flexDirection="column" space={6}>
        <ClientFeaturesUsedLimits usedNumbers={usedNumbers} isClientView={isClientView} />
        <ClientFeaturesInputLimits
          isClientView={!isClientView}
          options={options}
          updateFeatureAvailabilityFields={updateFeatureAvailabilityFields}
          getFeatureAnswerValues={getFeatureAnswerValues}
        />
      </FlexLayout>
    </Card>
  );
};

export default Limits;
