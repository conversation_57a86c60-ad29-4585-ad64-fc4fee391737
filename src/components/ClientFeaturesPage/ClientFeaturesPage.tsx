import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useParams } from 'react-router-dom';

import { getClient, updateClientFeatures } from 'api';
import { useAppSelector } from 'hooks';
import { Button, FlexLayout, PageLayout } from 'ui';
import { clientFeatureSelector, updateError, resetErrors } from 'reducers/clientFeature.slice';
import { showToast } from 'ui/components/Toast';
import { errorHandler } from 'utils/errors';

import ClientFeaturesComponent from './ClientFeaturesComponent';

const ClientFeaturePage = () => {
  const dispatch = useDispatch();
  const { clientId } = useParams<{ clientId: string }>();
  const {
    featureAvailability,
    loanNumber,
    guaranteeNumber,
    backToBackLoanNumber,
    loanGuaranteeNumber,
    userNumber,
    cashPoolNumber,
    creditRatingNumber,
    geographyData,
    currency,
  } = useAppSelector(clientFeatureSelector);
  const [clientName, setClientName] = useState('');

  const validateFields = () => {
    dispatch(resetErrors());
    let hasErrors = false;
    const numberErrorMessage = 'Enter a number.';

    if (currency?.currencies.length === 0) {
      dispatch(updateError({ currencyError: 'Select at least one currency.' }));
      hasErrors = true;
    }
    if (!userNumber) {
      dispatch(updateError({ userNumberError: numberErrorMessage }));
      hasErrors = true;
    }
    if (!loanGuaranteeNumber) {
      dispatch(updateError({ loanGuaranteeNumberError: numberErrorMessage }));
      hasErrors = true;
    }
    if (!loanNumber) {
      dispatch(updateError({ loanNumberError: numberErrorMessage }));
      hasErrors = true;
    }
    if (!backToBackLoanNumber) {
      dispatch(updateError({ backToBackLoanNumberError: numberErrorMessage }));
      hasErrors = true;
    }
    if (!guaranteeNumber) {
      dispatch(updateError({ guaranteeNumberError: numberErrorMessage }));
      hasErrors = true;
    }
    if (!creditRatingNumber) {
      dispatch(updateError({ creditRatingNumberError: numberErrorMessage }));
      hasErrors = true;
    }
    if (!cashPoolNumber) {
      dispatch(updateError({ cashPoolNumberError: numberErrorMessage }));
      hasErrors = true;
    }

    if (hasErrors) {
      throw new Error('Some fields are invalid.');
    }
  };

  const onSaveClientFeatures = async () => {
    try {
      validateFields();
      await updateClientFeatures(Number(clientId), {
        featureAvailability,
        loanNumber,
        guaranteeNumber,
        backToBackLoanNumber,
        loanGuaranteeNumber,
        userNumber,
        cashPoolNumber,
        creditRatingNumber,
        geographyData,
        currency,
      });
      showToast('Client features updated successfully.');
    } catch (err) {
      errorHandler(err);
    }
  };

  useEffect(() => {
    getClient(clientId).then((client) => setClientName(client.name));
  }, [clientId]);

  return (
    <PageLayout title={clientName}>
      <ClientFeaturesComponent isClientView={false} />
      <FlexLayout alignItems="flex-end" flexGrow="1" justifyContent="flex-end">
        <Button text="Save" onClick={onSaveClientFeatures} variant="primary" />
      </FlexLayout>
    </PageLayout>
  );
};

export default ClientFeaturePage;
