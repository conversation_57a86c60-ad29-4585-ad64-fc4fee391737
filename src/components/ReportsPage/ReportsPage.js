import { useContext, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { FilterColumnsModal } from '~/components/Modals';
import { BackToBackLoansTable, CreditRatingsTable, GuaranteesTable, LoansTable } from '~/components/Shared';
import { TableColumnContext } from '~/context/tableColumn';
import { UserInfoContext } from '~/context/user';
import { REPORT_TYPE, reportEnum } from '~/enums';
import { useQuery, useReports } from '~/hooks';
import { backToBackLoanColumns } from '~/reducers/tableColumns/backToBackLoanColumns.slice';
import { guaranteeColumns } from '~/reducers/tableColumns/guaranteeColumns.slice';
import { loanColumns } from '~/reducers/tableColumns/loanColumns.slice';
import { routesEnum } from '~/routes';
import { <PERSON><PERSON>, Card, FlexLayout, LoadingSpinner, PageLayout, Tabs } from '~/ui';
import { showErrorToast, showToast } from '~/ui/components/Toast';
import { jsonToSheet } from '~/utils/documents';
import { genericTabSetter } from '~/utils/tabs';

import { getSheetData } from './ReportsPage.utils';

function ReportsPage() {
  const history = useHistory();
  const query = useQuery();
  const visibleLoanColumns = useSelector(loanColumns);
  const visibleB2BLoanColumns = useSelector(backToBackLoanColumns);
  const visibleGuaranteeColumns = useSelector(guaranteeColumns);
  const [tabs, setTabs] = useState([]);
  const [reportType, setReportType] = useState();
  const [showFilerModal, setShowFilterModal] = useState(false);
  const [isLoading, guaranteeReports, loanReports, creditRatings, b2bLoanReports, { setRefreshTrigger }] = useReports({
    limit: null,
    isPortfolio: false,
  });
  const { userInfo } = useContext(UserInfoContext);
  const { dbTableColumns } = useContext(TableColumnContext);

  function handleOnExportTableClick() {
    const sheetData = getSheetData({
      loanReports,
      visibleLoanColumns: dbTableColumns[reportEnum.LOAN] ?? visibleLoanColumns,
      b2bLoanReports,
      visibleB2BLoanColumns,
      guaranteeReports,
      visibleGuaranteeColumns: dbTableColumns[reportEnum.GUARANTEE] ?? visibleGuaranteeColumns,
      creditRatings,
      userInfo,
    });

    jsonToSheet(sheetData, 'Analyses.xlsx')
      .then(() => showToast('Sheet has been successfully exported.'))
      .catch(() => showErrorToast());
  }

  const handleOnImportTableClick = () => history.push(routesEnum.ANALYSES_IMPORT);

  const onTabSelect = (tabName) => history.replace(`${routesEnum.ANALYSES}?${REPORT_TYPE}=${tabName}`);

  useEffect(() => setReportType(query.get(REPORT_TYPE) || reportType), [query, reportType]);

  useEffect(() => {
    const { features } = userInfo;
    const tabs = [
      { isEnabled: features.loan, label: 'Loans', value: reportEnum.LOAN },
      { isEnabled: features.backToBackLoan, label: 'Back-To-Back Loans', value: reportEnum.BACK_TO_BACK_LOAN },
      { isEnabled: features.guarantee, label: 'Guarantees', value: reportEnum.GUARANTEE },
      { isEnabled: features.creditRating, label: 'Credit Ratings', value: reportEnum.CREDIT_RATING },
    ];
    genericTabSetter(setTabs, setReportType, tabs);
  }, [userInfo]);

  if (isLoading) return <LoadingSpinner />;

  return (
    <PageLayout
      title="Analyses"
      rightTitleContent={
        <FlexLayout alignItems="center" space={6}>
          <Button iconLeft="export" text="Export" size="s" variant="secondary" onClick={handleOnExportTableClick} />
          <Button iconLeft="add" size="s" text="Import" variant="secondary" onClick={handleOnImportTableClick} />
        </FlexLayout>
      }
    >
      <Card pb={3} pt={6}>
        <FlexLayout alignItems="center" justifyContent="space-between">
          <Tabs selectedTab={reportType} tabs={tabs} onTabSelect={onTabSelect} />
          {[reportEnum.LOAN, reportEnum.BACK_TO_BACK_LOAN, reportEnum.GUARANTEE].includes(reportType) && (
            <Button
              iconLeft="columns"
              size="s"
              text="Columns"
              variant="secondary"
              onClick={() => setShowFilterModal(true)}
            />
          )}
        </FlexLayout>
        {reportType === reportEnum.LOAN && (
          <LoansTable isEditable data={loanReports} setRefreshTrigger={setRefreshTrigger} isSearchable />
        )}
        {reportType === reportEnum.BACK_TO_BACK_LOAN && (
          <BackToBackLoansTable isEditable data={b2bLoanReports} setRefreshTrigger={setRefreshTrigger} isSearchable />
        )}
        {reportType === reportEnum.GUARANTEE && (
          <GuaranteesTable isEditable data={guaranteeReports} setRefreshTrigger={setRefreshTrigger} isSearchable />
        )}
        {reportType === reportEnum.CREDIT_RATING && (
          <CreditRatingsTable isEditable data={creditRatings} setRefreshTrigger={setRefreshTrigger} isSearchable />
        )}
      </Card>
      {showFilerModal && <FilterColumnsModal type={reportType} handleOnHide={() => setShowFilterModal(false)} />}
    </PageLayout>
  );
}

export default ReportsPage;
