import React from 'react';

import { ThreeDotActionMenu } from '~/components/Shared';
import { isAdmin, REPORT_TYPE, reportEnum } from '~/enums';
import { setNotification } from '~/reducers/notifications.slice';
import { updateField } from '~/reducers/payment.slice';
import { routesEnum } from '~/routes';
import { getUpperCaseReportType } from '~/utils/report';
import { getLoansSheetData } from '~/components/Shared/ReportsTables/LoansTable/LoansTable.utils';
import { getGuaranteesSheetData } from '~/components/Shared/ReportsTables/GuaranteesTable/GuaranteesTable.utils';
import { getCreditRatingsSheetData } from '~/components/Shared/ReportsTables/CreditRatingsTable/CreditRatingsTable.utils';
import { getB2BLoansSheetData } from '~/components/Shared/ReportsTables/BackToBackLoansTable/BackToBackLoansTable.utils';

export function getSheetData({
  guaranteeReports,
  visibleGuaranteeColumns,
  loanReports,
  visibleLoanColumns,
  b2bLoanReports,
  visibleB2BLoanColumns,
  creditRatings,
  userInfo,
}) {
  const loansSheetData = {
    sheetData: getLoansSheetData(loanReports, visibleLoanColumns, userInfo),
    sheetName: 'Loans',
  };
  const backToBackLoansSheetData = {
    sheetData: getB2BLoansSheetData(b2bLoanReports, visibleB2BLoanColumns, userInfo),
    sheetName: 'Back-to-back Loans',
  };
  const guaranteesSheetData = {
    sheetData: getGuaranteesSheetData(guaranteeReports, visibleGuaranteeColumns, userInfo),
    sheetName: 'Guarantees',
  };
  const creditRatingsSheetData = {
    sheetData: getCreditRatingsSheetData(creditRatings, userInfo),
    sheetName: 'Credit ratings',
  };

  return [loansSheetData, backToBackLoansSheetData, guaranteesSheetData, creditRatingsSheetData];
}

export const renderTableActionColumn = (
  item,
  reportType,
  setShowDeleteModal,
  setSelectedItem,
  history,
  role,
  dispatch
) => {
  const options = [];

  if (item.editable && item.status === 'Draft' && !item.isPortfolio) {
    options.push({
      label: 'Edit',
      onClick: () => history.push(`${history.location.pathname}/${item.id}/edit?${REPORT_TYPE}=${reportType}`),
    });
  }

  if (isAdmin(role) || !item.isPortfolio) {
    options.push({
      label: 'Delete',
      onClick: () => {
        setShowDeleteModal(true);
        setSelectedItem(item);
      },
    });
  }

  if (item.isPortfolio && item.status === 'Final' && reportType !== reportEnum.CREDIT_RATING) {
    options.push({
      label: 'Show all interest',
      onClick: () => {
        dispatch(updateField({ [`${reportType}Id`]: item.id }));
        history.push(`${routesEnum.PAYMENTS}?${REPORT_TYPE}=${reportType}`);
      },
    });
  }

  const upperCaseReportType = getUpperCaseReportType(reportType);
  if (!isAdmin(role) && item.isPortfolio) {
    options.push({
      label: 'Notify admin to delete',
      onClick: () =>
        dispatch(
          setNotification({
            action: `DELETE_${upperCaseReportType}`,
            title: `Notify admin to delete ${reportType}`,
            id: item.id,
          })
        ),
    });

    if (item.status === 'Final') {
      options.push({
        label: 'Notify admin to mark as Draft',
        onClick: () =>
          dispatch(
            setNotification({
              action: `MARK_${upperCaseReportType}_AS_DRAFT`,
              title: 'Notify admin to mark as Draft',
              id: item.id,
            })
          ),
      });
    }

    if (item.status === 'Draft') {
      options.push({
        label: 'Notify admin to move to analyses',
        onClick: () =>
          dispatch(
            setNotification({
              action: `MOVE_${upperCaseReportType}_TO_ANALYSES`,
              title: 'Notify admin to move to analyses',
              id: item.id,
            })
          ),
      });
    }
  }

  if (options.length === 0) return null;

  return <ThreeDotActionMenu options={options} />;
};
