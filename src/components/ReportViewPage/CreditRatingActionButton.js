import React from 'react';

import { getActionButtonNotificationAction } from '~/components/ReportViewPage/ReportView.utils';
import { rolesEnum } from '~/enums';
import { Button } from '~/ui';

const CreditRatingActionButton = ({
  isShowing,
  data,
  role,
  handleOnUpdateStatus,
  handleOnUpdateIsPortfolio,
  onNotifyAdminShowModal,
}) => {
  if (!isShowing) return null;
  if (data.status !== 'Draft') return null;

  if (role === rolesEnum.USER) {
    const action = getActionButtonNotificationAction(data);
    return <Button text="Notify admin" onClick={onNotifyAdminShowModal(action)} />;
  }

  return (
    <>
      {data.isPortfolio ? (
        <Button
          disabled={data.files.length !== 0 && data.files.some((f) => f.status === 'Draft')}
          text="Finalize"
          variant="primary"
          onClick={() => handleOnUpdateStatus('Final')}
        />
      ) : (
        <Button text="Add to Portfolio" variant="primary" onClick={() => handleOnUpdateIsPortfolio(true)} />
      )}
    </>
  );
};

export default CreditRatingActionButton;
