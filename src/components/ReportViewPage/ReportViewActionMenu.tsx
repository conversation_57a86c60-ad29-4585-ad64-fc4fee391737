import ThreeDotActionMenu from 'components/Shared/ThreeDotActionMenu';
import { GENERAL_NOTIFICATION_ACTIONS, isAdmin } from 'enums';
import { RoleType, PrincipalEmbeddedCompanyType } from 'types';

type ReportViewActionMenuProps = {
  item: {
    status: 'Final' | 'Draft';
    isPortfolio: boolean;
    editable: boolean;
    deletedAt: Date | null;
    principal?: PrincipalEmbeddedCompanyType;
  };
  onDelete: Function;
  onRestore: Function;
  onDraft: Function;
  onEdit: Function;
  onMoveToAnalysis: Function;
  role: RoleType;
  onNotifyAdminShowModal: Function;
  additionalOptions: [{ label: string; onClick: Function }] | null;
};

function ReportViewActionMenu({
  item,
  onDelete,
  onRestore,
  onDraft,
  onEdit,
  onMoveToAnalysis,
  role,
  onNotifyAdminShowModal,
  additionalOptions = null,
}: ReportViewActionMenuProps) {
  const options = [];

  const { status, isPortfolio, editable, deletedAt, principal } = item;
  const hasGuaranteeCumulativeProbabilityOfDefault =
    principal?.creditRating?.cumulativeProbabilityOfDefault != null || !principal;

  if (deletedAt) {
    if (!isPortfolio || isAdmin(role)) {
      options.push({
        label: 'Restore',
        onClick: onRestore,
      });
      options.push({
        label: 'Delete permanently',
        onClick: onDelete,
      });
    } else {
      options.push({
        label: 'Notify to restore',
        onClick: onNotifyAdminShowModal(GENERAL_NOTIFICATION_ACTIONS.RESTORE),
      });
      options.push({
        label: 'Notify to permanently delete',
        onClick: onNotifyAdminShowModal(GENERAL_NOTIFICATION_ACTIONS.PERMANENT_DELETE),
      });
    }
  } else {
    if (editable && status === 'Draft' && !isPortfolio && hasGuaranteeCumulativeProbabilityOfDefault) {
      options.push({
        label: 'Edit',
        onClick: onEdit,
      });
    }

    if (isAdmin(role) || !isPortfolio) {
      options.push({
        label: 'Delete',
        onClick: onDelete,
      });
    }

    if (isAdmin(role)) {
      if (status === 'Final') {
        options.push({
          label: 'Mark as Draft',
          onClick: onDraft,
        });
      }

      if (status === 'Draft' && isPortfolio) {
        options.push({
          label: 'Move to Analyses',
          onClick: onMoveToAnalysis,
        });
      }
    } else {
      if (isPortfolio) {
        options.push({
          label: 'Notify to delete',
          onClick: onNotifyAdminShowModal(GENERAL_NOTIFICATION_ACTIONS.DELETE),
        });
      }

      if (status === 'Final') {
        options.push({
          label: 'Notify to mark as Draft',
          onClick: onNotifyAdminShowModal(GENERAL_NOTIFICATION_ACTIONS.MARK_AS_DRAFT),
        });
      }

      if (status === 'Draft' && isPortfolio) {
        options.push({
          label: 'Notify to move to Analyses',
          onClick: onNotifyAdminShowModal(GENERAL_NOTIFICATION_ACTIONS.MOVE_TO_ANALYSES),
        });
      }
    }
  }
  if (additionalOptions) options.push(...additionalOptions);

  if (options.length === 0) return null;

  return <ThreeDotActionMenu options={options} dataTestId="reportViewActionMenu" />;
}

export default ReportViewActionMenu;
