import _ from 'lodash';
import { useContext, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

import { updateLoanRates, updateB2BLoanRates, updateGuaranteeRates } from '~/api';
import { TextWithTooltip } from '~/components/Shared';

import { reportEnum, whtEnums } from '~/enums';
import { UserInfoContext } from '~/context/user';
import { WithTooltip } from '~/ui/hocs';
import { updateField, updateBasisPoints, updateEstimationOfReferenceRate } from '~/reducers/report.slice';
import { Box, Button, Card, Checkbox, FlexLayout, NumberInput, RadioGroup, Tabs } from '~/ui';
import { errorHandler } from '~/utils/errors';
import { showToast, showWarningToast } from '~/ui/components/Toast';
import * as reportUtils from '~/utils/report';
import * as ratesCardUtils from './RatesCard.utils';

import WHTCreatableSingleSelect from './WHTCreatableSingleSelect';
import ExpenseSummaryGraph from './ExpenseSummaryGraph';
import RemunerationGraph from './RemunerationGraph';
import B2BLoanWHTModal from './B2BLoanWHTModal';

const PAYMENTS = 'Interest';
const TRANSACTION = 'Transaction';
const tabs = [
  { label: PAYMENTS, value: PAYMENTS },
  { label: TRANSACTION, value: TRANSACTION },
];

const RatesCard = ({ isShowing, reportId, data }) => {
  const dispatch = useDispatch();

  const { userInfo } = useContext(UserInfoContext);
  const [whtRateOptions, setWhtRateOptions] = useState([]);
  const [expenseSummaryCalculation, setExpenseSummaryCalculation] = useState(null);
  const [selectedTab, setSelectedTab] = useState(PAYMENTS);
  const [isB2BLoanWHTShowing, setIsB2BLoanWHTShowing] = useState(false);

  const { report, lender, borrower } = data;
  const isGuarantee = data.reportType === reportEnum.GUARANTEE;
  const isLoan = data.reportType === reportEnum.LOAN;
  const isBackToBackLoan = data.reportType === reportEnum.BACK_TO_BACK_LOAN;
  const isFixed = data.rateType?.type === 'fixed' || isGuarantee;
  const isPortfolio = data.isPortfolio;
  const isLoanBetweenSameCountries = data.borrower?.country === data.lender?.country;

  const getUpdateRatesBody = (report) => {
    if (isGuarantee) {
      return { finalInterestRate: report.finalInterestRate };
    }

    if (isLoan) {
      return {
        finalInterestRate: Number(report.finalInterestRate),
        isWhtEnabled: Boolean(report.isWhtEnabled),
        whtInterestRate: Number(report.whtInterestRate),
        approach: report.approach,
        ...(!isFixed && { estimationOfReferenceRate: report.estimationOfReferenceRate }),
      };
    }

    if (isBackToBackLoan) {
      return {
        finalInterestRate: report.finalInterestRate,
        ...(!isFixed && { estimationOfReferenceRate: report.estimationOfReferenceRate }),
      };
    }
  };

  const onConfirmClick = async () => {
    const updateRatesApiMethodMapper = {
      [reportEnum.LOAN]: updateLoanRates,
      [reportEnum.BACK_TO_BACK_LOAN]: updateB2BLoanRates,
      [reportEnum.GUARANTEE]: updateGuaranteeRates,
    };
    const updateRates = updateRatesApiMethodMapper[data.reportType];

    try {
      const res = await updateRates({ id: reportId, data: getUpdateRatesBody(data.report) });

      if (!isBackToBackLoan && userInfo.features.payment) {
        const expenseSummary = await ratesCardUtils.calculateExpenseSummary({ reportData: data, rates: data.report });
        setExpenseSummaryCalculation(expenseSummary);
      }
      if (isBackToBackLoan) {
        setExpenseSummaryCalculation(null);
        dispatch(updateField({ legs: res.legs }));
      }
      dispatch(updateField({ report: { ...res.report, isConfirmed: true } }));

      showToast(`Confirmed ${isFixed ? 'interest' : 'spread'}.`);
      if (isBackToBackLoan && res.legs.some((leg) => leg.report.finalInterestRate < 0)) {
        showWarningToast(`Negative interest ${isFixed ? 'rates' : 'spreads'}. Check pass-through remuneration inputs.`);
      }
    } catch (err) {
      errorHandler(err);
    }
  };

  const isWhtDisabled = () => {
    if (isFixed) {
      return (
        data.report.isConfirmed || data.report.finalInterestRate == null || Number.isNaN(data.report.finalInterestRate)
      );
    }

    return (
      data.report.isConfirmed ||
      data.report.estimationOfReferenceRate == null ||
      Number.isNaN(data.report.estimationOfReferenceRate) ||
      data.report.finalInterestRate == null ||
      Number.isNaN(data.report.finalInterestRate)
    );
  };

  const getWhtTooltip = () => {
    if (isFixed) return 'Enter interest rate first';
    return 'Enter estimated reference rate and spread first';
  };

  const getBackToBackWhtTooltip = () => {
    if (isFixed) return 'You must enter an interest rate before you can calculate withholding tax.';
    return 'You must enter estimated reference rate and spread before you can calculate withholding tax.';
  };

  const isConfirmDisabled = () => {
    if ((isLoan || isBackToBackLoan) && data.report.isWhtEnabled && !data.report.whtInterestRate) {
      return true;
    }

    if (isFixed) {
      const finalInterestRate = Number(data.report.finalInterestRate);
      return !_.isFinite(finalInterestRate) || finalInterestRate === 0;
    }

    const basisPoints = Number(data.report.finalInterestRate);
    if (!_.isFinite(basisPoints) || basisPoints === 0) return true;

    return false;
  };

  useEffect(() => {
    if (data.report.finalInterestRate && isShowing) {
      dispatch(updateField({ report: { ...data.report, isConfirmed: true } }));

      if (!isBackToBackLoan && userInfo.features.payment) {
        ratesCardUtils
          .calculateExpenseSummary({ reportData: data, rates: data.report })
          .then(setExpenseSummaryCalculation);
      }
    }
    // eslint-disable-next-line
  }, []);

  useEffect(() => {
    if (!reportUtils.checkIsImported(report) && !isBackToBackLoan) {
      ratesCardUtils.handleWhtRateSetup({ isGuarantee, report, lender, borrower }).then(setWhtRateOptions);
    }
  }, [report, lender, borrower, isBackToBackLoan, isGuarantee]);

  useEffect(() => {
    if (isBackToBackLoan) {
      setSelectedTab(TRANSACTION);
    }
  }, [isBackToBackLoan]);

  useEffect(() => {
    // Init approach to approach1 if not already set
    if (data.report.isWhtEnabled && !data.report.approach) {
      dispatch(updateField({ report: { ...data.report, approach: whtEnums.WHT_APPROACHES.BORROWER_PAYS } }));
    }
    // eslint-disable-next-line
  }, [data.report.isWhtEnabled]);

  if (!isShowing) return null;

  return (
    <Card p={4}>
      <FlexLayout justifyContent="space-between">
        <TextWithTooltip
          label={isLoan || isBackToBackLoan ? 'Interest' : 'Fee'}
          color="deep-sapphire"
          tooltip={isGuarantee ? 'Insert a guarantee fee' : 'Insert an interest rate and withholding tax'}
          variant="2l-spaced"
        />
        <Tabs
          minWidth="tab-width-xs"
          height="tab-height-small"
          textVariant="xs-spaced"
          colorSelected="shakespeare"
          isShowing={!!data.report.isConfirmed}
          tabs={tabs}
          selectedTab={selectedTab}
          onTabSelect={setSelectedTab}
        />
      </FlexLayout>
      <FlexLayout space={4} p={5} alignItems="flex-end" bg="alabaster">
        {isFixed ? (
          <NumberInput
            disabled={data.report.isConfirmed}
            label={isGuarantee ? 'Guarantee Fee' : 'Interest Rate'}
            inputType="float"
            width="xs"
            height="input-height-text"
            bg={data.report.isConfirmed ? 'alabaster' : 'white'}
            unit="%"
            value={data.report.finalInterestRate}
            onChange={(finalInterestRate) => dispatch(updateField({ report: { ...data.report, finalInterestRate } }))}
          />
        ) : (
          <>
            <NumberInput
              tooltip={ratesCardUtils.estOfRefRateTooltip}
              disabled={data.report.isConfirmed}
              label="Est. Ref. Rate"
              inputType="float"
              width="xs"
              height="input-height-text"
              bg={data.report.isConfirmed ? 'alabaster' : 'white'}
              value={data.report.estimationOfReferenceRate}
              onChange={(estimationOfReferenceRate) =>
                dispatch(updateEstimationOfReferenceRate({ estimationOfReferenceRate }))
              }
              unit="%"
            />
            <NumberInput
              disabled={data.report.isConfirmed}
              label="Spread"
              inputType="float"
              width="xs"
              height="input-height-text"
              bg={data.report.isConfirmed ? 'alabaster' : 'white'}
              value={data.report.finalInterestRate}
              onChange={(finalInterestRate) => dispatch(updateBasisPoints({ finalInterestRate }))}
              unit="bps"
            />
          </>
        )}
        {isLoan && (
          <>
            <Box bg="link-water" sx={{ borderRight: 'border', height: 'input-height-text' }}></Box>
            <WithTooltip
              disabled={true}
              label="whtLoanCheckbox"
              tooltip={ratesCardUtils.getWhtCheckboxTooltip(isLoanBetweenSameCountries)}
            >
              <Checkbox
                disabled={data.report.isConfirmed || isLoanBetweenSameCountries}
                isActive={data.report.isWhtEnabled}
                label="Withholding tax"
                bg={data.report.isConfirmed || isLoanBetweenSameCountries ? 'alabaster' : 'white'}
                onChange={() =>
                  dispatch(updateField({ report: { ...data.report, isWhtEnabled: !data.report.isWhtEnabled } }))
                }
                variant="outlinedAlternative"
              />
            </WithTooltip>
          </>
        )}
        {isLoan && data.report.isWhtEnabled && (
          <>
            <RadioGroup
              disabled={data.report.isConfirmed}
              options={[
                { label: 'Net interest', value: whtEnums.WHT_APPROACHES.BORROWER_PAYS },
                { label: 'Gross-up', value: whtEnums.WHT_APPROACHES.GROSS_UP },
              ]}
              label="Withholding Tax Approaches"
              tooltip={ratesCardUtils.approachesTooltip}
              width="sm"
              radioVariant="primarySmallerPadding"
              bg={data.report.isConfirmed ? 'alabaster' : 'white'}
              value={data.report.approach}
              onChange={(approach) => dispatch(updateField({ report: { ...data.report, approach } }))}
            />
            <WithTooltip label="Disable WHT Rate Picker" tooltip={getWhtTooltip()} disabled={isWhtDisabled()}>
              <WHTCreatableSingleSelect
                disabled={isWhtDisabled()}
                width="xs"
                height="input-height-text"
                bg={data.report.isConfirmed ? 'alabaster' : 'white'}
                options={whtRateOptions}
                setWhtRateOptions={setWhtRateOptions}
                value={String(data.report.whtInterestRate)}
                onChange={(whtInterestRate) => dispatch(updateField({ report: { ...data.report, whtInterestRate } }))}
              />
            </WithTooltip>
          </>
        )}
        <Button
          disabled={isConfirmDisabled()}
          isShowing={!data.report.isConfirmed && !isPortfolio}
          text="Confirm"
          variant="secondary"
          onClick={onConfirmClick}
          sx={{ alignSelf: 'flex-end', height: 'input-height-text' }}
        />
        <Button
          text="Edit"
          isShowing={!!data.report.isConfirmed && !isPortfolio}
          variant="secondary"
          onClick={() => dispatch(updateField({ report: { ...data.report, isConfirmed: false } }))}
          sx={{ alignSelf: 'flex-end', height: 'input-height-text' }}
        />
        <WithTooltip
          sx={{ marginLeft: 'auto' }}
          disabled={!data.report.isConfirmed}
          label="enterWhtRates"
          tooltip={getBackToBackWhtTooltip()}
        >
          <Button
            text="Withholding Tax"
            variant="secondary"
            sx={{ height: 'input-height-text' }}
            isShowing={isBackToBackLoan && data.report.isConfirmed && !isPortfolio}
            disabled={!data.report.isConfirmed}
            onClick={() => setIsB2BLoanWHTShowing(true)}
          />
        </WithTooltip>
      </FlexLayout>
      <ExpenseSummaryGraph
        isShowing={selectedTab === PAYMENTS && data.report.isConfirmed}
        data={data}
        expenseSummaryCalculation={expenseSummaryCalculation}
        setExpenseSummaryCalculation={setExpenseSummaryCalculation}
      />
      <RemunerationGraph isShowing={selectedTab === TRANSACTION && data.report.isConfirmed} />
      {/* Hidden above the viewport. Used for Generate Summary because it's needs to be in the summary even when not shown */}
      <RemunerationGraph id="remunerationGraph" isShowing={true} />
      <B2BLoanWHTModal
        isShowing={isBackToBackLoan && isB2BLoanWHTShowing}
        onHide={() => setIsB2BLoanWHTShowing(false)}
        data={data}
      />
    </Card>
  );
};

export default RatesCard;
