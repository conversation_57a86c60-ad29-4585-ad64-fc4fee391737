import { saveAs } from 'file-saver';
import { useContext, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import { getCreditRating, getCreditRatingFile } from '~/api';
import { DeleteModal, RestoreModal } from '~/components/Modals';
import ReportFilesTable from '~/components/ReportViewPage/ReportFilesTable';
import { NotesCard, UserLog } from '~/components/Shared';
import PDFViewer from '~/components/Shared/PDFViewer';
import { showErrorToast } from '~/ui/components/Toast';
import { UserInfoContext } from '~/context/user';
import { REPORT_TYPE, reportEnum } from '~/enums';
import {
  creditRating,
  resetCreditRating,
  setCreditRatingData,
  setIsPristine,
  updateField,
} from '~/reducers/creditRating.slice';
import { <PERSON><PERSON>, <PERSON>, FlexLayout, LoadingSpinner, PageLayout } from '~/ui';
import { cancellable } from '~/utils/promise';

import CreditRatingActionButton from './CreditRatingActionButton';
import ReportCharacteristics from './ReportCharacteristics';
import { getCharacteristicsCreditRatingData, getNotificationAction, onNotifyAdminShowModal } from './ReportView.utils';
import ReportViewActionMenu from './ReportViewActionMenu';

function CreditRatingView({
  handleOnItemDelete,
  handleOnItemRestore,
  handleUpdateIsPortfolio,
  handleUpdateStatus,
  handleSaveNoteClick,
  setIsReportMovedModalOpen,
}) {
  const history = useHistory();
  const { reportId } = useParams();
  const data = useSelector(creditRating);
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(true);
  const [pdf, setPdf] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showRestoreModal, setShowRestoreModal] = useState(false);
  const { userInfo } = useContext(UserInfoContext);

  const isReportInTrash = data?.deletedAt !== null;

  useEffect(() => {
    const cancellablePromise = cancellable(getCreditRating(reportId));
    cancellablePromise.promise
      .then((data) => {
        dispatch(setCreditRatingData(data));
        if (data?.attributes) {
          const cancellablePromisePdf = cancellable(getCreditRatingFile({ fileId: data?.files[0]?.id }));
          cancellablePromisePdf.promise.then((pdf) => {
            setPdf(pdf);
            setIsLoading(false);
          });
        } else {
          setIsLoading(false);
        }
      })
      .catch((err) => {
        if (err.cancelled) {
          cancellablePromise.cancel();
        }
        if (err.response.status === 404) {
          setIsReportMovedModalOpen(true);
        }
      });

    return () => {
      cancellablePromise.cancel();
      dispatch(resetCreditRating());
    };
  }, [dispatch, history, reportId, setIsReportMovedModalOpen]);

  const handleOnDownloadTemplateClick = async () => {
    if (data?.files[1] == null) return showErrorToast('Template file is missing.');

    const uploadedTemplateFile = await getCreditRatingFile({ fileId: data?.files[1].id });
    saveAs(uploadedTemplateFile, uploadedTemplateFile.name);
  };
  const downloadTemplateOption = { label: 'Download template', onClick: handleOnDownloadTemplateClick };

  function handleOnDownloadClick() {
    saveAs(pdf, pdf.name);
  }

  function handleOnUpdateIsPortfolio(isPortfolio) {
    handleUpdateIsPortfolio(reportId, { isPortfolio });
  }

  function handleOnUpdateStatus(status) {
    handleUpdateStatus(reportId, { status }, (resData) => {
      dispatch(updateField({ status: resData?.status, finalizedBy: resData?.finalizedBy }));
      dispatch(setIsPristine());
    });
  }

  function handleOnSaveNoteClick() {
    handleSaveNoteClick(reportId, { note: data?.note }, (resData) => {
      dispatch(updateField({ note: resData?.note, updatedBy: resData?.updatedBy }));
      dispatch(setIsPristine());
    });
  }

  if (isLoading) return <LoadingSpinner />;

  return (
    <>
      <PageLayout
        title="Credit Rating"
        rightTitleContent={
          <FlexLayout alignItems="center" space={6}>
            <FlexLayout alignItems="center" mr={2} space={6}>
              <UserLog label="Created by:" user={data?.createdBy} />
              {data?.updatedBy && <UserLog label="Updated by:" user={data?.updatedBy} />}
              {data?.finalizedBy && <UserLog label="Finalized by:" user={data?.finalizedBy} />}
            </FlexLayout>
            {pdf && (
              <Button
                iconLeft="download"
                text="Download"
                size="s"
                variant="secondary"
                onClick={handleOnDownloadClick}
              />
            )}
            <ReportViewActionMenu
              item={data}
              onDelete={() => setShowDeleteModal(true)}
              onRestore={() => setShowRestoreModal(true)}
              onDraft={() => handleOnUpdateStatus('Draft')}
              onEdit={() => {
                history.push(
                  `/${data?.isPortfolio ? 'portfolio' : 'analyses'}/${data.id}/edit?${REPORT_TYPE}=${data.reportType}`
                );
              }}
              onMoveToAnalysis={() => handleOnUpdateIsPortfolio(false)}
              role={userInfo.role}
              onNotifyAdminShowModal={(notificationType) =>
                onNotifyAdminShowModal(
                  getNotificationAction(data.reportType, notificationType),
                  'CREDIT_RATING',
                  data.id,
                  dispatch
                )
              }
              additionalOptions={data.isTemplateUploaded ? [downloadTemplateOption] : null}
            />
          </FlexLayout>
        }
      >
        {pdf ? (
          <Card flexGrow="1" py={6}>
            {pdf && <PDFViewer file={pdf} />}
          </Card>
        ) : (
          <ReportCharacteristics
            data={getCharacteristicsCreditRatingData(data, userInfo)}
            split={false}
            createdAt={data?.createdAt}
            updatedAt={data?.updatedAt}
            reportType={data?.reportType}
          />
        )}
        {data?.isPortfolio && (
          <>
            {data?.attributes == null && (
              <ReportFilesTable
                formData={data}
                formType={reportEnum.CREDIT_RATING}
                updateField={updateField}
                isReportInTrash={isReportInTrash}
              />
            )}
            <NotesCard
              description="Include any notes on the report here."
              editMode={data?.status === 'Draft' && !isReportInTrash}
              note={data?.note}
              onChange={(value) => dispatch(updateField({ note: value }))}
              onSave={handleOnSaveNoteClick}
            />
          </>
        )}
        <FlexLayout alignItems="flex-end" flexGrow="1" justifyContent="space-between">
          <Button text="Back" variant="gray" onClick={history.goBack} />
          <CreditRatingActionButton
            data={data}
            isShowing={!isReportInTrash}
            role={userInfo.role}
            handleOnUpdateStatus={handleOnUpdateStatus}
            handleOnUpdateIsPortfolio={handleOnUpdateIsPortfolio}
            onNotifyAdminShowModal={(notificationType) =>
              onNotifyAdminShowModal(
                getNotificationAction(data.reportType, notificationType),
                'CREDIT_RATING',
                data.id,
                dispatch
              )
            }
          />
        </FlexLayout>
      </PageLayout>
      <DeleteModal
        item="credit rating"
        isShowing={showDeleteModal}
        isDeletePermanent={isReportInTrash}
        handleOnDeleteClick={() => handleOnItemDelete({ id: data?.id, force: isReportInTrash })}
        handleOnHide={() => setShowDeleteModal(false)}
      />
      <RestoreModal
        item="credit rating"
        isShowing={showRestoreModal}
        handleOnRestoreClick={() => handleOnItemRestore(data?.id)}
        handleOnHide={() => setShowRestoreModal(false)}
      />
    </>
  );
}

export default CreditRatingView;
