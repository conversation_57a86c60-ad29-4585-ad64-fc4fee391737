import { Fragment, useContext } from 'react';

import { CountryContext, UserInfoContext } from 'context';
import { useAppSelector } from 'hooks';
import { reportEnum } from 'enums';
import { report } from 'reducers/report.slice';
import { FlexLayout, Icon, Text } from 'ui';
import { displayNumber2 } from 'utils/strings';
import { CountryValueType, CurrencyValueType } from 'types';
import { Company } from 'types/database/Company.type';

type TextVariant = '2l-spaced' | 'm-spaced' | 's-spaced' | 'xs-spaced';

const CompanyBox = ({ name, country, textVariant }: { name: string; country: string; textVariant: TextVariant }) => {
  const { countriesByName } = useContext(CountryContext);
  const width = 215;

  const textStyle = {
    textAlign: 'center',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    width: `${width - 20}px`,
  };

  return (
    <FlexLayout
      flexDirection="column"
      alignItems="center"
      space={2}
      bg="link-water"
      px={8}
      py={6}
      sx={{ borderRadius: 'm', borderColor: 'link-water', minWidth: `${width}px`, maxWidth: `${width}px` }}
    >
      <Text color="deep-sapphire" variant={textVariant} sx={textStyle}>
        {name}
      </Text>
      <Text color="deep-sapphire" variant={textVariant} sx={textStyle}>
        {`${country} ${countriesByName[country as CountryValueType].flagEmoji}`}
      </Text>
    </FlexLayout>
  );
};

const getLeftPosition = (step: number) => {
  const startingPoint = 131;
  const connectorWidth = 263;
  return startingPoint + step * connectorWidth;
};

const TopConnector = ({ step, amount, currency }: { step: number; amount: number; currency: CurrencyValueType }) => {
  const { userInfo } = useContext(UserInfoContext);

  return (
    <FlexLayout sx={{ position: 'absolute', top: '69px', left: `${getLeftPosition(step)}px` }}>
      <Text
        color="deep-sapphire"
        variant="m-spaced-bold"
        sx={{ position: 'absolute', top: '-23px', whiteSpace: 'nowrap', textAlign: 'center', width: '272px' }}
      >
        {`${currency} ${displayNumber2(amount, { decimalPoint: userInfo.decimalPoint, minDig: 0 })}`}
      </Text>
      <Icon icon="b2bTopConnector" size="icon-xl" />
    </FlexLayout>
  );
};

const BottomConnector = ({ step, rate }: { step: number; rate: number }) => {
  const { userInfo } = useContext(UserInfoContext);
  const data = useAppSelector(report);
  const isFixed = data.rateType?.type === 'fixed' || data.reportType === reportEnum.GUARANTEE;
  const unit = isFixed ? '%' : 'bps';

  return (
    <FlexLayout sx={{ position: 'absolute', top: '200px', left: `${getLeftPosition(step)}px` }}>
      <Text
        color="deep-sapphire"
        variant="m-spaced-bold"
        sx={{ position: 'absolute', top: '35px', whiteSpace: 'nowrap', textAlign: 'center', width: '272px' }}
      >
        {displayNumber2(rate, { decimalPoint: userInfo.decimalPoint, minDig: 0, unit })}
      </Text>
      <Icon icon="b2bBottomConnector" size="icon-xl" />
    </FlexLayout>
  );
};

const getLenders = (data: any) => {
  if (data.reportType === reportEnum.BACK_TO_BACK_LOAN) return data.lenders;
  if (data.reportType === reportEnum.LOAN) return [data.lender];
  if (data.reportType === reportEnum.GUARANTEE) return [data.guarantor];
};

const getLastBorrower = (data: any) => {
  if (data.reportType === reportEnum.BACK_TO_BACK_LOAN) return data.borrowers[data.borrowers.length - 1];
  if (data.reportType === reportEnum.LOAN) return data.borrower;
  if (data.reportType === reportEnum.GUARANTEE) return data.principal;
};

const getRate = (data: any, index: number): number => {
  if (data.reportType === reportEnum.BACK_TO_BACK_LOAN) return data.legs[index]?.report?.finalInterestRate;
  return data.report.finalInterestRate;
};

const getLongestStringLength = (companies: Array<Company>): number => {
  return companies.reduce((overallMaxLength: number, currentValue: Company) => {
    const longestCurrentLength = Math.max(currentValue.name.length, currentValue.country.length);

    if (longestCurrentLength > overallMaxLength) {
      return longestCurrentLength;
    }
    return overallMaxLength;
  }, 0);
};

const getTextVariant = (textLength: number): TextVariant => {
  if (textLength > 25) return 'xs-spaced';
  if (textLength > 20) return 's-spaced';
  if (textLength > 15) return 'm-spaced';
  return '2l-spaced';
};

const RemunerationGraph = ({ isShowing, id }: { isShowing: boolean; id?: string }) => {
  const data = useAppSelector(report);
  const hideAboveViewportStyle = id ? { position: 'absolute', top: -2000 } : {};
  const lenders = getLenders(data);
  const lastBorrower = getLastBorrower(data);
  const companies = [...lenders, lastBorrower];
  const textVariant = getTextVariant(getLongestStringLength(companies));

  if (!isShowing) return null;

  return (
    <FlexLayout
      bg="alabaster"
      sx={{ width: '100%', ...hideAboveViewportStyle }}
      justifyContent="center"
      alignItems="center"
      id={id}
    >
      <FlexLayout
        alignItems="center"
        space={12}
        px={8}
        sx={{ position: 'relative', minHeight: '300px', maxWidth: '100%', overflowX: 'scroll' }}
      >
        {companies.map((company: Company, index: number) => (
          <CompanyBox key={index} name={company.name} country={company.country} textVariant={textVariant} />
        ))}
        {lenders.map((_: Company, index: number) => (
          <Fragment key={index}>
            <TopConnector step={index} amount={data.amount} currency={data.currency} />
            <BottomConnector step={index} rate={getRate(data, index)} />
          </Fragment>
        ))}
      </FlexLayout>
    </FlexLayout>
  );
};

export default RemunerationGraph;
