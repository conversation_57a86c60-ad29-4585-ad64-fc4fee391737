import isSameDay from 'date-fns/isSameDay';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { useContext, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import {
  getB2BLoan,
  getGuarantee,
  getGuaranteePaymentsByGuaranteeId,
  getLoan,
  getLoanPaymentsByLoanId,
  runGuaranteeAlgorithm,
  runLoanAlgorithm,
  updateGuaranteeWithUpdatedValues,
  updateLoanWithUpdatedValues,
} from '~/api';
import { ConfirmModal, DeleteModal, RestoreModal, SideBySideCalculationModal } from '~/components/Modals';
import ReportFilesTable from '~/components/ReportViewPage/ReportFilesTable';
import { NotesCard, UserLog } from '~/components/Shared';
import { UserInfoContext } from '~/context/user';
import { REPORT_TYPE, reportEnum, whtEnums } from '~/enums';
import { ORIGINAL, UPDATED } from '~/enums/reports';
import { useQuery } from '~/hooks';
import {
  report,
  resetReport,
  setIsPristine,
  setIsSubmitting,
  setReportData,
  transformGuarantee,
  transformLoan,
  updateField,
} from '~/reducers/report.slice';
import { Button, FlexLayout, LoadingSpinner, PageLayout, Text } from '~/ui';
import { showErrorToast, showToast } from '~/ui/components/Toast';
import { getUpdatedMaturityDate } from '~/utils/dates';
import { errorHandler } from '~/utils/errors';
import { checkIsImported, getReportUnit, getUpperCaseReportType } from '~/utils/report';
import { capitalize } from '~/utils/strings';

import CalculationResults from './CalculationResults/CalculationResults';
import RatesCard from './RatesCard';
import ReportActionButton from './ReportActionButton';
import ReportCharacteristics from './ReportCharacteristics';
import {
  getCharacteristicsReportData,
  getConfirmModalAdditionalInfo,
  getNotificationAction,
  isReportFilesTableShown,
  onNotifyAdminShowModal,
} from './ReportView.utils';
import ReportViewActionMenu from './ReportViewActionMenu';
import UpcomingPaymentsTable from './UpcomingPaymentsTable';

function ReportView({
  handleOnItemDelete,
  handleOnItemRestore,
  handleUpdateIsPortfolio,
  handleUpdateStatus,
  handleSaveNoteClick,
  setIsReportMovedModalOpen,
}) {
  const dispatch = useDispatch();
  const history = useHistory();
  const query = useQuery();
  const { reportId } = useParams();
  const data = useSelector(report);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showRestoreModal, setShowRestoreModal] = useState(false);
  const [showSideBySideModal, setShowSideBySideModal] = useState(false);
  const [summaryIsLoading, setSummaryIsLoading] = useState(false);
  const [confirmInterestRateData, setConfirmInterestRateData] = useState(false);
  const [updatedAlgorithmData, setUpdatedAlgorithmData] = useState({});
  const [reportPayments, setReportPayments] = useState();
  const { userInfo } = useContext(UserInfoContext);
  const isLoan = data?.reportType === reportEnum.LOAN;
  const isBackToBackLoan = data?.reportType === reportEnum.BACK_TO_BACK_LOAN;

  const reportType = query.get(REPORT_TYPE) || reportEnum.LOAN;
  const upperCaseReportType = getUpperCaseReportType(reportType);
  const isBalloon = data?.type === 'Balloon';
  const isReportInTrash = data?.deletedAt !== null;
  const unit = getReportUnit(data.rateType);
  const isShowingWarningThatGuaranteeAlgorithmHasChanged =
    reportType === reportEnum.GUARANTEE &&
    !checkIsImported(data?.report) &&
    data.principal?.creditRating?.cumulativeProbabilityOfDefault == null;

  useEffect(() => {
    const getReportApiMethodMapper = {
      [reportEnum.LOAN]: getLoan,
      [reportEnum.GUARANTEE]: getGuarantee,
      [reportEnum.BACK_TO_BACK_LOAN]: getB2BLoan,
    };
    const getReport = getReportApiMethodMapper[reportType];

    getReport(reportId)
      .then((res) => dispatch(setReportData({ ...res, reportType })))
      .catch((error) => {
        if (error?.response?.status === 404) {
          setIsReportMovedModalOpen(true);
        }
      });

    return () => {
      dispatch(resetReport());
    };
  }, [dispatch, history, reportId, reportType, setIsReportMovedModalOpen]);

  useEffect(() => {
    if (data?.isPortfolio && userInfo.features.payment && reportType !== reportEnum.BACK_TO_BACK_LOAN) {
      const getPaymentsByReportId =
        reportType === reportEnum.LOAN ? getLoanPaymentsByLoanId : getGuaranteePaymentsByGuaranteeId;
      getPaymentsByReportId({ id: reportId, isBalloon }).then(setReportPayments);
    }
  }, [reportId, reportType, data.isPortfolio, data.status, isBalloon, userInfo.features.payment]);

  const getFormat = () => {
    const element = document.getElementById('reports');
    const elementClone = element.cloneNode(true);
    if (checkIsImported(data.report)) {
      elementClone.style.height = '20cm';
      elementClone.style.width = '29.7cm';
      return [elementClone, 'a4'];
    }

    elementClone.style.height = '24.7cm';
    elementClone.style.width = '42cm';
    return [elementClone, [940, 1570]];
  };

  async function handleOnGenerateSummaryClick() {
    try {
      setSummaryIsLoading(true);
      const [elementClone, format] = getFormat();

      const pdf = new jsPDF({ hotfixes: ['px_scaling'], orientation: 'landscape', unit: 'px', format });
      await pdf.html(elementClone);

      const summaryStatisticsContainer = document.getElementById('remunerationGraph');
      if (summaryStatisticsContainer) {
        const base64data = (await html2canvas(summaryStatisticsContainer)).toDataURL('image/png');
        pdf.addPage();
        pdf.addImage(base64data, 'PNG', 250, 100, 1100, 300);
      }

      pdf.save('PricingReport.pdf');
      showToast('PDF has been successfully created.');
    } catch (err) {
      errorHandler(err);
    } finally {
      setSummaryIsLoading(false);
    }
  }

  function handleOnUpdateIsPortfolio(isPortfolio) {
    handleUpdateIsPortfolio(reportId, { isPortfolio });
  }

  function handleOnUpdateStatus(status) {
    handleUpdateStatus(reportId, { status }, (resData) => {
      dispatch(updateField({ status: resData?.status, finalizedBy: resData?.finalizedBy }));
      dispatch(setIsPristine());
    });
  }

  function handleOnSaveNoteClick() {
    handleSaveNoteClick(reportId, { note: data?.note }, (resData) => {
      dispatch(updateField({ note: resData?.note, updatedBy: resData?.updatedBy }));
      dispatch(setIsPristine());
    });
  }

  function getAlgorithmAndDataTransform(type) {
    if (type === reportEnum.LOAN) {
      return [transformLoan, runLoanAlgorithm];
    }

    return [transformGuarantee, runGuaranteeAlgorithm];
  }

  async function handleOnAddToPortfolio() {
    if (checkIsImported(data.report) || isBackToBackLoan) {
      return handleOnUpdateIsPortfolio(true);
    }

    if (
      !isSameDay(new Date(data.issueDate), new Date(data.createdAt)) &&
      !isSameDay(new Date(data.issueDate), new Date(data.movedToAnalysesDate))
    ) {
      return setConfirmInterestRateData({ ...data, pickedReport: ORIGINAL });
    }

    // maturity date is called termination date in guarantees
    const maturityDate = data.maturityDate || data.terminationDate;
    const updatedIssueDate = new Date();
    const updatedMaturityDate = getUpdatedMaturityDate(maturityDate, data.issueDate, updatedIssueDate);

    if (isSameDay(new Date(data.issueDate), updatedIssueDate)) {
      return setConfirmInterestRateData({ ...data, pickedReport: ORIGINAL });
    }

    const dataWithNewDates = { ...data, issueDate: updatedIssueDate, maturityDate: updatedMaturityDate };

    dispatch(setIsSubmitting(true));
    try {
      const [transformData, runAlgorithm] = getAlgorithmAndDataTransform(dataWithNewDates.reportType);
      const { report, pricingApproach } = await runAlgorithm({ id: reportId, data: transformData(dataWithNewDates) });
      const updatedData = { ...dataWithNewDates, pricingApproach, report: { ...data.report, ...report } };
      setUpdatedAlgorithmData(updatedData);
      setShowSideBySideModal(true);
    } catch (err) {
      showErrorToast();
    }
    dispatch(setIsSubmitting(false));
  }

  async function handleMoveToPortfolioAfterInterestConfirm() {
    if (confirmInterestRateData.pickedReport === ORIGINAL) {
      return handleOnUpdateIsPortfolio(true);
    }

    const updateReportWithUpdatedValues =
      data.reportType === reportEnum.LOAN ? updateLoanWithUpdatedValues : updateGuaranteeWithUpdatedValues;

    await updateReportWithUpdatedValues({
      id: reportId,
      data: {
        pricingApproach: updatedAlgorithmData.pricingApproach, // undefined for guarantees
        issueDate: updatedAlgorithmData.issueDate,
        maturityDate: updatedAlgorithmData.maturityDate,
        report: { ...updatedAlgorithmData.report },
      },
    })
      .then(() => {
        showToast(`${capitalize(reportType)} has been added to Portfolio.`);
        history.replace(`/portfolio/${reportId}?${REPORT_TYPE}=${reportType}`);
      })
      .catch(errorHandler);
  }

  function onSideBySideReportPick(pickedData, pickedReport) {
    setShowSideBySideModal(false);
    setConfirmInterestRateData({ ...pickedData, pickedReport });
  }

  const getConfirmModalTitle = () => {
    const finalInterestRate = data.report.finalInterestRate;
    const approach = whtEnums.WHT_APPROACHES_LABELS[data.report.approach];
    const reportType = data.reportType;
    const whtInterestRate = data.report.whtInterestRate;

    if (data.report.isWhtEnabled) {
      return `Add ${reportType} to portfolio using interest rate of ${finalInterestRate}${unit} and withholding tax of ${whtInterestRate}% calculated using ${approach} approach?`;
    } else return `Add ${reportType} to portfolio using interest rate of ${finalInterestRate}${unit}?`;
  };

  if (!data.reportType) return <LoadingSpinner />;

  return (
    <>
      <PageLayout
        title="Summary Report"
        rightTitleContent={
          <FlexLayout alignItems="center" space={6}>
            <FlexLayout alignItems="center" mr={2} space={6}>
              <UserLog label="Created by:" user={data?.createdBy} />
              {data?.updatedBy && <UserLog label="Updated by:" user={data?.updatedBy} />}
              {data?.finalizedBy && <UserLog label="Finalized by:" user={data?.finalizedBy} />}
            </FlexLayout>
            <Button
              loading={summaryIsLoading}
              size="s"
              text="Generate a summary"
              variant="secondary"
              onClick={summaryIsLoading ? null : handleOnGenerateSummaryClick}
            />
            <ReportViewActionMenu
              item={data}
              onDelete={() => setShowDeleteModal(true)}
              onRestore={() => setShowRestoreModal(true)}
              onDraft={() => handleOnUpdateStatus('Draft')}
              onEdit={() => {
                history.push(
                  `/${data?.isPortfolio ? 'portfolio' : 'analyses'}/${data.id}/edit?${REPORT_TYPE}=${reportType}`
                );
              }}
              onMoveToAnalysis={() => handleOnUpdateIsPortfolio(false)}
              role={userInfo.role}
              onNotifyAdminShowModal={(notificationType) =>
                onNotifyAdminShowModal(
                  getNotificationAction(data.reportType, notificationType),
                  upperCaseReportType,
                  data.id,
                  dispatch
                )
              }
            />
          </FlexLayout>
        }
      >
        {isShowingWarningThatGuaranteeAlgorithmHasChanged && (
          <Text color="pomegranate" variant="2l-spaced">
            We have updated the algorithm for guarantees. Reprice this Guarantee to apply the new algorithm.
          </Text>
        )}
        <FlexLayout id="reports" space={6}>
          {!checkIsImported(data?.report) && <CalculationResults />}
          <ReportCharacteristics
            data={getCharacteristicsReportData(data, userInfo)}
            split={checkIsImported(data?.report)}
            createdAt={data?.createdAt}
            updatedAt={data?.updatedAt}
            reportType={data?.reportType}
          />
        </FlexLayout>
        <RatesCard isShowing={!isReportInTrash && !checkIsImported(data.report)} reportId={reportId} data={data} />
        {isReportFilesTableShown(data, reportType) && (
          <ReportFilesTable
            formData={data}
            formType={reportType}
            updateField={updateField}
            showGenerateAgreement={!data.isPortfolio}
            role={userInfo.role}
            isReportInTrash={isReportInTrash}
            isRateFormConfirmed={data.report.isConfirmed}
          />
        )}
        {data.isPortfolio && (
          <>
            {userInfo.features.payment && reportType !== reportEnum.BACK_TO_BACK_LOAN && (
              <UpcomingPaymentsTable reportType={reportType} reportPayments={reportPayments} />
            )}
            <NotesCard
              description="Include any notes on the report here."
              editMode={data?.status === 'Draft' && !isReportInTrash}
              note={data?.note}
              onChange={(value) => dispatch(updateField({ note: value }))}
              onSave={handleOnSaveNoteClick}
            />
          </>
        )}
        <FlexLayout alignItems="flex-end" flexGrow="1" justifyContent="space-between">
          <Button text="Back" variant="gray" onClick={history.goBack} />
          <ReportActionButton
            data={data}
            isShowing={!isReportInTrash}
            isRateFormConfirmed={data.report.isConfirmed}
            role={userInfo.role}
            handleOnUpdateStatus={handleOnUpdateStatus}
            handleOnAddToPortfolio={handleOnAddToPortfolio}
            onNotifyAdminShowModal={(notificationType) =>
              onNotifyAdminShowModal(
                getNotificationAction(data.reportType, notificationType),
                upperCaseReportType,
                data.id,
                dispatch
              )
            }
          />
        </FlexLayout>
      </PageLayout>
      <DeleteModal
        item="report"
        isShowing={showDeleteModal}
        isDeletePermanent={isReportInTrash}
        handleOnDeleteClick={() => handleOnItemDelete({ id: data?.id, force: isReportInTrash })}
        handleOnHide={() => setShowDeleteModal(false)}
        dataTestId="confirmDeleteReportButton"
      />
      <RestoreModal
        item="report"
        isShowing={showRestoreModal}
        handleOnRestoreClick={() => handleOnItemRestore(data?.id)}
        handleOnHide={() => setShowRestoreModal(false)}
        dataTestId="confirmDeleteReportButton"
      />
      <SideBySideCalculationModal
        isShowing={showSideBySideModal}
        originalData={data}
        updatedData={updatedAlgorithmData}
        title="Summary report"
        onHide={() => setShowSideBySideModal(false)}
        onOriginalClick={() => onSideBySideReportPick(data, ORIGINAL)}
        onUpdatedClick={() => onSideBySideReportPick(updatedAlgorithmData, UPDATED)}
      />
      <ConfirmModal
        buttonText="Confirm"
        data={data}
        isButtonDisabled={!data.report?.finalInterestRate?.toString()?.length}
        isShowing={confirmInterestRateData}
        onClick={handleMoveToPortfolioAfterInterestConfirm}
        onHide={() => setConfirmInterestRateData(null)}
        title={getConfirmModalTitle()}
        additionalInfo={getConfirmModalAdditionalInfo(isLoan)}
        updateField={updateField}
      />
    </>
  );
}

export default ReportView;
