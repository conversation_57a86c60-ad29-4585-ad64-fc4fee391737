import _ from 'lodash';

import {
  getGuaranteeCalculatedPaymentsInterest,
  getLoanCalculatedPaymentsInterest,
  getB2BLoanCalculatedPaymentsInterest,
  getWhtRates,
} from '~/api';
import { reportEnum, whtEnums } from '~/enums';
import { errorHandler } from '~/utils/errors';

export const calculateExpenseSummary = async ({ reportData, rates }) => {
  const isLoan = reportData.reportType === reportEnum.LOAN;
  const isBackToBackLoan = reportData.reportType === reportEnum.BACK_TO_BACK_LOAN;
  const isGuarantee = reportData.reportType === reportEnum.GUARANTEE;
  const isFixed = reportData.rateType?.type === 'fixed' || isGuarantee;

  if (!reportData.reportType) return;
  if (isFixed && rates.finalInterestRate == null) return;
  if (!isFixed && (rates.finalInterestRate == null || rates.estimationOfReferenceRate == null)) return;

  const getCalculatedPaymentsInterestApiMethodMapper = {
    [reportEnum.LOAN]: getLoanCalculatedPaymentsInterest,
    [reportEnum.BACK_TO_BACK_LOAN]: getB2BLoanCalculatedPaymentsInterest,
    [reportEnum.GUARANTEE]: getGuaranteeCalculatedPaymentsInterest,
  };
  const getCalculatedPaymentsInterest = getCalculatedPaymentsInterestApiMethodMapper[reportData.reportType];

  const { finalInterestRate, estimationOfReferenceRate, whtInterestRate, isWhtEnabled, approach } = rates;

  try {
    const calculatedPayments = await getCalculatedPaymentsInterest({
      id: reportData.id,
      data: { ...rates, basisPoints: rates.finalInterestRate },
    });

    if ((isLoan || isBackToBackLoan) && isWhtEnabled && approach === whtEnums.WHT_APPROACHES.GROSS_UP) {
      const interestRateForGrossedUp = isFixed
        ? finalInterestRate
        : estimationOfReferenceRate + finalInterestRate / 100;

      const grossedUpInterestRate =
        (1 + whtInterestRate / 100 / (1 - whtInterestRate / 100)) * interestRateForGrossedUp;

      calculatedPayments.grossedUpInterestRate = grossedUpInterestRate;
    }

    return calculatedPayments;
  } catch (err) {
    errorHandler(err);
  }
};

export const handleWhtRateSetup = async ({ isGuarantee, report, lender, borrower }) => {
  if (isGuarantee) return;

  try {
    const whtRatesResponse = await getWhtRates({ origin: lender?.country, recipient: borrower?.country });
    let newWhtRateOptions = [];
    if (whtRatesResponse?.whtData?.interest) {
      const defaultInterestRate = whtRatesResponse.isDefault ? '(Headline)' : '(Treaty)';
      newWhtRateOptions = whtRatesResponse.whtData.interest
        .split('/')
        .map((rate) => {
          // Rate doesn't need to be a number. It can be for example `D`. Such values are discarded.
          if (rate == null || rate === '' || Number.isNaN(Number(rate))) return null;
          return { label: `${rate}% ${defaultInterestRate}`, value: rate };
        })
        .filter(Boolean);
    }

    // Add current wht interest rate (if there is any) as option if it is not among ones that we got from backend
    if (
      _.isFinite(Number(report.whtInterestRate)) &&
      !newWhtRateOptions.find((option) => option.value === report.whtInterestRate?.toString())
    ) {
      const oldOption = { label: `${report.whtInterestRate}% (Custom)`, value: `${report.whtInterestRate}` };
      newWhtRateOptions.push(oldOption);
    }

    return newWhtRateOptions;
  } catch (err) {
    errorHandler(err);
  }
};

export const getWhtCheckboxTooltip = (isLoanBetweenSameCountries) => {
  if (isLoanBetweenSameCountries) {
    return 'Withholding tax does not apply to loans between entities that are resident in the same country.';
  }
  return "The withholding tax rates might not be current.<br />It's advisable to review your double-tax treaty for verification.";
};

export const approachesTooltip = `
Approach I - This approach has the Borrower paying the withholding tax to<br />
their own government but on behalf of the Lender (i.e., the Lender owes the tax to the Borrower's government)
<br /><br />
Approach I is not allowed in some countries. <br />
Approach II adjusts the rate upwards so that the Lender still receives their full interest expense.
<br />
Note: the Borrower still pays to its own government on behalf of the Lender.
`;

export const estOfRefRateTooltip =
  'Estimated reference rate is used for the graph visualization only and not for calculation of actual payments.';
