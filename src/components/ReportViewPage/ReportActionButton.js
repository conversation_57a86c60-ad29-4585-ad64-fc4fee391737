import { WithTooltip } from '~/ui/hocs';
import { rolesEnum, reportEnum } from '~/enums';
import { Button } from '~/ui';

import {
  wouldNotGuaranteeBeAgreedAtArmsLength,
  getActionButtonNotificationAction,
  isAddToPortfolioDisabled,
} from './ReportView.utils';

const ReportActionButton = ({
  isShowing,
  isRateFormConfirmed,
  data,
  role,
  handleOnUpdateStatus,
  handleOnAddToPortfolio,
  onNotifyAdminShowModal,
}) => {
  const isLoan = data.reportType === reportEnum.LOAN;
  if (!isShowing) return null;

  const getTooltip = () => {
    if (wouldNotGuaranteeBeAgreedAtArmsLength(data)) return "Guarantee would not be agreed at arm's length";
    return `Input ${isLoan ? 'interest rate' : 'guarantee fee'} before moving to Portfolio`;
  };

  if (data.status !== 'Draft') return null;

  if (role === rolesEnum.USER) {
    const action = getActionButtonNotificationAction(data);
    return <Button text="Notify admin" onClick={onNotifyAdminShowModal(action)} />;
  }

  return (
    <>
      {data.isPortfolio ? (
        <WithTooltip
          tooltip="All documents need to be finalized."
          label="FinalizeReport"
          disabled={data.files.length !== 0 && data.files.some((f) => f.status === 'Draft')}
        >
          <Button
            disabled={data.files.length !== 0 && data.files.some((f) => f.status === 'Draft')}
            text="Finalize"
            variant="primary"
            onClick={() => handleOnUpdateStatus('Final')}
          />
        </WithTooltip>
      ) : (
        <WithTooltip
          label="Disabled add to portfolio"
          tooltip={getTooltip()}
          disabled={isAddToPortfolioDisabled(data, isRateFormConfirmed) || data.isSubmitting}
        >
          <Button
            text="Add to Portfolio"
            variant="primary"
            disabled={isAddToPortfolioDisabled(data, isRateFormConfirmed) || data.isSubmitting}
            onClick={handleOnAddToPortfolio}
            loading={data.isSubmitting}
          />
        </WithTooltip>
      )}
    </>
  );
};

export default ReportActionButton;
