import { useContext, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { UserInfoContext } from '~/context/user';
import { reportEnum } from '~/enums';
import { report } from '~/reducers/report.slice';
import { Card, FlexLayout, Text, WithTooltip } from '~/ui';
import { isParentCompany } from '~/utils/companies';
import * as stringUtils from '~/utils/strings';

import BoundsGraph from './BoundsGraph';
import {
  getBoundsReportData,
  getGuaranteeCalculationResultRenderValues,
  getLoanCalculationResultRenderValues,
} from './CalculationResults.utils';

function CalculationResults() {
  const data = useSelector(report);
  const { userInfo } = useContext(UserInfoContext);
  const [subTitle, setSubTitle] = useState({ value: '', color: 'shakespeare' });
  const [description, setDescription] = useState('');
  const [unit, multiplier] = data.rateType?.type === 'fixed' ? ['%', 100] : ['bps', 10000];

  const riskTaker = data.borrowers?.find((borrower) => borrower.id === data.riskTakerId);
  const remuneration = data.capm?.requiredRateOfReturn * data.expectedLoss?.expectedLoss;

  useEffect(() => {
    if (data?.reportType === reportEnum.LOAN) {
      const renderValues = getLoanCalculationResultRenderValues(data?.pricingApproach, isParentCompany(data?.lender));
      setSubTitle((prevState) => ({ ...prevState, value: renderValues?.subTitle }));
      setDescription(renderValues?.description);
    }
    if (data?.reportType === reportEnum.BACK_TO_BACK_LOAN) {
      const ultimateLender = data.ultimateLender;
      const renderValues = getLoanCalculationResultRenderValues(data?.pricingApproach, isParentCompany(ultimateLender));
      setSubTitle((prevState) => ({ ...prevState, value: renderValues?.subTitle }));
      setDescription(renderValues?.description);
    }
    if (data?.reportType === reportEnum.GUARANTEE) {
      const renderValues = getGuaranteeCalculationResultRenderValues(data?.pricingApproach, data?.report);
      setSubTitle({ value: renderValues?.subTitle, color: renderValues?.subTitleColor });
      setDescription(renderValues?.description);
    }
  }, [data?.lender, data?.report, data?.pricingApproach, data?.reportType, data.ultimateLender]);

  return (
    <Card title="Calculation Results" py={6} space={4} sx={{ flexBasis: '30%' }}>
      <FlexLayout flexDirection="column" flexGrow="1" space={4}>
        <Text color={subTitle?.color} variant="m-spaced-medium">
          {subTitle?.value}
        </Text>
        <Text color="bali-hai" variant="m-spaced" sx={{ lineHeight: '21px' }}>
          {description}
        </Text>
        <BoundsGraph data={getBoundsReportData(data, userInfo)} />
        {data?.reportType === reportEnum.BACK_TO_BACK_LOAN && (
          <FlexLayout bg="alabaster" px={4} py={2} sx={{ border: 'border', borderRadius: 'm' }}>
            <WithTooltip disabled label="RiskTakerRemuneration" tooltip={`${riskTaker?.name} is the risk taker`}>
              <Text color="deep-sapphire" variant="s-spaced">
                {riskTaker?.name} pass through remuneration:
              </Text>{' '}
              <Text color="deep-sapphire" variant="m-spaced-medium">
                {stringUtils.displayNumber2(remuneration * multiplier, { decimalPoint: userInfo.decimalPoint, unit })}
              </Text>
            </WithTooltip>
          </FlexLayout>
        )}
      </FlexLayout>
    </Card>
  );
}

export default CalculationResults;
