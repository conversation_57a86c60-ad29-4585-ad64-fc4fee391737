import { useState, useContext, useEffect, useRef } from 'react';
import { Bar } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, LogarithmicScale, BarElement, Tooltip, Legend } from 'chart.js';

import { reportEnum, expenseSummaryTabsEnum } from '~/enums';
import { FlexLayout, SingleSelect, Text, Box, Tabs } from '~/ui';
import { displayNumber2 } from '~/utils/strings';
import { errorHandler } from '~/utils/errors';
import { UserInfoContext } from '~/context/user';
import { genericTabSetter } from '~/utils/tabs';

import * as ratesCardUtils from '../RatesCard.utils';
import * as expenseSummaryGraphUtils from './ExpenseSummaryGraph.utils';

ChartJS.register(CategoryScale, LinearScale, LogarithmicScale, Tooltip, Legend, BarElement);

const ExpenseSummaryGraph = ({ isShowing, data, expenseSummaryCalculation, setExpenseSummaryCalculation }) => {
  const { userInfo } = useContext(UserInfoContext);
  const [tabs, setTabs] = useState([]);
  const [selectedTab, setSelectedTab] = useState(null);
  const [selectedLeg, setSelectedLeg] = useState(null);
  const chartRef = useRef(null);

  const numberDisplayOptions = { decimalPoint: userInfo.decimalPoint };
  const isGuarantee = data?.reportType === reportEnum.GUARANTEE;
  const isBackToBackLoan = data?.reportType === reportEnum.BACK_TO_BACK_LOAN;
  const legs = data?.legs?.map((leg) => ({ label: `${leg.borrower.name} - ${leg.lender.name}`, value: leg }));

  useEffect(() => {
    const tabs = [
      { isEnabled: true, label: 'TOTAL', value: expenseSummaryTabsEnum.TOTAL },
      { isEnabled: true, label: isGuarantee ? 'FEE' : 'INTEREST', value: expenseSummaryTabsEnum.INTEREST },
      { isEnabled: true, label: 'PRINCIPAL', value: expenseSummaryTabsEnum.PRINCIPAL },
      { isEnabled: expenseSummaryCalculation?.isWhtEnabled, label: 'WHT', value: expenseSummaryTabsEnum.WHT },
    ];
    genericTabSetter(setTabs, setSelectedTab, tabs);
  }, [expenseSummaryCalculation?.isWhtEnabled, isGuarantee]);

  const onSelectLeg = async (selectedLeg) => {
    try {
      const rates = { ...selectedLeg?.report, estimationOfReferenceRate: data.report.estimationOfReferenceRate };
      const expenseSummary = await ratesCardUtils.calculateExpenseSummary({ reportData: data, rates });
      setExpenseSummaryCalculation(expenseSummary);

      setSelectedLeg(legs.find((leg) => leg.value.id === selectedLeg.id));
    } catch (err) {
      errorHandler(err);
    }
  };

  if (!isShowing || !expenseSummaryCalculation) return null;

  return (
    <>
      <FlexLayout justifyContent="space-between">
        <Tabs
          selectedTab={selectedTab}
          tabs={tabs}
          onTabSelect={(tab) => {
            chartRef?.current?.reset(); // we do this to avoid weird graph animations when changing tabs
            setSelectedTab(tab);
          }}
          minWidth="tab-width-xs"
          height="tab-height-small"
          textVariant="xs-spaced"
          colorSelected="shakespeare"
        />
        <SingleSelect
          placeholder="Select a leg"
          isShowing={isBackToBackLoan}
          width="l"
          height="tab-height-small"
          options={legs}
          value={selectedLeg?.value}
          onChange={onSelectLeg}
        />
      </FlexLayout>
      <Box sx={{ height: '300px' }}>
        <Bar
          options={expenseSummaryGraphUtils.getGraphOptions({ data, selectedTab, numberDisplayOptions })}
          data={expenseSummaryGraphUtils.getGraphData({ data, expenseSummaryCalculation, selectedTab })}
          ref={chartRef}
        />
      </Box>
      <FlexLayout>
        <FlexLayout space={4} alignItems="flex-end" flexGrow="1" flexWrap="wrap">
          {!isGuarantee && (
            <FlexLayout>
              <Text
                color="bali-hai"
                variant="xs-spaced-medium"
                sx={{ border: 'border', borderRadius: 'm', padding: '4px 8px' }}
              >
                TOTAL EXPENSE:
                <Text color="deep-sapphire" variant="xs-spaced-medium" sx={{ marginLeft: '4px' }}>
                  {data.currency}{' '}
                  {displayNumber2(data.amount + expenseSummaryCalculation.totalInterest, numberDisplayOptions)}
                </Text>
              </Text>
            </FlexLayout>
          )}
          <FlexLayout>
            <Text
              color="bali-hai"
              variant="xs-spaced-medium"
              p={1}
              sx={{ border: 'border', borderRadius: 'm', padding: '4px 8px' }}
            >
              {isGuarantee ? 'TOTAL FEE' : 'TOTAL INTEREST'}
              <Text color="deep-sapphire" variant="xs-spaced-medium" sx={{ marginLeft: '4px' }}>
                {data.currency} {displayNumber2(expenseSummaryCalculation.totalInterest, numberDisplayOptions)}
              </Text>
            </Text>
          </FlexLayout>
          {expenseSummaryCalculation.isWhtEnabled && expenseSummaryCalculation.grossedUpInterestRate && (
            <FlexLayout>
              <Text
                color="bali-hai"
                variant="xs-spaced-medium"
                p={1}
                sx={{ border: 'border', borderRadius: 'm', padding: '4px 8px' }}
              >
                GROSSED-UP INTEREST RATE
                <Text color="deep-sapphire" variant="xs-spaced-medium" sx={{ marginLeft: '4px' }}>
                  {displayNumber2(expenseSummaryCalculation.grossedUpInterestRate, {
                    ...numberDisplayOptions,
                    unit: '%',
                  })}
                </Text>
              </Text>
            </FlexLayout>
          )}
        </FlexLayout>
      </FlexLayout>
    </>
  );
};

export default ExpenseSummaryGraph;
