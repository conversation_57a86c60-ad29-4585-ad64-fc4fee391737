import _ from 'lodash';

import { displayNumber2 } from '~/utils/strings';
import { roundToXDecimals } from '~/utils/numbers';
import { reportEnum, expenseSummaryTabsEnum } from '~/enums';

export const getGraphData = ({ data, expenseSummaryCalculation, selectedTab }) => {
  const isLoan = data?.reportType === reportEnum.LOAN;
  const isBackToBackLoan = data?.reportType === reportEnum.BACK_TO_BACK_LOAN;
  const isGuarantee = data?.reportType === reportEnum.GUARANTEE;

  if (!expenseSummaryCalculation) return;

  const labels = Object.keys(expenseSummaryCalculation.interestPerYear);
  const interestData = Object.values(expenseSummaryCalculation.interestPerYear).map((i) => roundToXDecimals(i));
  const whtInterestData =
    (isLoan || isBackToBackLoan) &&
    expenseSummaryCalculation.isWhtEnabled &&
    Object.values(expenseSummaryCalculation.whtInterestPerYear).map((i) => roundToXDecimals(i));

  const principalData = _.fill(Array(interestData.length), 0);
  principalData[principalData.length - 1] = roundToXDecimals(data.amount);

  const datasets = [];
  if (selectedTab === expenseSummaryTabsEnum.WHT || selectedTab === expenseSummaryTabsEnum.TOTAL) {
    datasets.push({ label: 'WHT', backgroundColor: '#8797AC', data: whtInterestData, stack: 'principalAndInterest' });
  }
  if (selectedTab === expenseSummaryTabsEnum.INTEREST || selectedTab === expenseSummaryTabsEnum.TOTAL) {
    datasets.push({
      label: isGuarantee ? 'Fee' : 'Interest',
      backgroundColor: '#12246C',
      data: interestData,
      stack: 'principalAndInterest',
    });
  }
  if (selectedTab === expenseSummaryTabsEnum.PRINCIPAL || selectedTab === expenseSummaryTabsEnum.TOTAL) {
    datasets.push({
      label: 'Principal',
      backgroundColor: '#48a6c9',
      data: principalData,
      stack: 'principalAndInterest',
    });
  }

  return { labels, datasets };
};

const getYAxisType = ({ data, isBalloon, selectedTab, expenseSummaryTabsEnum }) => {
  // negative values aren't allowed in logarithmic type so we set it to linear if the input is negative
  if (data.report.finalInterestRate < 0 || data.report.floatingInterestRate < 0) {
    return 'linear';
  }
  if (isBalloon) return 'linear';
  if (selectedTab === expenseSummaryTabsEnum.TOTAL) return 'logarithmic';

  return 'linear';
};

export const getGraphOptions = ({ data, selectedTab, numberDisplayOptions }) => {
  const isBalloon = data?.type === 'Balloon';

  return {
    maintainAspectRatio: false,
    plugins: { legend: { display: false } },
    scales: {
      yAxes: {
        id: 'y-axis-linear',
        type: getYAxisType({ data, isBalloon, selectedTab, expenseSummaryTabsEnum }),
        ticks: {
          maxTicksLimit: 8,
          callback: (value) => displayNumber2(value, numberDisplayOptions),
        },
      },
    },
    tooltips: {
      callbacks: {
        label: (tooltipItem, data) => {
          const { label = '' } = data.datasets[tooltipItem.datasetIndex];
          return `${label}: ${displayNumber2(tooltipItem.yLabel, numberDisplayOptions)}`;
        },
      },
    },
  };
};
