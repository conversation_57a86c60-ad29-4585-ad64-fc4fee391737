import { useState } from 'react';
import { OptionContext, SingleValue } from 'react-select';

import { CreatableSingleSelect } from 'ui';
import { SingleSelectWidthType, CreatableSelectOptionType, HeightType } from 'types';

type WHTCreatableSingleSelectProps = {
  label?: string;
  tooltip?: string;
  dataTestId?: string;
  disabled?: boolean;
  sx?: any;
  width?: SingleSelectWidthType;
  height?: HeightType;
  bg?: string;
  options: {
    label: string;
    value: string | number;
  }[];
  setWhtRateOptions: React.Dispatch<
    React.SetStateAction<
      {
        label: string;
        value: string | number;
      }[]
    >
  >;
  value: string;
  onChange: Function;
};

function WHTCreatableSingleSelect({
  label = 'WHT Rate',
  tooltip = 'Withholding tax rate.<br />Start typing to enter an arbitrary rate.',
  dataTestId,
  disabled = false,
  sx,
  width = 's',
  height,
  bg = 'transparent',
  options,
  setWhtRateOptions,
  value,
  onChange,
}: WHTCreatableSingleSelectProps) {
  const [errorMessage, setErrorMessage] = useState('');

  /** Adds new option if not in options already or just calls onChange if existing option is chosen */
  const onChangeHandler = (selectedOption: SingleValue<CreatableSelectOptionType>) => {
    setErrorMessage('');
    if (Number.isNaN(Number(selectedOption?.value))) {
      return setErrorMessage('WHT rate must be a number.');
    }

    onChange(selectedOption?.value);
    if (selectedOption?.__isNew__ && !options.find((o) => o.value === selectedOption.value)) {
      setWhtRateOptions([...options, { label: `${selectedOption.label}% (Custom)`, value: selectedOption.value }]);
    }
  };

  const formatLabel = (option: CreatableSelectOptionType, { context }: { context: OptionContext }) => {
    // Show whole label in dropdown menu, show only value% when selected
    return context === 'menu' ? option.label : `${option.value}%`;
  };

  return (
    <CreatableSingleSelect
      label={label}
      // hides tooltip when disabled
      tooltip={!disabled && tooltip}
      dataTestId={dataTestId}
      error={errorMessage}
      disabled={disabled}
      sx={sx}
      width={width}
      height={height}
      bg={bg}
      options={options}
      value={value}
      onChangeHandler={onChangeHandler}
      formatCreateLabel={(value: string) => `Use ${value}%`}
      formatOptionLabel={formatLabel}
    />
  );
}

export default WHTCreatableSingleSelect;
