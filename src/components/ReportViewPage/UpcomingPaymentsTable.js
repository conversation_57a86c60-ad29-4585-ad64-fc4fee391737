import React from 'react';

import { ReportsCard } from '~/components/Shared';
import { reportEnum } from '~/enums';
import { FlexLayout } from '~/ui';

import RepaymentTable from './RepaymentTable';

const UpcomingPaymentsTable = ({ reportType, reportPayments }) => (
  <FlexLayout flexDirection="column" space={6}>
    <ReportsCard
      title={reportType === reportEnum.LOAN ? 'Upcoming Loan Interest' : 'Upcoming Guarantee Interest'}
      table={<RepaymentTable data={reportPayments} reportType={reportType} />}
    />
  </FlexLayout>
);

export default UpcomingPaymentsTable;
