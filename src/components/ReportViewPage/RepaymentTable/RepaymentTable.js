import React, { useContext } from 'react';
import { useDispatch } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { UserInfoContext } from '~/context/user';
import { REPORT_TYPE } from '~/enums';
import { resetPayment, updateField } from '~/reducers/payment.slice';
import { routesEnum } from '~/routes';
import { FlexLayout, Table } from '~/ui';

import { getColumnData, getColumns, getLastPayment } from './RepaymentTable.utils';

function RepaymentTable({ data = [], reportType }) {
  const dispatch = useDispatch();
  const history = useHistory();
  const { userInfo } = useContext(UserInfoContext);
  const isBullet = !!data[0]?.bulletPayment;
  const isReportFinalized = (data[0]?.loan?.status || data[0]?.guarantee?.status) === 'Final';

  const arrayWithLastPayment = getLastPayment(data);
  const columnData = isBullet ? data : arrayWithLastPayment;

  const onItemClick = ({ reportId }) => {
    dispatch(resetPayment());
    dispatch(updateField({ [`${reportType}Id`]: reportId }));
    history.push(`${routesEnum.PAYMENTS}?${REPORT_TYPE}=${reportType}`);
  };

  return (
    <FlexLayout flexDirection="column">
      <Table
        columns={getColumns(reportType)}
        data={getColumnData(columnData, userInfo)}
        onItemClick={isReportFinalized ? onItemClick : null}
      />
    </FlexLayout>
  );
}

export default RepaymentTable;
