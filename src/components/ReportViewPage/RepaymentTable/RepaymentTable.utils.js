import React from 'react';

import { ThreeDotActionMenu } from '~/components/Shared';
import { isAdmin, reportEnum } from '~/enums';
import { formatDateString } from '~/utils/dates';
import { getInterestPayment, getOrdinalPayment } from '~/utils/payments';
import { displayNumber } from '~/utils/strings';

function getData(
  {
    bulletPayment,
    balloonPayment,
    paymentDueDate,
    loan,
    guarantee,
    id,
    isPaid,
    totalNumberOfPayments,
    ordinal,
    paymentAmount,
  },
  userInfo
) {
  const report = loan || guarantee;
  const payment = bulletPayment || balloonPayment;
  const isBullet = !!bulletPayment;
  const isLastPayment = ordinal === totalNumberOfPayments;

  const interestPayment = getInterestPayment(payment, isBullet);
  const ordinalPayments = getOrdinalPayment(isBullet, ordinal, totalNumberOfPayments);

  return {
    interestPayment: `${report.currency} ${displayNumber(interestPayment, userInfo.decimalPoint)}`,
    paymentDueDate: formatDateString(paymentDueDate, userInfo.dateFormat),
    paymentAmount: `${report.currency} ${displayNumber(paymentAmount, userInfo.decimalPoint)}`,
    principalRepayment: isLastPayment
      ? `${report.currency} ${displayNumber(report.amount, userInfo.decimalPoint)}`
      : '-',
    compoundingDate: formatDateString(payment.compoundingPeriodEndDate, userInfo.dateFormat),
    ordinalPayments,
    reportId: report.id,
    paymentId: id,
    isPaid,
    isBullet,
    isLastPayment,
  };
}

export function getColumnData(data = [], userInfo) {
  return data.map((item) => getData(item, userInfo));
}

export function getColumns(reportType) {
  const sharedColumns = [
    { label: 'Interest number', sortBy: 'ordinalPayments', value: 'ordinalPayments', width: 200 },
    { label: 'Interest date', sortBy: 'paymentDueDate', value: 'paymentDueDate' },
  ];

  if (reportType === reportEnum.LOAN) {
    return [
      ...sharedColumns,
      {
        label: 'Interest',
        sortBy: 'interestPayment',
        value: 'interestPayment',
        justifyContent: 'flex-end',
      },
      {
        label: 'Principal repayment',
        sortBy: 'principalRepayment',
        value: 'principalRepayment',
        justifyContent: 'flex-end',
      },
      { label: 'Interest amount', sortBy: 'paymentAmount', value: 'paymentAmount', justifyContent: 'flex-end' },
    ];
  }

  return [
    ...sharedColumns,
    {
      label: 'Guarantee Fee',
      sortBy: 'paymentAmount',
      value: 'paymentAmount',
      justifyContent: 'flex-end',
    },
  ];
}

export const renderTableActionColumn = (item, onMarkReportPaymentAsPaid, onNotifyAdminShowModal, role) => {
  const { paymentId } = item;
  const options = [];

  if (isAdmin(role)) {
    options.push({
      label: 'Publish',
      onClick: () => onMarkReportPaymentAsPaid(paymentId),
    });
  } else {
    options.push({
      label: 'Notify admin to publish',
      onClick: () => onNotifyAdminShowModal(item),
    });
  }

  return <ThreeDotActionMenu options={options} />;
};

/**
 * Returns an array with the last payment or an empty array
 * Used to show only last payment in portfolio for balloon type
 */
export const getLastPayment = (data) => {
  const lastPayment = data[data.length - 1];
  const lastPaymentArray = lastPayment ? [lastPayment] : [];
  return lastPayment?.ordinal === lastPayment?.totalNumberOfPayments ? lastPaymentArray : [];
};
