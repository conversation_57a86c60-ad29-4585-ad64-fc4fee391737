import { useState } from 'react';
import { useHistory } from 'react-router-dom';

import {
  deleteCreditRating,
  restoreCreditRating,
  deleteGuarantee,
  restoreGuarantee,
  deleteLoan,
  deleteB2BLoan,
  restoreB2BLoan,
  updateB2BLoanStatus,
  updateB2<PERSON>oanNote,
  updateB2BLoanIsPortfolio,
  restoreLoan,
  updateCreditRatingIsPortfolio,
  updateCreditRatingNote,
  updateCreditRatingStatus,
  updateGuaranteeIsPortfolio,
  updateGuaranteeNote,
  updateGuaranteeStatus,
  updateLoanIsPortfolio,
  updateLoanNote,
  updateLoanStatus,
} from '~/api';
import { MovedModal } from '~/components/Modals';
import { REPORT_TYPE, reportEnum } from '~/enums';
import { useQuery } from '~/hooks';
import { routesEnum } from '~/routes';
import { showToast, showErrorToast } from '~/ui/components/Toast';
import { capitalize } from '~/utils/strings';
import { errorHandler } from '~/utils/errors';

import CreditRatingView from './CreditRatingView';
import ReportView from './ReportView';

function ReportViewPage() {
  const [isReportMovedModalOpen, setIsReportMovedModalOpen] = useState(false);
  const history = useHistory();
  const query = useQuery();

  const reportType = query.get(REPORT_TYPE) || reportEnum.LOAN;

  const apis = {
    [reportEnum.LOAN]: {
      delete: deleteLoan,
      restore: restoreLoan,
      updateStatus: updateLoanStatus,
      updateNote: updateLoanNote,
      updateIsPortfolio: updateLoanIsPortfolio,
    },
    [reportEnum.BACK_TO_BACK_LOAN]: {
      delete: deleteB2BLoan,
      restore: restoreB2BLoan,
      updateStatus: updateB2BLoanStatus,
      updateNote: updateB2BLoanNote,
      updateIsPortfolio: updateB2BLoanIsPortfolio,
    },
    [reportEnum.GUARANTEE]: {
      delete: deleteGuarantee,
      restore: restoreGuarantee,
      updateStatus: updateGuaranteeStatus,
      updateNote: updateGuaranteeNote,
      updateIsPortfolio: updateGuaranteeIsPortfolio,
    },
    [reportEnum.CREDIT_RATING]: {
      delete: deleteCreditRating,
      restore: restoreCreditRating,
      updateStatus: updateCreditRatingStatus,
      updateNote: updateCreditRatingNote,
      updateIsPortfolio: updateCreditRatingIsPortfolio,
    },
  };

  function handleOnItemDelete({ id, force }) {
    const deleteItem = apis[reportType].delete;

    deleteItem({ id, force })
      .then(() => {
        history.goBack();
        showToast(`${capitalize(reportType)} has been successfully deleted.`);
      })
      .catch(errorHandler);
  }

  function handleOnItemRestore(id) {
    const restoreItem = apis[reportType].restore;

    restoreItem(id)
      .then(() => {
        history.goBack();
        showToast(`${capitalize(reportType)} has been successfully restored.`);
      })
      .catch(errorHandler);
  }

  function handleUpdateIsPortfolio(id, data) {
    const updateIsPortfolio = apis[reportType].updateIsPortfolio;

    updateIsPortfolio({ id, data })
      .then(() => {
        showToast(`${capitalize(reportType)} has been added to ${data?.isPortfolio ? 'Portfolio' : 'Analysis'}.`);
        history.replace(`/${data?.isPortfolio ? 'portfolio' : 'analyses'}/${id}?${REPORT_TYPE}=${reportType}`);
      })
      .catch((err) => showErrorToast(err?.response?.data?.message));
  }

  function handleUpdateStatus(id, data, cb) {
    const updateStatus = apis[reportType].updateStatus;

    updateStatus({ id, data })
      .then((resData) => {
        cb(resData);
        showToast(`${capitalize(reportType)} has been marked as ${data?.status}.`);
      })
      .catch((err) => {
        if (err?.response?.status >= 500) return showErrorToast();
        showErrorToast(err?.response?.data?.message);
      });
  }

  function handleSaveNoteClick(id, data, cb) {
    const updateNote = apis[reportType].updateNote;

    updateNote({ id, data })
      .then((resData) => {
        cb(resData);
        showToast('Note has been successfully updated.');
      })
      .catch(errorHandler);
  }

  return (
    <>
      {reportType === reportEnum.CREDIT_RATING ? (
        <CreditRatingView
          handleOnItemDelete={handleOnItemDelete}
          handleOnItemRestore={handleOnItemRestore}
          handleUpdateIsPortfolio={handleUpdateIsPortfolio}
          handleUpdateStatus={handleUpdateStatus}
          handleSaveNoteClick={handleSaveNoteClick}
          setIsReportMovedModalOpen={setIsReportMovedModalOpen}
        />
      ) : (
        <ReportView
          handleOnItemDelete={handleOnItemDelete}
          handleOnItemRestore={handleOnItemRestore}
          handleUpdateIsPortfolio={handleUpdateIsPortfolio}
          handleUpdateStatus={handleUpdateStatus}
          handleSaveNoteClick={handleSaveNoteClick}
          setIsReportMovedModalOpen={setIsReportMovedModalOpen}
        />
      )}
      <MovedModal
        title="The report was moved or doesn't exist anymore"
        handleReportMovedClick={() => history.replace(routesEnum.DASHBOARD)}
        isShowing={isReportMovedModalOpen}
      />
    </>
  );
}

export default ReportViewPage;
