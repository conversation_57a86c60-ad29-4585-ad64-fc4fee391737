import { useCallback, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import _ from 'lodash';

import { updateB2BLoanLegRates } from '~/api';
import { Button, Checkbox, FlexLayout, Modal, RadioGroup, Text } from '~/ui';
import { whtEnums } from '~/enums';
import { updateField as updateFieldReport } from '~/reducers/report.slice';
import { showToast } from '~/ui/components/Toast';
import { WithTooltip } from '~/ui/hocs';

import WHTCreatableSingleSelect from './WHTCreatableSingleSelect';
import * as ratesCardUtils from './RatesCard.utils';

const B2BLoanWHTModal = ({ isShowing, data, onHide }) => {
  const dispatch = useDispatch();

  const onChangeIsWhtEnabled = async (leg, isActive) => {
    const { report, lender, borrower } = leg;
    const whtOptions = await ratesCardUtils.handleWhtRateSetup({ isGuarantee: false, report, lender, borrower });
    const legsDeepClone = _.cloneDeep(data.legs);
    for (const currentLoopLeg of legsDeepClone) {
      if (currentLoopLeg.id === leg.id) {
        currentLoopLeg.report.isWhtEnabled = isActive;
        currentLoopLeg.whtOptions = whtOptions;
        break;
      }
    }

    dispatch(updateFieldReport({ legs: legsDeepClone }));
  };

  const onChangeApproach = (leg, approach) => {
    const legsDeepClone = _.cloneDeep(data.legs);
    for (const currentLoopLeg of legsDeepClone) {
      if (currentLoopLeg.id === leg.id) {
        currentLoopLeg.report.approach = approach;
        break;
      }
    }
    dispatch(updateFieldReport({ legs: legsDeepClone }));
  };

  const onChangeWhtInterestRate = (leg, whtInterestRate) => {
    const legsDeepClone = _.cloneDeep(data.legs);
    for (const currentLoopLeg of legsDeepClone) {
      if (currentLoopLeg.id === leg.id) {
        currentLoopLeg.report.whtInterestRate = whtInterestRate;
        break;
      }
    }

    dispatch(updateFieldReport({ legs: legsDeepClone }));
  };

  const isSaveDisabled = () => {
    for (const leg of data.legs) {
      if (leg.report.isWhtEnabled && (!leg.report.approach || leg.report.whtInterestRate == null)) {
        return true;
      }
    }

    return false;
  };

  const onSave = async () => {
    await updateB2BLoanLegRates({ id: data.id, data: { legs: data.legs } });
    showToast('Withholding tax updated');
    onHide();
  };

  const setWhtRateOptions = (leg, newOptions) => {
    const legsDeepClone = _.cloneDeep(data.legs);
    for (const currentLoopLeg of legsDeepClone) {
      if (currentLoopLeg.id === leg.id) {
        currentLoopLeg.whtOptions = newOptions;
        currentLoopLeg.report.whtInterestRate = newOptions[newOptions.length - 1].value;
        break;
      }
    }

    dispatch(updateFieldReport({ legs: legsDeepClone }));
  };

  const setInitialWht = useCallback(async () => {
    if (!isShowing) return;

    const legsDeepClone = _.cloneDeep(data.legs);
    for (const currentLoopLeg of legsDeepClone) {
      const { report, lender, borrower } = currentLoopLeg;
      currentLoopLeg.whtOptions = await ratesCardUtils.handleWhtRateSetup({
        isGuarantee: false,
        report,
        lender,
        borrower,
      });
    }
    dispatch(updateFieldReport({ legs: legsDeepClone }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isShowing, dispatch]);

  useEffect(() => {
    setInitialWht();
  }, [setInitialWht]);

  if (!isShowing) return null;

  return (
    <Modal onHide={onHide} actionButtons={<Button disabled={isSaveDisabled()} text="Save" size="l" onClick={onSave} />}>
      {data.legs.map((leg, index) => {
        const isLoanBetweenSameCountries = leg.borrower.country === leg.lender.country;

        return (
          <FlexLayout
            key={index}
            flexDirection="column"
            sx={
              data.legs.length - 1 !== index ? { borderBottom: 'border-dashed-link-water', paddingBottom: '24px' } : {}
            }
          >
            <FlexLayout alignItems="center" sx={{ gap: 6, marginBottom: '16px !important' }}>
              <Text color="deep-sapphire">
                {leg.borrower.name} - {leg.lender.name}
              </Text>
              <WithTooltip
                disabled={true}
                label={`whtB2BLoanCheckbox-${index}`}
                tooltip={ratesCardUtils.getWhtCheckboxTooltip(isLoanBetweenSameCountries)}
              >
                <Checkbox
                  label="Withholding tax"
                  variant="outlinedAlternative"
                  bg={isLoanBetweenSameCountries ? 'alabaster' : 'white'}
                  disabled={isLoanBetweenSameCountries}
                  isActive={leg.report.isWhtEnabled}
                  onChange={(isActive) => onChangeIsWhtEnabled(leg, isActive)}
                />
              </WithTooltip>
            </FlexLayout>
            {leg.report.isWhtEnabled && (
              <FlexLayout sx={{ gap: 8 }}>
                <RadioGroup
                  options={[
                    { label: 'Net interest', value: whtEnums.WHT_APPROACHES.BORROWER_PAYS },
                    { label: 'Gross-up', value: whtEnums.WHT_APPROACHES.GROSS_UP },
                  ]}
                  label="Withholding Tax Approaches"
                  tooltip={ratesCardUtils.approachesTooltip}
                  width="sm"
                  radioVariant="primarySmallerPadding"
                  value={leg.report.approach}
                  onChange={(approach) => onChangeApproach(leg, approach)}
                />
                <WithTooltip label="Disable WHT Rate Picker">
                  <WHTCreatableSingleSelect
                    width="xs"
                    height="input-height-text"
                    options={leg.whtOptions}
                    setWhtRateOptions={(options) => setWhtRateOptions(leg, options)}
                    value={leg.report.whtInterestRate}
                    onChange={(whtInterestRate) => onChangeWhtInterestRate(leg, whtInterestRate)}
                  />
                </WithTooltip>
              </FlexLayout>
            )}
          </FlexLayout>
        );
      })}
    </Modal>
  );
};

export default B2BLoanWHTModal;
