import { saveAs } from 'file-saver';
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
  createCreditRatingFile,
  createGuaranteeFile,
  createLoanFile,
  createB2BLoanFile,
  getB2BLoanFile,
  deleteB2BLoanFile,
  updateB2BLoanFileStatus,
  deleteCreditRatingFile,
  deleteGuaranteeFile,
  deleteLoanFile,
  generateGuaranteeAgreement,
  generateGuaranteeTpReport,
  generateLoanAgreement,
  generateB2BLoanAgreements,
  generateB2BLoanTpReport,
  generateLoanTpReport,
  getCreditRatingFile,
  getGuaranteeFile,
  getLoanFile,
  updateCreditRatingFileStatus,
  updateGuaranteeFileStatus,
  updateLoanFileStatus,
} from '~/api';
import { DeleteModal, ConfirmModal } from '~/components/Modals';
import FileLabelSingleSelect from '~/components/ReportViewPage/FileLabelSingleSelect';
import {
  getReportFilesColumns,
  getReportFilesData,
  isAgreementAlreadyGenerated,
  isTpReportAlreadyGenerated,
  getConfirmModalTitle,
  getConfirmModalAdditionalInfo,
} from '~/components/ReportViewPage/ReportView.utils';
import { ThreeDotActionMenu } from '~/components/Shared';
import { isAdmin, reportEnum } from '~/enums';
import { reportFile, resetReportFile, setReportFile, updateFileField } from '~/reducers/reportFile.slice';
import { Button, Card, FileInput, FlexLayout, Icon, LoadingSpinner, Modal, Table, Text, TextInput } from '~/ui';
import { showToast, showErrorToast } from '~/ui/components/Toast';
import { checkIsImported, getReportUnit } from '~/utils/report';
import { errorHandler } from '~/utils/errors';
import { WithTooltip } from '~/ui/hocs';

const RightTitleContent = ({
  formData,
  formType,
  setShowGenerateAgreementModal,
  handleGenerateAgreement,
  handleGenerateTpReport,
  handleOnFileChange,
  isRateFormConfirmed,
}) => {
  const hasGuaranteeCumulativeProbabilityOfDefault =
    formData.principal?.creditRating?.cumulativeProbabilityOfDefault != null || !formData.principal;
  const isBackToBackLoan = formType === reportEnum.BACK_TO_BACK_LOAN;

  const getGenerateAgreementDisabledTooltip = () => {
    if (isAgreementAlreadyGenerated(formData.files)) return 'Agreement is already generated.';
    if (formData.status === 'Final') return 'Report is finalized.';
    if (!isRateFormConfirmed) return 'You must enter an interest rate before you can generate an agreement.';
    if (!hasGuaranteeCumulativeProbabilityOfDefault) return 'You must reprice this guarantee';
    return 'Not available';
  };

  const isGenerateAgreementDisabled = () => {
    return (
      isAgreementAlreadyGenerated(formData.files) ||
      formData.status === 'Final' ||
      !isRateFormConfirmed ||
      !hasGuaranteeCumulativeProbabilityOfDefault
    );
  };

  const getGenerateReportDisabledTooltip = () => {
    if (!hasGuaranteeCumulativeProbabilityOfDefault) return 'You must reprice this guarantee';
    return 'Not available';
  };

  const isGenerateReportDisabled = () => !hasGuaranteeCumulativeProbabilityOfDefault;

  return (
    <FlexLayout alignItems="center" justifyContent="flex-end" space={4}>
      <WithTooltip
        label="Disable generate agreement"
        tooltip={getGenerateAgreementDisabledTooltip()}
        disabled={isGenerateAgreementDisabled()}
      >
        <Button
          isShowing={formType !== reportEnum.CREDIT_RATING && !checkIsImported(formData.report)}
          iconLeft="add"
          text={isBackToBackLoan ? 'Generate agreements' : 'Generate agreement'}
          size="s"
          variant="secondary"
          onClick={() => {
            if (formData.isPortfolio) {
              return handleGenerateAgreement();
            }
            setShowGenerateAgreementModal(true);
          }}
          disabled={isGenerateAgreementDisabled()}
        />
      </WithTooltip>
      <WithTooltip
        label="Disable generate report"
        tooltip={getGenerateReportDisabledTooltip()}
        disabled={isGenerateReportDisabled()}
      >
        <Button
          isShowing={formData.isPortfolio && formType !== reportEnum.CREDIT_RATING && !checkIsImported(formData.report)}
          iconLeft="add"
          text="Generate report"
          size="s"
          variant="secondary"
          onClick={handleGenerateTpReport}
          disabled={isTpReportAlreadyGenerated(formData.files) || formData.status === 'Final'}
        />
      </WithTooltip>
      <FileInput onChange={handleOnFileChange}>
        <Button isShowing={formData.isPortfolio} iconLeft="upload" size="s" text="Import" variant="secondary" />
      </FileInput>
    </FlexLayout>
  );
};

function ReportFilesTable({
  isReportInTrash = false,
  formData,
  formType,
  updateField,
  showGenerateAgreement,
  role,
  isRateFormConfirmed,
}) {
  const dispatch = useDispatch();
  const uploadedFileData = useSelector(reportFile);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [fileIsUploading, setFileIsUploading] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false); // false or set to table row values of report files
  const [showGenerateAgreementModal, setShowGenerateAgreementModal] = useState(false); // also used for generating tpReport
  const [isLoading, setIsLoading] = useState(false);
  const isLoan = formType === reportEnum.LOAN;
  const isBackToBackLoan = formType === reportEnum.BACK_TO_BACK_LOAN;

  const unit = getReportUnit(formData.rateType);

  function handleOnFileImportClick() {
    const createFileApiMethodMapper = {
      [reportEnum.LOAN]: createLoanFile,
      [reportEnum.BACK_TO_BACK_LOAN]: createB2BLoanFile,
      [reportEnum.GUARANTEE]: createGuaranteeFile,
      [reportEnum.CREDIT_RATING]: createCreditRatingFile,
    };

    const createFile = createFileApiMethodMapper[formType];
    setFileIsUploading(true);

    createFile({
      reportId: formData.id,
      file: uploadedFile,
      label: uploadedFileData.label,
      newName: uploadedFileData.newName,
    })
      .then((res) => {
        setShowImportModal(false);
        dispatch(updateField({ files: [...formData?.files, res] }));
        setUploadedFile(null);
        dispatch(resetReportFile());
        showToast('File has been successfully uploaded.');
      })
      .catch((err) => {
        if (err.response.status === 400) {
          showErrorToast('File too large.');
        } else {
          showErrorToast();
        }
      })
      .finally(() => setFileIsUploading(false));
  }

  function handleOnFileChange(file) {
    if (file) {
      setUploadedFile(file);
      dispatch(setReportFile({ name: file.name, files: formData?.files }));
      setShowImportModal(true);
    }
  }

  function handleOnImportModalHide() {
    setShowImportModal(false);
    dispatch(resetReportFile());
    setUploadedFile(null);
  }

  function handleOnDownloadClick(file, type) {
    const downloadFileApiMethodMapper = {
      [reportEnum.LOAN]: getLoanFile,
      [reportEnum.BACK_TO_BACK_LOAN]: getB2BLoanFile,
      [reportEnum.GUARANTEE]: getGuaranteeFile,
      [reportEnum.CREDIT_RATING]: getCreditRatingFile,
    };

    const downloadFile = downloadFileApiMethodMapper[formType];
    const index = formData?.files.findIndex((f) => f.id === file.id);
    const filesCopy = [...formData?.files];
    filesCopy[index] = { ...filesCopy[index], isDownloading: true };
    dispatch(updateField({ files: filesCopy }));

    downloadFile({ fileId: file.id, type })
      .then((res) => {
        saveAs(res, res.name);
        showToast(`${file.name} has been successfully downloaded.`);
      })
      .catch(() => showErrorToast())
      .finally(() => {
        const filesCopy = [...formData?.files];
        filesCopy[index] = { ...filesCopy[index], isDownloading: false };
        dispatch(updateField({ files: filesCopy }));
      });
  }

  function handleOnDeleteClick(file) {
    const deleteFileApiMethodMapper = {
      [reportEnum.LOAN]: deleteLoanFile,
      [reportEnum.BACK_TO_BACK_LOAN]: deleteB2BLoanFile,
      [reportEnum.GUARANTEE]: deleteGuaranteeFile,
      [reportEnum.CREDIT_RATING]: deleteCreditRatingFile,
    };

    const deleteFile = deleteFileApiMethodMapper[formType];
    deleteFile({ fileId: file.id, reportId: formData.id })
      .then(() => {
        dispatch(updateField({ files: formData?.files.filter((f) => f.id !== file.id) }));
        showToast(`${file.name} has been successfully deleted`);
        setShowDeleteModal(false);
      })
      .catch(() => showErrorToast());
  }

  function handleUpdateStatus(file, status) {
    const updateFileStatusApiMethodMapper = {
      [reportEnum.LOAN]: updateLoanFileStatus,
      [reportEnum.BACK_TO_BACK_LOAN]: updateB2BLoanFileStatus,
      [reportEnum.GUARANTEE]: updateGuaranteeFileStatus,
      [reportEnum.CREDIT_RATING]: updateCreditRatingFileStatus,
    };

    const updateFile = updateFileStatusApiMethodMapper[formType];
    updateFile({ fileId: file.id, data: { status } })
      .then(() => {
        const index = formData?.files.findIndex((f) => f.id === file.id);
        const filesCopy = [...formData?.files];
        filesCopy[index] = { ...filesCopy[index], status };
        dispatch(updateField({ files: filesCopy }));
        showToast(`${file.name} has been successfully ${status === 'Final' ? 'finalized.' : 'marked as draft.'}`);
      })
      .catch(() => showErrorToast());
  }

  function renderActionColumn(item) {
    const options = [];

    if (formData.isPortfolio) {
      if (item.status === 'Draft') {
        options.push({
          label: 'Finalize',
          onClick: () => handleUpdateStatus(item, 'Final'),
        });
      } else if (formData?.status === 'Draft') {
        options.push({
          label: 'Mark as Draft',
          onClick: () => handleUpdateStatus(item, 'Draft'),
        });
      }
    }

    options.push({
      label: 'Download',
      onClick: () => handleOnDownloadClick(item),
    });
    if (item.status === 'Final') {
      options.push({
        label: 'Download as PDF',
        onClick: () => handleOnDownloadClick(item, 'pdf'),
      });
    }

    if ((isAdmin(role) || !formData.isPortfolio) && formData.status !== 'Final') {
      options.push({
        label: 'Delete',
        onClick: () => setShowDeleteModal(item),
      });
    }

    return item.isDownloading ? <LoadingSpinner size="s" /> : <ThreeDotActionMenu options={options} />;
  }

  async function handleGenerateAgreement() {
    try {
      setIsLoading(true);
      const generateAgreementApiMethodMapper = {
        [reportEnum.LOAN]: generateLoanAgreement,
        [reportEnum.BACK_TO_BACK_LOAN]: generateB2BLoanAgreements,
        [reportEnum.GUARANTEE]: generateGuaranteeAgreement,
      };

      const generateAgreement = generateAgreementApiMethodMapper[formData.reportType];
      const generatedAgreement = await generateAgreement({ id: formData.id });
      if (isBackToBackLoan) {
        return dispatch(updateField({ files: [...formData?.files, ...generatedAgreement] }));
      }
      dispatch(updateField({ files: [...formData?.files, generatedAgreement] }));
    } catch (err) {
      errorHandler(err);
    } finally {
      setIsLoading(false);
      setShowGenerateAgreementModal(false);
    }
  }

  async function handleGenerateTpReport() {
    try {
      const generateTpReportApiMethodMapper = {
        [reportEnum.LOAN]: generateLoanTpReport,
        [reportEnum.BACK_TO_BACK_LOAN]: generateB2BLoanTpReport,
        [reportEnum.GUARANTEE]: generateGuaranteeTpReport,
      };
      const generateTpReport = generateTpReportApiMethodMapper[formData.reportType];

      const generatedTpReport = await generateTpReport({ id: formData.id });
      dispatch(updateField({ files: [...formData?.files, generatedTpReport] }));
    } catch (err) {
      errorHandler(err);
    } finally {
      setShowGenerateAgreementModal(false);
    }
  }

  return (
    <>
      <Card pb={3} pt={6} spaces={0}>
        <Table
          actionColumn={!isReportInTrash && renderActionColumn}
          columns={getReportFilesColumns()}
          data={getReportFilesData(formData?.files, showGenerateAgreement)}
          isSearchable
          rightTitleContent={
            !isReportInTrash && (
              <RightTitleContent
                formData={formData}
                formType={formType}
                setShowGenerateAgreementModal={setShowGenerateAgreementModal}
                handleGenerateAgreement={handleGenerateAgreement}
                handleGenerateTpReport={handleGenerateTpReport}
                handleOnFileChange={handleOnFileChange}
                isRateFormConfirmed={isRateFormConfirmed}
              />
            )
          }
        />
      </Card>
      {showImportModal && (
        <Modal
          actionButtons={
            <Button
              disabled={
                (uploadedFileData?.nameExists &&
                  (uploadedFileData?.name === uploadedFileData?.newName || !uploadedFileData?.newName)) ||
                !uploadedFileData?.label
              }
              loading={fileIsUploading}
              text="Import"
              onClick={fileIsUploading ? null : handleOnFileImportClick}
            />
          }
          title={
            uploadedFileData?.nameExists
              ? `The file ${uploadedFileData?.name} already exist. Rename your report.`
              : 'Choose file label'
          }
          width="s"
          onHide={handleOnImportModalHide}
        >
          <FlexLayout alignItems="center" space={2}>
            <Icon icon="xls" size="m" />
            <Text color="bali-hai" variant="2l-spaced" shouldTruncate sx={{ fontStyle: 'italic', flexGrow: '1' }}>
              {uploadedFile?.name}
            </Text>
          </FlexLayout>
          {uploadedFileData.nameExists && (
            <TextInput
              placeholder="Insert name"
              value={uploadedFileData?.newName}
              width="fullWidth"
              onChange={(value) => dispatch(updateFileField({ newName: value }))}
            />
          )}
          <FileLabelSingleSelect
            formType={formType}
            value={uploadedFileData?.label}
            onChange={(value) => dispatch(updateFileField({ label: value }))}
          />
        </Modal>
      )}
      <DeleteModal
        isShowing={showDeleteModal}
        item="report file"
        handleOnDeleteClick={() => handleOnDeleteClick(showDeleteModal)}
        handleOnHide={() => setShowDeleteModal(false)}
      />
      <ConfirmModal
        buttonText="Confirm"
        data={formData}
        isLoading={isLoading}
        isButtonDisabled={!formData.report?.finalInterestRate?.toString()?.length}
        isShowing={showGenerateAgreementModal}
        onClick={() => handleGenerateAgreement()}
        onHide={() => setShowGenerateAgreementModal(false)}
        title={getConfirmModalTitle({ isLoan, isBackToBackLoan, formData, unit })}
        additionalInfo={getConfirmModalAdditionalInfo(isLoan, isBackToBackLoan)}
        updateField={updateField}
      />
    </>
  );
}

export default ReportFilesTable;
