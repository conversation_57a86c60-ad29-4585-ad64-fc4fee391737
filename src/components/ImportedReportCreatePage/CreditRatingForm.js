import { useDispatch, useSelector } from 'react-redux';

import { tooltips } from '~/components/CompanyCreatePage/CreditRatingCard.utils';
import { CompanySingleSelect, CreditRatingSingleSelect } from '~/components/Shared';
import { importedReportForm, updateField } from '~/reducers/importedReportForm.slice';
import { Box, DateInput, FlexLayout } from '~/ui';
import NumberInput from '~/ui/components/NumberInput';

// Form for credit rating portfolio import
function CreditRatingForm() {
  const dispatch = useDispatch();
  const { company, closingDate, creditRating, probabilityOfDefault } = useSelector(importedReportForm);

  return (
    <FlexLayout flexDirection="column" space={8}>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(4, 1fr)' }}>
        <CompanySingleSelect
          label="Company"
          properties={['id', 'name', 'country']}
          value={company?.id}
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ company: value }))}
        />
        <DateInput
          label="Closing Date"
          value={closingDate}
          tooltip={tooltips.closingDate}
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ closingDate: value }))}
        />
        <CreditRatingSingleSelect
          label="Credit Rating"
          value={creditRating?.rating}
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ creditRating: { rating: value } }))}
        />
        <NumberInput
          allowNegatives={false}
          inputType="float"
          label="Probability of default"
          tooltip={tooltips.probabilityOfDefault}
          value={probabilityOfDefault}
          unit="%"
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ probabilityOfDefault: value }))}
        />
      </Box>
    </FlexLayout>
  );
}

export default CreditRatingForm;
