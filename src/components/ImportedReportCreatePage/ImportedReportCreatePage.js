import { saveAs } from 'file-saver';
import { useCallback, useContext, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { NotesCard } from '~/components/Shared';
import { UserInfoContext } from '~/context/user';
import { REPORT_TYPE, reportEnum } from '~/enums';
import { useCompanies, useUnsavedChangesWarning } from '~/hooks';
import {
  importedReportForm,
  isFormValid,
  resetForm,
  setFormInitial,
  setIsPristine,
  updateField,
} from '~/reducers/importedReportForm.slice';
import { routesEnum } from '~/routes';
import { Button, Card, FlexLayout, PageLayout, Tabs, FileInput } from '~/ui';
import { showToast } from '~/ui/components/Toast';
import { capitalize } from '~/utils/strings';
import { genericTabSetter } from '~/utils/tabs';
import { sheetToJson } from '~/utils/documents';
import { errorHandler } from '~/utils/errors';

import CreditRatingForm from './CreditRatingForm';
import GuaranteeForm from './GuaranteeForm';
import LoanForm from './LoanForm';
import {
  reportTypeToCreateReportsFunctionMapper,
  reportTypeToCreateReportFunctionMapper,
  reportTypeToGetReportsFromSheetFunctionMapper,
  reportTypeToImportFunctionMapper,
} from './ImportedReportCreatePage.utils';

function ImportedReportCreatePage() {
  const dispatch = useDispatch();
  const history = useHistory();
  const formData = useSelector(importedReportForm);
  const [tabs, setTabs] = useState([]);
  const [isUploading, setIsUploading] = useState(false);
  const [Prompt] = useUnsavedChangesWarning({ isDirty: formData.isDirty });
  const { userInfo } = useContext(UserInfoContext);
  const companies = useCompanies();

  const setReportType = useCallback((reportType) => dispatch(setFormInitial({ reportType })), [dispatch]);

  useEffect(() => {
    const { features } = userInfo;
    const tabs = [
      { isEnabled: features.loan, label: 'Loans', value: reportEnum.LOAN },
      { isEnabled: features.guarantee, label: 'Guarantees', value: reportEnum.GUARANTEE },
      { isEnabled: features.creditRating, label: 'Credit Ratings', value: reportEnum.CREDIT_RATING },
    ];
    genericTabSetter(setTabs, setReportType, tabs);

    return () => dispatch(resetForm());
  }, [userInfo, dispatch, setReportType]);

  function handleOnImportTemplateDownloadClick() {
    const getMassImportTemplate = reportTypeToImportFunctionMapper[formData.reportType];
    getMassImportTemplate()
      .then((res) => {
        saveAs(res, `${formData.reportType.replace(/\s+/g, '_')}_import_template.xlsx`);
        showToast('Template was successfully downloaded.');
      })
      .catch((error) => errorHandler(error));
  }

  function handleOnImportTemplateUploadClick(document) {
    setIsUploading(true);
    const reader = new FileReader();
    reader.onload = function (e) {
      const data = new Uint8Array(e.target.result);
      sheetToJson(data, null)
        .then((jsonSheet) => {
          try {
            const getReportsFromSheet = reportTypeToGetReportsFromSheetFunctionMapper[formData.reportType];
            const reports = getReportsFromSheet(jsonSheet, companies);

            const createReports = reportTypeToCreateReportsFunctionMapper[formData.reportType];
            createReports(reports)
              .then(() => {
                showToast(`${capitalize(formData.reportType)} were successfully imported.`);
                history.push(`${routesEnum.ANALYSES}?${REPORT_TYPE}=${formData.reportType}`);
              })
              .catch(errorHandler);
          } catch (error) {
            errorHandler(error);
          }
        })
        .catch(errorHandler)
        .finally(() => setIsUploading(false));
    };

    reader.onerror = function (error) {
      setIsUploading(false);
      errorHandler(error);
    };

    reader.readAsArrayBuffer(document);
  }

  const handleOnTabSelect = (tabName) => dispatch(setFormInitial({ reportType: tabName }));

  function handleOnSubmitClick() {
    const createItem = reportTypeToCreateReportFunctionMapper[formData.reportType];

    const { reportType, isDirty, ...requestData } = formData;
    createItem(requestData)
      .then((res) => {
        showToast(`${capitalize(reportType)} has been successfully created.`);
        dispatch(setIsPristine());
        history.push(`${routesEnum.ANALYSES}/${res.id}?${REPORT_TYPE}=${reportType}`);
      })
      .catch(errorHandler);
  }

  return (
    <>
      <PageLayout
        title="Import"
        rightTitleContent={
          <FlexLayout alignItems="center" space={6}>
            <Button
              iconLeft="download"
              size="s"
              text="Download template"
              variant="secondary"
              onClick={handleOnImportTemplateDownloadClick}
            />
            <FileInput accept=".xls,.xlsx" sx={{ alignSelf: 'center' }} onChange={handleOnImportTemplateUploadClick}>
              <Button iconLeft="upload" size="s" loading={isUploading} text="Upload template" variant="secondary" />
            </FileInput>
          </FlexLayout>
        }
      >
        <Card py={6}>
          <Tabs selectedTab={formData.reportType} tabs={tabs} onTabSelect={handleOnTabSelect} />
          {formData.reportType === reportEnum.LOAN && <LoanForm />}
          {formData.reportType === reportEnum.GUARANTEE && <GuaranteeForm />}
          {formData.reportType === reportEnum.CREDIT_RATING && <CreditRatingForm />}
        </Card>
        <NotesCard
          description={`Include any notes for the ${formData.reportType} here.`}
          note={formData?.note}
          onChange={(value) => dispatch(updateField({ note: value }))}
        />
        <FlexLayout alignItems="flex-end" flexGrow="1" justifyContent="space-between">
          <Button text="Back" variant="gray" onClick={history.goBack} />
          <Button disabled={!isFormValid(formData)} iconRight="arrowRight" text="Next" onClick={handleOnSubmitClick} />
        </FlexLayout>
      </PageLayout>
      {Prompt}
    </>
  );
}

export default ImportedReportCreatePage;
