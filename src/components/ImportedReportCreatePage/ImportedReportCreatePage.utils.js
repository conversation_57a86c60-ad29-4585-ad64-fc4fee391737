import _ from 'lodash';
import { format } from 'date-fns';

import { DATE_FNS_FORMATS, reportEnum, lenderOrGuarantorTypeEnum, pricingMethodologyEnum } from '~/enums';
import {
  createImportedGuarantee,
  createImportedLoan,
  createMassImportedGuarantees,
  createMassImportedLoans,
  getCreditRatingMassImportTemplate,
  getGuaranteeMassImportTemplate,
  getLoanMassImportTemplate,
  postImportedCreditRating,
  postMassImportedCreditRatings,
} from '~/api';
import { getPricingApproachFromTemplates } from '~/utils/report';

const requiredCompanyFields = ['id', 'industry', 'name', 'parentCompanyId', 'country', 'creditRating'];

const getLender = ({ loan, isThirdParty, companies, rowNumber }) => {
  if (isThirdParty) {
    return {
      id: null,
      parentCompanyId: null,
      name: loan['Lender Name'],
      country: loan['Lender Country'],
      industry: null,
      creditRating: null,
    };
  } else {
    const lender = companies.find((company) => company.name === loan['Lender Name']);
    if (!lender) throw new Error(`Lender company in row ${rowNumber} does not exist`);
    return lender;
  }
};

function getLoansFromSheet(sheet, companies) {
  // In Loan import sheet first row is not data row, so we remove it
  sheet.shift();

  if (sheet.length === 0) {
    throw new Error('Sheet is empty. Please upload populated sheet.');
  }
  if (!('Lender Type' in sheet[0])) throw new Error('Wrong template.');

  const loans = [];
  for (let i = 0, len = sheet.length; i < len; i++) {
    const loan = sheet[i];

    const isThirdParty = loan['Lender Type'] === lenderOrGuarantorTypeEnum.THIRD_PARTY;

    const lender = getLender({ loan, isThirdParty, companies, rowNumber: i + 3 });

    const borrower = companies.find((company) => company.name === loan['Borrower Name']);
    if (!borrower) throw new Error(`Borrower company in row ${i + 3} does not exist`);

    if (lender.name === borrower.name)
      throw new Error(`Lender and Borrower in row ${i + 3} can not be the same entity`);

    loans.push({
      lender: _.pick(lender, requiredCompanyFields),
      borrower: _.pick(
        { ...borrower, creditRating: { ...borrower.creditRating, newRating: loan['Borrower Rating'] } },
        requiredCompanyFields
      ),
      issueDate: format(loan['Issue Date'], DATE_FNS_FORMATS.ISO),
      maturityDate: format(loan['Maturity Date'], DATE_FNS_FORMATS.ISO),
      currency: loan['Currency'],
      amount: loan['Principal Amount'],
      paymentFrequency: loan['Compounding Frequency'],
      seniority: loan['Seniority'],
      pricingApproach: isThirdParty ? null : getPricingApproachFromTemplates(loan['Pricing Approach']),
      rateType: {
        type: loan['Interest Type'].toLowerCase(),
        referenceRate: loan['Reference Rate'],
        referenceRateMaturity: loan['Reference Rate Maturity'],
      },
      type: loan['Type'],
      report: { finalInterestRate: loan['Interest Rate'] },
      note: loan['Note'] || null,
      isThirdParty,
    });
  }

  return loans;
}

const getGuarantor = ({ guarantee, isThirdParty, companies, rowNumber }) => {
  if (isThirdParty) {
    return {
      id: null,
      parentCompanyId: null,
      name: guarantee['Guarantor Name'],
      country: guarantee['Guarantor Country'],
      industry: null,
      creditRating: null,
    };
  } else {
    const guarantor = companies.find((company) => company.name === guarantee['Guarantor Name']);
    if (!guarantor) throw new Error(`Guarantor company in row ${rowNumber} does not exist`);
    return {
      ...guarantor,
      creditRating: { ...guarantor.creditRating, newRating: guarantee['Guarantor Rating'] },
    };
  }
};

function getGuaranteesFromSheet(sheet, companies) {
  // In Guarantee import sheet first row is not data row, so we remove it
  sheet.shift();

  if (sheet.length === 0) {
    throw new Error('Sheet is empty. Please upload populated sheet.');
  }
  if (!('Guarantor Type' in sheet[0])) throw new Error('Wrong template.');

  const guarantees = [];
  for (let i = 0, len = sheet.length; i < len; i++) {
    const guarantee = sheet[i];

    const isThirdParty = guarantee['Guarantor Type'] === lenderOrGuarantorTypeEnum.THIRD_PARTY;

    const guarantor = getGuarantor({ guarantee, isThirdParty, companies, rowNumber: i + 3 });

    const principal = companies.find((company) => company.name === guarantee['Principal Name']);
    if (!principal) throw new Error(`Principal company in row ${i + 3} does not exist`);

    if (guarantor.name === principal.name)
      throw new Error(`Guarantor and Principal in row ${i + 3} can not be the same entity`);

    guarantees.push({
      guarantor: _.pick(guarantor, requiredCompanyFields),
      principal: _.pick(
        { ...principal, creditRating: { ...principal.creditRating, newRating: guarantee['Principal Rating'] } },
        requiredCompanyFields
      ),
      issueDate: format(guarantee['Issue Date'], DATE_FNS_FORMATS.ISO),
      terminationDate: format(guarantee['Termination Date'], DATE_FNS_FORMATS.ISO),
      currency: guarantee['Currency'],
      amount: guarantee['Amount'],
      paymentFrequency: guarantee['Payment Frequency'],
      seniority: isThirdParty ? null : guarantee['Seniority'],
      pricingApproach: isThirdParty ? null : getPricingApproachFromTemplates(guarantee['Pricing Approach']),
      pricingMethodology: pricingMethodologyEnum.YIELD_EXPECTED_LOSS_APPROACH,
      report: { finalInterestRate: guarantee['Guarantee Fee'] },
      note: guarantee['Note'] || null,
      isThirdParty,
    });
  }

  return guarantees;
}

function getCreditRatingsFromSheet(sheet, companies) {
  if (sheet.length === 0) {
    throw new Error('Sheet is empty. Please upload populated sheet.');
  }
  if (!('Company' in sheet[0])) throw new Error('Wrong template.');

  const creditRatings = [];
  for (let i = 0, len = sheet.length; i < len; i++) {
    const newCreditRating = sheet[i];

    const company = companies.find((company) => company.name === newCreditRating['Company']);
    if (!company) throw new Error(`Company in row ${i + 2} does not exist`);

    creditRatings.push({
      attributes: null,
      company: _.pick(company, 'id', 'name', 'country'),
      closingDate: format(newCreditRating['Closing Date'], DATE_FNS_FORMATS.ISO),
      creditRating: { rating: newCreditRating['Credit Rating'] },
      probabilityOfDefault: newCreditRating['Probability of default'],
      note: newCreditRating['Note'] || null,
    });
  }

  return creditRatings;
}

export const reportTypeToImportFunctionMapper = {
  [reportEnum.LOAN]: getLoanMassImportTemplate,
  [reportEnum.GUARANTEE]: getGuaranteeMassImportTemplate,
  [reportEnum.CREDIT_RATING]: getCreditRatingMassImportTemplate,
};

export const reportTypeToGetReportsFromSheetFunctionMapper = {
  [reportEnum.LOAN]: getLoansFromSheet,
  [reportEnum.GUARANTEE]: getGuaranteesFromSheet,
  [reportEnum.CREDIT_RATING]: getCreditRatingsFromSheet,
};

export const reportTypeToCreateReportsFunctionMapper = {
  [reportEnum.LOAN]: createMassImportedLoans,
  [reportEnum.GUARANTEE]: createMassImportedGuarantees,
  [reportEnum.CREDIT_RATING]: postMassImportedCreditRatings,
};

export const reportTypeToCreateReportFunctionMapper = {
  [reportEnum.LOAN]: createImportedLoan,
  [reportEnum.GUARANTEE]: createImportedGuarantee,
  [reportEnum.CREDIT_RATING]: postImportedCreditRating,
};

export const loanTooltips = {
  lenderType: 'Specify whether the loan is issued from a related party or third-party lender.',
  lender: 'Select the company that provided the loan.',
  lenderName: 'Enter the name of the company that provides the loan.',
  lenderCountry: "Select the Lender's country of residence.",
  borrower: 'Select the company that received the loan.',
  issueDate: 'Select the date on which the loan was issued from the lender to the borrower.',
  maturityDate: 'Select the date on which the loan has or will mature and be repaid by the borrower.',
  currentBorrowerRating: 'This displays the current rating of the borrower for reference purposes.',
  borrowerRating:
    'Select the issuer rating of the borrower.<br/>' +
    'This can be either stand-alone or implicit support adjusted, whichever was used in pricing the loan.',
  currency: 'Select the currency in which the loan is or was denominated.',
  amount: 'Enter the quantum of the loan, or the amount of money that was lent and borrowed.',
  rateType: 'Enter the interest type of the loan.',
  seniority:
    'Select the tranche or level of seniority of the loan from highest (Senior Secured) to lowest (Subordinated).',
  finalInterestRate: 'Enter the interest rate on the loan.',
  paymentFrequency:
    'Select the frequency at which interest compounds on the loan.<br/>' +
    'The interest frequency will correspond to this selection except in the case of balloon loans.',
  type:
    'Select the loan type.<br/>' +
    'Bullet loans have interest paid periodically and principal repaid at maturity.<br/>' +
    'Balloon loans have both interest and principal repaid at maturity.',
};

export const guaranteeTooltips = {
  guarantorType: 'Specify whether the guarantee is issued from a related party or third-party guarantor.',
  guarantor: 'Select the company that provided the guarantee.',
  guarantorName: 'Enter the name of the company that provides the guarantee.',
  guarantorCountry: "Select the Guarantor's country of residence.",
  principal: 'Select the company that received the guarantee.',
  currentGuarantorRating: 'This displays the current rating of the guarantor for reference purposes.',
  guarantorRating:
    'Select the issuer rating of the guarantor.<br/>' +
    'This can be either stand-alone or implicit support adjusted, whichever was used in pricing the guarantee.',
  currentPrincipalRating: 'This displays the current rating of the principal for reference purposes.',
  principalRating:
    'Select the issuer rating of the principal.<br/>' +
    'This can be either stand-alone or implicit support adjusted, whichever was used in pricing the guarantee.',
  issueDate: 'Select the date on which the loan was issued from the guarantor to the principal.',
  terminationDate: 'Select the date on which the guarantee has or will terminate and no longer be valid.',
  currency: 'Select the currency of denomination of the liability that the guarantee covered or covers.',
  amount: 'Enter the quantum of the liability that the guarantee covered or covers.',
  finalInterestRate: 'Enter the fee on the guarantee.',
  paymentFrequency: 'Select the frequency of fee interest for the guarantee.',
  seniority: 'Select the tranche or level of seniority of the liability that the guarantee covers.',
};
