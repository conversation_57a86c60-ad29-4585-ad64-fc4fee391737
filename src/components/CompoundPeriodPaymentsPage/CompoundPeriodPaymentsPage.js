import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';

import { getBalloonCompoundPeriodPayments } from '~/api';
import { FilterPaymentColumnsModal, ReferenceRateModal } from '~/components/Modals';
import { reportEnum } from '~/enums';
import { paymentSelector } from '~/reducers/payment.slice';
import { Button, Card, FlexLayout, PageLayout, Text } from '~/ui';

import LoanBalloonPaymentsTable from './FloatBalloonPaymentsTable';

/** See markPaymentAsPaid in loanPaymentController in API for explanation of this screen */
const CompoundPeriodPaymentsPage = () => {
  const { loanId } = useParams();
  const formData = useSelector(paymentSelector);
  const [isLoading, setIsLoading] = useState(true);
  const [isColumnFilterModalShowing, setIsColumnFilterModalShowing] = useState(false);
  const [isReferenceModalShowing, setIsReferenceModalShowing] = useState(false); // false or set to table row values of payment
  const [payments, setPayments] = useState();
  const reportType = reportEnum.LOAN;

  useEffect(() => {
    setIsLoading(true);
    getBalloonCompoundPeriodPayments({ id: loanId }).then((compoundPayments) => {
      setPayments(compoundPayments);
      setIsLoading(false);
    });
  }, [formData, loanId]);

  return (
    <PageLayout title="Interest">
      <Card pb={3} pt={6}>
        <FlexLayout alignItems="center" justifyContent="space-between">
          <Text variant="2l-spaced" color="deep-sapphire">
            Compound Periods
          </Text>
          <FlexLayout alignItems="center" justifyContent="space-between" space={6}>
            <Button
              iconLeft="columns"
              size="s"
              text="Columns"
              variant="secondary"
              onClick={() => setIsColumnFilterModalShowing(true)}
            />
          </FlexLayout>
        </FlexLayout>

        <LoanBalloonPaymentsTable
          isLoading={isLoading}
          payments={payments}
          setPayments={setPayments}
          reportType={reportType}
          setIsReferenceModalShowing={setIsReferenceModalShowing}
        />
      </Card>
      <FilterPaymentColumnsModal
        isShowing={isColumnFilterModalShowing}
        reportType="floatBalloon"
        onHide={() => setIsColumnFilterModalShowing(false)}
      />
      <ReferenceRateModal payment={isReferenceModalShowing} onHide={() => setIsReferenceModalShowing(false)} />
    </PageLayout>
  );
};

export default CompoundPeriodPaymentsPage;
