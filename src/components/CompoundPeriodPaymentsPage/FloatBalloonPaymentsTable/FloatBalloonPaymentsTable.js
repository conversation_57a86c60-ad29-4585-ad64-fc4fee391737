import { useContext } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { UserInfoContext } from '~/context/user';
import { floatBalloonPaymentColumns } from '~/reducers/tableColumns/floatBalloonPaymentColumns.slice';
import { routesEnum } from '~/routes';
import { FlexLayout, LoadingSpinner, Table } from '~/ui';

import {
  getLoanFloatBalloonPaymentsColumns,
  getLoanFloatBalloonPaymentsData,
  renderTableActionColumn as renderLoanTableActionColumn,
} from './FloatBalloonPaymentsTable.utils';

const FloatBalloonPaymentsTable = ({ payments, setPayments, isLoading, reportType, setIsReferenceModalShowing }) => {
  const history = useHistory();
  const dispatch = useDispatch();
  const visibleLoanColumns = useSelector(floatBalloonPaymentColumns);
  const { userInfo } = useContext(UserInfoContext);

  if (isLoading) return <LoadingSpinner />;

  return (
    <FlexLayout flexDirection="column">
      <Table
        actionColumn={(item) =>
          renderLoanTableActionColumn({
            item,
            setPayments,
            dispatch,
            setIsReferenceModalShowing,
            history,
          })
        }
        columns={getLoanFloatBalloonPaymentsColumns(visibleLoanColumns)}
        data={getLoanFloatBalloonPaymentsData(payments, userInfo)}
        onItemClick={({ id }) => history.push(`${routesEnum.PORTFOLIO}/${id}?reportType=${reportType}`)}
      />
    </FlexLayout>
  );
};

export default FloatBalloonPaymentsTable;
