import React from 'react';
import ReactTooltip from 'react-tooltip';

import { getBalloonCompoundPeriodPayments, markLoanPaymentAsPaid } from '~/api';
import { ThreeDotActionMenu } from '~/components/Shared';
import { updateField } from '~/reducers/payment.slice';
import { routesEnum } from '~/routes';
import { Box, FlexLayout, Text } from '~/ui';
import { showToast } from '~/ui/components/Toast';
import { formatDateString } from '~/utils/dates';
import { getReportUnit } from '~/utils/report';
import { displayNumber2 } from '~/utils/strings';
import { getVisibleColumns } from '~/utils/tables';

function getLoanData(payment, userInfo) {
  const {
    paymentDueDate,
    isPaid,
    loan,
    isPrincipalPayment,
    balloonPayment,
    paymentNumber,
    paymentAmount,
    numberOfCompoundPayments,
    nextPaymentToBePaid,
    lastPaidPayment,
  } = payment;

  const {
    amount,
    currency,
    editable,
    lender,
    borrower,
    id,
    report,
    paymentFrequency,
    issueDate,
    maturityDate,
    rateType,
  } = loan;
  const numberDisplayOptions = { decimalPoint: userInfo.decimalPoint };

  return {
    id,
    paymentId: payment.id,
    isPrincipalPayment,
    nextPaymentToBePaid,
    lastPaidPayment,
    isLastPayment: paymentNumber === numberOfCompoundPayments,
    editable,
    lender: lender?.name,
    borrower: borrower?.name,
    currency,
    paymentNumberNum: paymentNumber,
    paymentAmount: displayNumber2(paymentAmount, numberDisplayOptions),
    compoundedInterest: displayNumber2(balloonPayment.compoundedInterest, numberDisplayOptions),
    paymentDueDate: formatDateString(paymentDueDate, userInfo.dateFormat) || '-',
    paymentDueDateNotFormatted: paymentDueDate,
    compoundingPeriodEndDate: formatDateString(balloonPayment?.compoundingPeriodEndDate, userInfo.dateFormat) || '-',
    compoundingPeriodEndDateNotFormatted: balloonPayment?.compoundingPeriodEndDate,
    isPaid: isPaid ? 'Published' : 'Unpublished',
    loanType: 'Balloon',
    paymentNumber: `${paymentNumber} of ${numberOfCompoundPayments}`,
    finalInterestRate: `${report?.finalInterestRate} ${getReportUnit(rateType, true)}`,
    principalAmount: displayNumber2(amount, numberDisplayOptions),
    paymentFrequency,
    issueDate: formatDateString(issueDate, userInfo.dateFormat),
    issueDateNotFormatted: issueDate,
    maturityDate: formatDateString(maturityDate, userInfo.dateFormat),
    maturityDateNotFormatted: maturityDate,
    referenceRate: rateType?.referenceRate || '-',
    referenceRateMaturity: rateType?.referenceRateMaturity || '',
    rateType: 'Float',
  };
}

export function getLoanFloatBalloonPaymentsData(data = [], userInfo) {
  return data.map((item) => getLoanData(item, userInfo));
}

const columns = [
  {
    label: 'Lender',
    sortBy: 'lender',
    value: 'lender',
    renderCustomCell: ({ lender, isPrincipalPayment }) => (
      <FlexLayout alignItems="center" space={2}>
        {isPrincipalPayment && (
          <>
            <Box
              bg="shakespeare"
              data-tip
              data-for="principalPayment"
              sx={{ height: '8px', width: '8px', borderRadius: 'round' }}
            />
            <ReactTooltip id="principalPayment">Principal Interest</ReactTooltip>
          </>
        )}
        <Text variant="m-spaced">{lender}</Text>
      </FlexLayout>
    ),
  },
  { label: 'Borrower', sortBy: 'borrower', value: 'borrower' },
  { label: 'Loan type', sortBy: 'loanType', value: 'loanType' },
  { label: 'Rate type', sortBy: 'rateType', value: 'rateType' },
  { label: 'Currency', sortBy: 'currency', value: 'currency' },
  { label: 'Interest amount', sortBy: 'paymentAmount', value: 'paymentAmount', justifyContent: 'flex-end' },
  { label: 'Interest due', sortBy: 'paymentDueDate', value: 'paymentDueDate' },
  { label: 'Compounding period end date', sortBy: 'compoundingPeriodEndDate', value: 'compoundingPeriodEndDate' },
  {
    label: 'Status',
    sortBy: 'isPaid',
    value: 'isPaid',
    // important isPaid is a string for export
    renderCustomCell: ({ isPaid }) => (
      <Text color={isPaid === 'Published' ? 'deep-sapphire' : 'blaze-orange'} variant="m-spaced">
        {isPaid === 'Published' ? 'Published' : 'Unpublished'}
      </Text>
    ),
  },
  { label: 'Compounded interest', sortBy: 'compoundedInterest', value: 'compoundedInterest' },
  { label: 'Interest number', sortBy: 'paymentNumber', value: 'paymentNumber', justifyContent: 'flex-end' },
  { label: 'Interest frequency', sortBy: 'paymentFrequency', value: 'paymentFrequency' },
  { label: 'Issue date', sortBy: 'issueDate', value: 'issueDate' },
  { label: 'Maturity date', sortBy: 'maturityDate', value: 'maturityDate' },
  { label: 'Rate', sortBy: 'finalInterestRate', value: 'finalInterestRate', justifyContent: 'flex-end' },
  { label: 'Principal amount', sortBy: 'principalAmount', value: 'principalAmount', justifyContent: 'flex-end' },
  { label: 'Reference rate', sortBy: 'referenceRate', value: 'referenceRate' },
];

export function getLoanFloatBalloonPaymentsColumns(visibleColumns) {
  return getVisibleColumns({ columns, visibleColumns });
}

export const renderTableActionColumn = ({ item, setPayments, dispatch, setIsReferenceModalShowing, history }) => {
  const options = [];

  options.push({
    label: 'Show all interest',
    onClick: () => {
      history.push(routesEnum.PAYMENTS);
      dispatch(updateField({ loanId: item.id }));
    },
  });

  if (item.nextPaymentToBePaid) {
    options.push({
      label: 'Publish',
      onClick: async () => setIsReferenceModalShowing(item),
    });
  }

  if (item.lastPaidPayment || (item.isLastPayment && item.isPaid === 'Published')) {
    options.push({
      label: 'Unpublish',
      onClick: async () => {
        await markLoanPaymentAsPaid(item.id, item.paymentId, { isPaid: false });
        await getBalloonCompoundPeriodPayments({ id: item.id })
          .then(setPayments)
          .then(() => showToast('Interest unpublished'));
      },
    });
  }

  return <ThreeDotActionMenu options={options} />;
};

export const loanTooltips = {
  lender: 'Select the company that provides the loan.',
  borrower: 'Select the company that receives the loan.',
  startDate: 'Select the start date of interest.',
  endDate: 'Select the end date of interest.',
  currency: 'Select the currency in which the loan is denominated.',
  interestType: 'Select the interest type of the loan.',
  status: 'Select interest status.',
};
