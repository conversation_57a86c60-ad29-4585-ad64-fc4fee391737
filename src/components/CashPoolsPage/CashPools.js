import { format } from 'date-fns';
import { saveAs } from 'file-saver';
import { useContext, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { deleteCashPool, exportAllCashPoolsAsExcel, getCashPools } from '~/api';
import { DeleteModal, ExportCashPoolsModal } from '~/components/Modals';
import { CountryContext } from '~/context';
import { resetCashPool } from '~/reducers/cashPool.slice';
import { routesEnum } from '~/routes';
import { Button, Card, FlexLayout, LoadingSpinner, PageLayout, Table } from '~/ui';
import { showErrorToast, showToast } from '~/ui/components/Toast';

import { columns, getCashPoolsData, renderTableActionColumn } from './CashPools.utils';

const CashPools = () => {
  const dispatch = useDispatch();
  const history = useHistory();
  const [cashPools, setCashPools] = useState();
  const [isLoading, setIsLoading] = useState(true);
  const [showDeleteModal, setShowDeleteModal] = useState(false); // false or set to table row values of cash pool
  const [showExportModal, setShowExportModal] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [dateRange, setDateRange] = useState({ startDate: null, endDate: null });
  const { countriesByName } = useContext(CountryContext);

  const onCreateNewCashPool = () => history.push(`${routesEnum.CASH_POOLS}/new`);

  const onDeleteCashPool = () => {
    deleteCashPool(showDeleteModal.id)
      .then(() => {
        setCashPools(cashPools.filter((c) => c.id !== showDeleteModal.id));
        showToast('Cash Pool deleted successfully');
        setShowDeleteModal(false);
      })
      .catch(() => showErrorToast());
  };

  const handleExport = async () => {
    try {
      setIsExporting(true);
      const file = await exportAllCashPoolsAsExcel({
        startDate: !dateRange.startDate ? null : format(new Date(String(dateRange.startDate)), 'yyyy-MM-dd'),
        endDate: !dateRange.endDate ? null : format(new Date(String(dateRange.endDate)), 'yyyy-MM-dd'),
      });
      saveAs(file, file.name);
      showToast('Sheet has been successfully exported.');
    } catch (err) {
      showErrorToast();
    } finally {
      setIsExporting(false);
    }
  };

  useEffect(() => {
    dispatch(resetCashPool());
    getCashPools()
      .then((cashPools) => {
        setCashPools(cashPools);
        setIsLoading(false);
      })
      .catch(() => showErrorToast());
  }, [dispatch]);

  if (isLoading) return <LoadingSpinner />;

  return (
    <PageLayout
      title="Cash Pools"
      rightTitleContent={
        <FlexLayout alignItems="center" space={6}>
          <Button
            iconLeft="export"
            size="s"
            text="Export"
            variant="secondary"
            onClick={() => setShowExportModal(true)}
          />
          <Button iconLeft="add" size="s" text="New cash pool" variant="secondary" onClick={onCreateNewCashPool} />
        </FlexLayout>
      }
    >
      <Card pb={3} pt={6}>
        <FlexLayout flexDirection="column">
          <Table
            actionColumn={(item) => renderTableActionColumn(item, history, setShowDeleteModal)}
            columns={columns}
            data={getCashPoolsData(cashPools, countriesByName)}
            isSearchable
            onItemClick={({ id }) => history.push(`${routesEnum.CASH_POOLS}/${id}`)}
          />
        </FlexLayout>
      </Card>
      {showDeleteModal && (
        <DeleteModal
          item="Cash Pool"
          handleOnDeleteClick={onDeleteCashPool}
          handleOnHide={() => setShowDeleteModal(false)}
        />
      )}
      {showExportModal && (
        <ExportCashPoolsModal
          dateRange={dateRange}
          setDateRange={setDateRange}
          handleOnClick={handleExport}
          handleOnHide={() => {
            setDateRange({ startDate: null, endDate: null });
            setShowExportModal(false);
          }}
          isExporting={isExporting}
        />
      )}
    </PageLayout>
  );
};

export default CashPools;
