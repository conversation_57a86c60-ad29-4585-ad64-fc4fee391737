import { ThreeDotActionMenu } from '~/components/Shared';
import { routesEnum } from '~/routes';

function getCashPoolData(cashPool, countriesByName) {
  const { id, name, country, type, currencies, leader } = cashPool;
  return {
    id,
    cashPoolName: name,
    country: `${countriesByName[country]?.flagEmoji} ${country}`,
    type,
    currencies,
    cashPoolLeader: leader.name,
  };
}

export function getCashPoolsData(data = [], countriesByName) {
  return data.map((item) => getCashPoolData(item, countriesByName));
}

export const columns = [
  { label: 'Cash pool name', sortBy: 'cashPoolName', value: 'cashPoolName' },
  { label: 'Country', sortBy: 'country', value: 'country' },
  { label: 'Type', sortBy: 'type', value: 'type' },
  { label: 'Currencies', sortBy: 'currencies', value: 'currencies' },
  { label: 'Cash pool leader', sortBy: 'cashPoolLeader', value: 'cashPoolLeader' },
];

function getCashPoolSheetData(data = [], countriesByName) {
  return data.map((item) => {
    const sheetData = {};
    const loanData = getCashPoolData(item, countriesByName); // TODO
    for (let i = 0; i < columns.length; i++) {
      const column = columns[i];
      sheetData[column.label] = loanData[column.value];
    }

    return sheetData;
  });
}

export const getSheetData = (cashPools, countriesByName) => [
  { sheetData: getCashPoolSheetData(cashPools, countriesByName), sheetName: 'Cash Pools' },
];

export const renderTableActionColumn = (item, history, setShowDeleteModal) => {
  const options = [
    {
      label: 'Edit',
      onClick: () => history.push(`${routesEnum.CASH_POOLS}/${item.id}/edit`),
    },
    {
      label: 'Audit Trail',
      onClick: () => history.push(`${routesEnum.CASH_POOLS}/${item.id}/audit-trail`),
    },
    {
      label: 'Statement Data',
      onClick: () => history.push(`${routesEnum.CASH_POOLS}/${item.id}/statement-data`),
    },
    {
      label: 'Uploaded Files',
      onClick: () => history.push(`${routesEnum.CASH_POOLS}/${item.id}/uploaded-files`),
    },
    {
      label: 'Delete',
      onClick: () => setShowDeleteModal(item),
    },
  ];

  return <ThreeDotActionMenu options={options} />;
};
