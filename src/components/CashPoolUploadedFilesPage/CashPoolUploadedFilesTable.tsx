import { useCallback, useContext, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

import { deleteStatementDataFile, getCashPoolStatementFiles, getStatementDataFile } from 'api';
import { DeleteModal } from 'components/Modals';
import { UserInfoContext } from 'context/user';
import { statementDataFiltersSelector, updateField, updatePagination } from 'reducers/statementDataFilters.slice';
import { StatementDataType } from 'types';
import { LoadingSpinner, Pagination, Table } from 'ui';
import { showToast } from 'ui/components/Toast';
import { errorHandler } from 'utils/errors';

import saveAs from 'file-saver';
import { useAppDispatch, useAppSelector } from 'hooks';
import {
  columns,
  getCashPoolStatementsDataFile,
  renderTableActionColumn,
  TableRowType,
} from './CashPoolUploadedFilesTable.utils';

const StatementsTable = () => {
  const dispatch = useAppDispatch();
  const statementDataFilters = useAppSelector(statementDataFiltersSelector);
  const { cashPoolId } = useParams<{ cashPoolId: string }>();
  const { userInfo } = useContext(UserInfoContext);
  const [statementDataFiles, setStatementDataFiles] = useState<Array<StatementDataType>>();
  const [statementDataFilesCount, setStatementDataFilesCount] = useState(0);
  const [isDeleteModalShowing, setIsDeleteModalShowing] = useState<TableRowType | false>(false);
  const { sort, limit, offset } = statementDataFilters;

  const getAndSetStatementsFiles = useCallback(async () => {
    getCashPoolStatementFiles({ cashPoolId, ...statementDataFilters })
      .then(({ data, count }: any) => {
        setStatementDataFiles(data);
        setStatementDataFilesCount(count);
      })
      .catch(errorHandler);
  }, [cashPoolId, statementDataFilters]);

  const handleOnDownloadClick = async (fileId: number) => {
    try {
      const file = await getStatementDataFile({ cashPoolId, fileId });
      saveAs(file, file.name);
      showToast(`${file.name} successfully downloaded.`);
    } catch (error) {
      errorHandler(error);
    }
  };

  const handleOnDeleteClick = async () => {
    try {
      if (!isDeleteModalShowing) return;
      await deleteStatementDataFile({ cashPoolId, id: isDeleteModalShowing.id });
      await getAndSetStatementsFiles();
      setIsDeleteModalShowing(false);
      showToast('Successfully deleted.');
    } catch (error) {
      errorHandler(error);
    }
  };

  useEffect(() => {
    getAndSetStatementsFiles();
  }, [getAndSetStatementsFiles]);

  if (!statementDataFiles) return <LoadingSpinner />;

  return (
    <>
      <Table
        actionColumn={(item: TableRowType) =>
          renderTableActionColumn({ item, setIsDeleteModalShowing, handleOnDownloadClick })
        }
        columns={columns}
        data={getCashPoolStatementsDataFile(statementDataFiles, userInfo)}
        isPaginationHidden
        serverSideSorting
        updateSorting={(id: string) => dispatch(updateField({ sort: id }))}
        sortedBy={sort}
      />
      <Pagination
        canNextPage={statementDataFilesCount - offset * limit > offset * limit}
        canPreviousPage={offset !== 0}
        pageCount={statementDataFilesCount / limit}
        forcePage={offset}
        onPageChange={({ selected }: { selected: number }) => dispatch(updatePagination({ offset: selected }))}
        isShowing={statementDataFilesCount > limit}
      />
      <DeleteModal
        isShowing={!!isDeleteModalShowing}
        item="Statement Data file"
        additionalInfo="Deleting this file will remove it from the file archive, but it will not affect the underlying Statement Data. If you’d like to delete the data associated with this file, please do so in the Statement Data section."
        handleOnDeleteClick={handleOnDeleteClick}
        handleOnHide={() => setIsDeleteModalShowing(false)}
      />
    </>
  );
};

export default StatementsTable;
