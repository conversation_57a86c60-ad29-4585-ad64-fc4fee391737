import { ThreeDotActionMenu } from 'components/Shared';
import { StatementDataType, UserInfoType } from 'types';
import { formatDateString } from 'utils/dates';

export type TableRowType = {
  id: number;
  startDate: string;
  endDate: string;
  name: string;
};

function getCashPoolStatementDataFile(data: any, userInfo: UserInfoType) {
  const { dateFormat } = userInfo;

  return {
    id: data.id,
    name: data.name,
    startDate: formatDateString(data.startDate, dateFormat),
    endDate: formatDateString(data.endDate, dateFormat),
    createdAt: formatDateString(data.createdAt, dateFormat, true),
    user: data.createdByUser.fullName || data.createdByUser.username,
  };
}

export function getCashPoolStatementsDataFile(data: Array<StatementDataType> = [], userInfo: UserInfoType) {
  return data.map((item) => getCashPoolStatementDataFile(item, userInfo));
}

export const columns = [
  { label: 'Name', sortBy: 'name', value: 'name' },
  { label: 'Start Date', sortBy: 'startDate', value: 'startDate' },
  { label: 'End Date', sortBy: 'endDate', value: 'endDate' },
  { label: 'Date Uploaded', sortBy: 'createdAt', value: 'createdAt' },
  { label: 'User', sortBy: 'user', value: 'user' },
];

export const renderTableActionColumn = ({
  item,
  handleOnDownloadClick,
  setIsDeleteModalShowing,
}: {
  item: TableRowType;
  handleOnDownloadClick: (fileId: number) => Promise<void>;
  setIsDeleteModalShowing: React.Dispatch<React.SetStateAction<false | TableRowType>>;
}) => {
  const options = [];

  options.push({
    label: 'Download',
    onClick: () => handleOnDownloadClick(item.id),
  });
  options.push({
    label: 'Delete',
    onClick: () => setIsDeleteModalShowing(item),
  });

  return <ThreeDotActionMenu options={options} />;
};
