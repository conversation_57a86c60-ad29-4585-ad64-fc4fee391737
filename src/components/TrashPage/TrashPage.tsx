import { useEffect, useState, useContext } from 'react';
import { useHistory } from 'react-router-dom';

import { UserInfoContext } from 'context/user';
import { useQuery, useDeletedReports } from 'hooks';
import { routesEnum } from 'routes';
import { REPORT_TYPE, reportEnum } from 'enums';
import { LoansTable, BackToBackLoansTable, GuaranteesTable, CreditRatingsTable } from 'components/Shared';
import { ReportTypeType, TabType, ReportsTabValueType } from 'types';
import { PageLayout, Card, LoadingSpinner, FlexLayout, Tabs } from 'ui';
import { genericTabSetter } from 'utils/tabs';

const TrashPage = () => {
  const history = useHistory();
  const query = useQuery();
  const { userInfo } = useContext(UserInfoContext);
  const [reportType, setReportType] = useState<ReportTypeType>();
  const [tabs, setTabs] = useState<TabType<ReportsTabValueType>[]>([]);
  const { isLoading, deletedGuarantees, deletedLoans, deletedB2BLoans, deletedCreditRatings, setRefreshTrigger } =
    useDeletedReports();

  useEffect(() => setReportType((query.get(REPORT_TYPE) as ReportTypeType) || reportType), [query, reportType]);

  useEffect(() => {
    const { features } = userInfo;
    const tabs = [
      { isEnabled: features.loan, label: 'Loans', value: reportEnum.LOAN },
      { isEnabled: features.backToBackLoan, label: 'Back-To-Back Loans', value: reportEnum.BACK_TO_BACK_LOAN },
      { isEnabled: features.guarantee, label: 'Guarantees', value: reportEnum.GUARANTEE },
      { isEnabled: features.creditRating, label: 'Credit Ratings', value: reportEnum.CREDIT_RATING },
    ];
    genericTabSetter(setTabs, setReportType, tabs);
  }, [userInfo]);

  const onTabSelect = (tabName: ReportsTabValueType) =>
    history.replace(`${routesEnum.TRASH}?${REPORT_TYPE}=${tabName}`);

  return (
    <PageLayout title="Trash">
      <Card pb={3} pt={6}>
        <FlexLayout alignItems="center" justifyContent="space-between">
          <Tabs selectedTab={reportType} tabs={tabs} onTabSelect={onTabSelect} />
        </FlexLayout>
        {isLoading ? (
          <LoadingSpinner />
        ) : (
          <>
            {reportType === reportEnum.LOAN && (
              <LoansTable isEditable data={deletedLoans} setRefreshTrigger={setRefreshTrigger} isSearchable />
            )}
            {reportType === reportEnum.BACK_TO_BACK_LOAN && (
              <BackToBackLoansTable
                isEditable
                data={deletedB2BLoans}
                setRefreshTrigger={setRefreshTrigger}
                isSearchable
              />
            )}
            {reportType === reportEnum.GUARANTEE && (
              <GuaranteesTable isEditable data={deletedGuarantees} setRefreshTrigger={setRefreshTrigger} isSearchable />
            )}
            {reportType === reportEnum.CREDIT_RATING && (
              <CreditRatingsTable
                isEditable
                data={deletedCreditRatings}
                setRefreshTrigger={setRefreshTrigger}
                isSearchable
              />
            )}
          </>
        )}
      </Card>
    </PageLayout>
  );
};

export default TrashPage;
