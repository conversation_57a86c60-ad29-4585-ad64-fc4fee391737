import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button, Modal, NumberInput } from 'ui';
import { updateAccounts, cashPoolTopCurrencyAccountSelector } from 'reducers/cashPoolTopCurrencyAccount.slice';

type AccountBalanceModalPropsType = {
  dataTestId?: string;
  participant: any;
  onHide: () => void;
};

function AccountBalanceModal({ participant, dataTestId, onHide }: AccountBalanceModalPropsType) {
  const [balance, setBalance] = useState(null);
  const dispatch = useDispatch();
  const topCurrencyAccount = useSelector(cashPoolTopCurrencyAccountSelector);
  const account = topCurrencyAccount?.accounts?.find((account: any) => account?.companyId === participant?.id);

  const handleOnSubmitClick = () => {
    const data = { ...account, balance };
    dispatch(updateAccounts({ ...data, value: true }));
    onHide();
  };

  useEffect(() => {
    if (account) {
      setBalance(account.balance);
    }

    return () => {
      setBalance(null);
    };
  }, [account]);

  if (!participant) return null;

  return (
    <Modal
      actionButtons={<Button text="Submit" onClick={handleOnSubmitClick} dataTestId={dataTestId} />}
      title={`Edit balance of ${account?.participant?.company?.name || account?.name}`}
      width="s"
      onHide={onHide}
    >
      <NumberInput
        inputType="float"
        allowNegatives
        label="Participant's balance"
        width="fullWidth"
        value={balance}
        onChange={setBalance}
      />
    </Modal>
  );
}

export default AccountBalanceModal;
