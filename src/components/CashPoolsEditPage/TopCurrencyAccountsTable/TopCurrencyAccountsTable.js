import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { DeleteModal } from '~/components/Modals';
import { ReportsCard } from '~/components/Shared';
import { NORDIC } from '~/enums';
import { cashPoolSelector, removeTopCurrencyAccount, setIsPristine } from '~/reducers/cashPool.slice';
import { routesEnum } from '~/routes';
import { Button, FlexLayout, Table } from '~/ui';

import {
  getTopCurrencyAccountsColumns,
  getTopCurrencyAccountsData,
  renderActionColumn,
} from './TopCurrencyAccountsTable.utils';

const TopCurrencyAccountsTable = () => {
  const dispatch = useDispatch();
  const { topCurrencyAccounts, type } = useSelector(cashPoolSelector);
  const history = useHistory();
  const [isDeleteModalShowing, setIsDeleteModalShowing] = useState(false); // false or set to table row values of top currency account
  const isNordic = type === NORDIC;

  const onDeleteClick = () => {
    dispatch(removeTopCurrencyAccount(isDeleteModalShowing));
    setIsDeleteModalShowing(false);
  };

  if (!isNordic) return null;

  return (
    <>
      <ReportsCard
        title="Currency Top Accounts"
        table={
          <Table
            rightTitleContent={
              <FlexLayout alignItems="center" space={6}>
                <Button
                  iconLeft="add"
                  size="s"
                  text="Top Account"
                  variant="secondary"
                  onClick={() => {
                    dispatch(setIsPristine());
                    setTimeout(() => history.push(routesEnum.CASH_POOL_TOP_CURRENCY_ACCOUNT_NEW), 0);
                  }}
                />
              </FlexLayout>
            }
            actionColumn={(topCurrencyAccount) =>
              renderActionColumn({ topCurrencyAccount, setIsDeleteModalShowing, history })
            }
            columns={getTopCurrencyAccountsColumns()}
            data={getTopCurrencyAccountsData(topCurrencyAccounts)}
            isSearchable
          />
        }
      />
      <DeleteModal
        isShowing={isDeleteModalShowing}
        item="Top Currency Account"
        handleOnDeleteClick={onDeleteClick}
        handleOnHide={() => setIsDeleteModalShowing(false)}
      />
    </>
  );
};

export default TopCurrencyAccountsTable;
