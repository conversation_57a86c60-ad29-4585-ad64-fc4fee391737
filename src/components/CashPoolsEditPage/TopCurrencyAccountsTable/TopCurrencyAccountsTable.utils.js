import React from 'react';

import { ThreeDotActionMenu } from '~/components/Shared';

export const getTopCurrencyAccountsColumns = () => {
  return [
    { label: 'Currency Top Account Name', sortBy: 'name', value: 'name', width: 250, wrapText: true },
    { label: 'Currency', sortBy: 'currency', value: 'currency' },
    { label: 'Number of Participants', sortBy: 'numberOfParticipants', value: 'numberOfParticipants' },
  ];
};

const getTopCurrencyAccountData = ({ topCurrencyAccount }) => {
  const { id, topCurrencyAccountTempId, name, currency, accounts } = topCurrencyAccount;

  return {
    id,
    topCurrencyAccountTempId,
    name,
    currency,
    numberOfParticipants: accounts?.length,
  };
};

export const getTopCurrencyAccountsData = (data = []) => {
  return data.map((topCurrencyAccount) => getTopCurrencyAccountData({ topCurrencyAccount }));
};

export const renderActionColumn = ({ topCurrencyAccount, setIsDeleteModalShowing, history }) => {
  const id = topCurrencyAccount.id || topCurrencyAccount.topCurrencyAccountTempId;
  const options = [
    { label: 'Edit', onClick: () => history.push(`/cash-pools/top-currency-account/${id}/edit`) },
    { label: 'Delete', onClick: () => setIsDeleteModalShowing(topCurrencyAccount) },
  ];

  return <ThreeDotActionMenu options={options} />;
};
