import _ from 'lodash';
import { useCallback, useContext, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import {
  createNordicCashPool,
  createNotionalCashPool,
  createPhysicalCashPool,
  getCashPool,
  updateEntireNordicCashPool,
  updateEntireNotionalCashPool,
  updateEntirePhysicalCashPool,
} from '~/api';
import {
  AuditTrailModal,
  NordicFieldsCard,
  NordicPhysicalFunctionalAnalysisCard,
  NotionalFieldsCard,
  NotionalFunctionalAnalysisCard,
  PhysicalFieldsCard,
} from '~/components/Shared';
import { WithTooltip } from '~/ui/hocs';
import { UserInfoContext } from '~/context/user';
import { NORDIC, NOTIONAL, PHYSICAL } from '~/enums';
import { useUnsavedChangesWarning } from '~/hooks';
import {
  cashPoolSelector,
  resetCashPool,
  setCashPool,
  setIsPristine,
  updateCashPoolType,
} from '~/reducers/cashPool.slice';
import {
  cashPoolTopCurrencyAccountSelector,
  resetAccounts,
  setCashPoolTopCurrencyAccount,
} from '~/reducers/cashPoolTopCurrencyAccount.slice';
import { routesEnum } from '~/routes';
import { Button, Card, FlexLayout, LoadingSpinner, PageLayout, Tabs } from '~/ui';
import { showErrorToast, showToast } from '~/ui/components/Toast';
import { errorHandler } from '~/utils/errors';
import { genericTabSetter } from '~/utils/tabs';

import {
  getCreateNordicCashPoolDto,
  getCreateNotionalCashPoolDto,
  getCreatePhysicalCashPoolDto,
  getRequiredNordicCashPoolFields,
  getRequiredNotionalCashPoolFields,
  getRequiredPhysicalCashPoolFields,
  getUpdateNordicCashPoolDto,
  getUpdateNotionalCashPoolDto,
  getUpdatePhysicalCashPoolDto,
  isNotionalCashPoolValid,
  isPhysicalCashPoolValid,
} from './CashPoolsEdit.utils';
import PhysicalNotionalSelectParticipantsTable from './PhysicalNotionalSelectParticipantsTable';
import TopCurrencyAccountsTable from './TopCurrencyAccountsTable';

const updateAndCreateButtonTooltip = 'All fields are required and Functional Analysis must be calculated.';

const CashPoolsEdit = () => {
  const history = useHistory();
  const { cashPoolId } = useParams();
  const dispatch = useDispatch();
  const { userInfo } = useContext(UserInfoContext);
  const cashPool = useSelector(cashPoolSelector);
  const topCurrencyAccount = useSelector(cashPoolTopCurrencyAccountSelector);
  const { country, type, isDirty } = cashPool;
  const [Prompt] = useUnsavedChangesWarning({ isDirty });
  const { accounts } = topCurrencyAccount;
  const [isAuditTrailModalShowing, setIsAuditTrailModalShowing] = useState(false);
  const [tabs, setTabs] = useState([]);
  const isEdit = history.location.pathname.includes('/edit');
  const { features } = userInfo;

  const onCreatePhysicalCashPool = () => {
    isPhysicalCashPoolValid({ cashPool, topCurrencyAccount, dispatch });
    return createPhysicalCashPool(getCreatePhysicalCashPoolDto(cashPool, accounts));
  };

  const onCreateNordicCashPool = () => {
    return createNordicCashPool(getCreateNordicCashPoolDto(cashPool));
  };

  const onCreateNotionalCashPool = () => {
    isNotionalCashPoolValid({ cashPool, topCurrencyAccount, dispatch });
    return createNotionalCashPool(getCreateNotionalCashPoolDto(cashPool, accounts));
  };

  const onUpdatePhysicalCashPool = (shouldCreateAuditTrail) => {
    isPhysicalCashPoolValid({ cashPool, topCurrencyAccount, dispatch });
    return updateEntirePhysicalCashPool({
      cashPoolId,
      data: getUpdatePhysicalCashPoolDto(cashPool, accounts, shouldCreateAuditTrail),
    });
  };

  const onUpdateNordicCashPool = (shouldCreateAuditTrail) => {
    return updateEntireNordicCashPool({
      cashPoolId,
      data: getUpdateNordicCashPoolDto(cashPool, shouldCreateAuditTrail),
    });
  };

  const onUpdateNotionalCashPool = (shouldCreateAuditTrail) => {
    isNotionalCashPoolValid({ cashPool, topCurrencyAccount, dispatch });
    return updateEntireNotionalCashPool({
      cashPoolId,
      data: getUpdateNotionalCashPoolDto(cashPool, accounts, shouldCreateAuditTrail),
    });
  };

  const poolMethods = {
    [PHYSICAL]: {
      createCashPool: onCreatePhysicalCashPool,
      updateCashPool: onUpdatePhysicalCashPool,
      getRequiredFields: getRequiredPhysicalCashPoolFields,
    },
    [NORDIC]: {
      createCashPool: onCreateNordicCashPool,
      updateCashPool: onUpdateNordicCashPool,
      getRequiredFields: getRequiredNordicCashPoolFields,
    },
    [NOTIONAL]: {
      createCashPool: onCreateNotionalCashPool,
      updateCashPool: onUpdateNotionalCashPool,
      getRequiredFields: getRequiredNotionalCashPoolFields,
    },
  };

  const onCreateCashPool = async () => {
    try {
      const createdCashPool = await poolMethods[type].createCashPool();
      dispatch(resetCashPool());
      dispatch(resetAccounts());

      history.replace(`${routesEnum.CASH_POOLS}/${createdCashPool.id}`);
      showToast('Cash Pool created successfully.');
    } catch (err) {
      errorHandler(err);
    }
  };

  const onUpdateCashPool = async (shouldCreateAuditTrail) => {
    try {
      const updatedCashPool = await poolMethods[type].updateCashPool(shouldCreateAuditTrail);
      dispatch(resetCashPool());
      dispatch(resetAccounts());

      history.replace(`${routesEnum.CASH_POOLS}/${updatedCashPool.id}`);
      showToast('Cash Pool updated successfully.');
    } catch (err) {
      errorHandler(err);
    }
  };

  const isCreateCashPoolDisabled = () => {
    const requiredFields = poolMethods[type].getRequiredFields(cashPool);
    return !_.every(requiredFields, (field) => ![null, '', NaN].includes(field));
  };

  const onTabSelect = useCallback(
    (type) => {
      dispatch(resetAccounts());
      dispatch(updateCashPoolType({ type }));
    },
    [dispatch]
  );

  useEffect(() => {
    /** Prevent resetting to different tab after creating/editing top currency account in Nordic cash pools. */
    if (country) return;

    const recentlyPricesTabs = [
      { isEnabled: features.physicalCashPool, label: PHYSICAL, value: PHYSICAL },
      { isEnabled: features.notionalCashPool, label: NOTIONAL, value: NOTIONAL },
      { isEnabled: features.nordicCashPool, label: NORDIC, value: NORDIC },
    ];
    genericTabSetter(setTabs, onTabSelect, recentlyPricesTabs);
  }, [country, features, onTabSelect]);

  useEffect(() => {
    /**
     * Check of country is here to not refetch and overwrite data after returning from creating/editing top currency accounts in Nordic cash pools.
     * Since it cannot be changed, that is set to null, data will be fetched only once to setup the cash pool.
     */
    if (isEdit && !country) {
      getCashPool({ cashPoolId })
        .then((cashPool) => {
          /**
           * cashPoolTopCurrencyAccount.slice is used to store participants of physical and notional cash pool.
           * See getCreatePhysicalCashPoolDto for more details.
           */
          if ([PHYSICAL, NOTIONAL].includes(cashPool.type)) {
            dispatch(setCashPoolTopCurrencyAccount(cashPool.topCurrencyAccounts[0]));
          }
          dispatch(setCashPool(cashPool));
          dispatch(setIsPristine());
        })
        .catch((err) => {
          if (err?.response?.status === 404) {
            history.push(routesEnum.CASH_POOLS);
            return showErrorToast(err?.response?.data?.message);
          }
          showErrorToast();
        });
    }

    return () => {
      // We don't want the form to reset when creating/editing top currency account.
      // Since that page change triggers useEffect cleanup, condition is added to skip.
      if (!history.location.pathname.includes('/cash-pools/top-currency-account')) {
        dispatch(resetCashPool());
        dispatch(resetAccounts());
      }
    };
    // eslint-disable-next-line
  }, [cashPoolId, dispatch, history, isEdit]);

  if (isEdit && !country) return <LoadingSpinner />;

  return (
    <PageLayout title={isEdit ? 'Edit Cash Pool' : 'New Cash Pool'}>
      <FlexLayout flexDirection="column">
        <Card pb={6} pt={6}>
          {!isEdit && (
            <FlexLayout alignItems="center" justifyContent="space-between">
              <Tabs selectedTab={type} tabs={tabs} onTabSelect={onTabSelect} />
            </FlexLayout>
          )}
          <PhysicalFieldsCard />
          <NordicFieldsCard />
          <NotionalFieldsCard />
        </Card>
      </FlexLayout>

      <NordicPhysicalFunctionalAnalysisCard />
      <NotionalFunctionalAnalysisCard />

      <PhysicalNotionalSelectParticipantsTable />

      <TopCurrencyAccountsTable />

      <FlexLayout alignItems="flex-end" flexGrow="1" justifyContent="space-between">
        <Button variant="gray" text="Cancel" onClick={history.goBack} />
        {isEdit ? (
          <WithTooltip
            tooltip={updateAndCreateButtonTooltip}
            label="UpdateCashPool"
            disabled={isCreateCashPoolDisabled()}
          >
            <Button
              text="Update"
              disabled={isCreateCashPoolDisabled()}
              onClick={() => setIsAuditTrailModalShowing(true)}
            />
          </WithTooltip>
        ) : (
          <WithTooltip
            tooltip={updateAndCreateButtonTooltip}
            label="CreateCashPool"
            disabled={isCreateCashPoolDisabled()}
          >
            <Button
              text="Next"
              dataTestId="createCashPoolButton"
              iconRight="arrowRight"
              disabled={isCreateCashPoolDisabled()}
              onClick={onCreateCashPool}
            />
          </WithTooltip>
        )}
      </FlexLayout>
      {Prompt}
      {isAuditTrailModalShowing && (
        <AuditTrailModal
          handleOnCreateClick={() => onUpdateCashPool(true)}
          handleOnUpdateClick={() => onUpdateCashPool(false)}
          onHide={() => setIsAuditTrailModalShowing(false)}
        />
      )}
    </PageLayout>
  );
};

export default CashPoolsEdit;
