import { useEffect } from 'react';

import { Modal, Text } from 'ui';
import { getMyUserInfo } from 'api';

const MaintenanceModal = () => {
  useEffect(() => {
    // eslint-disable-next-line no-console
    getMyUserInfo().then(console.log).catch(console.error);
  }, []);

  return (
    <Modal
      title="Sorry, we're down for maintenance."
      children={<Text color="deep-sapphire">We should be back shortly.</Text>}
      showButtonsSection={false}
      onHide={() => {}}
    />
  );
};

export default MaintenanceModal;
