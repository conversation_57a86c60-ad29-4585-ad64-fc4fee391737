import React, { useCallback, useContext, useEffect, useState } from 'react';
import { useHistory } from 'react-router';

import { deleteNotification, getNotifications, muteNotifications, updateNotificationIsHandled } from '~/api';
import { UserAvatar } from '~/components/Shared';
import { UnhandledNotificationsContext } from '~/context/notification';
import { UserInfoContext } from '~/context/user';
import { LIMIT, NOTIFICATION_ACTION_TEXT, OFFSET } from '~/enums';
import { useQuery } from '~/hooks';
import { routesEnum } from '~/routes';
import { Box, Button, Card, FlexLayout, LoadingSpinner, PageLayout, Pagination, Text } from '~/ui';
import { showErrorToast, showToast } from '~/ui/components/Toast';

import NotificationIndicator from './NotificationIndicator';
import {
  ActionMenu,
  getMuteNotificationsButtonText,
  getMuteNotificationsIcon,
  getMuteNotificationsToastText,
} from './NotificationsPage.utils';

const NotificationsPage = () => {
  const history = useHistory();
  const query = useQuery();
  const [notifications, setNotifications] = useState();
  const [notificationCount, setNotificationCount] = useState();
  const { setShouldRefreshUnhandledNotifications } = useContext(UnhandledNotificationsContext);
  const { userInfo, setUserInfo } = useContext(UserInfoContext);
  const ITEMS_PER_PAGE = 10;
  const offset = query.get(OFFSET) || 0;
  const limit = query.get(LIMIT) || ITEMS_PER_PAGE;

  const onSeeReportClick = (id, isHandled, url) => () => {
    if (!isHandled) {
      updateNotificationIsHandled(id, { isHandled: true })
        .then(() => setShouldRefreshUnhandledNotifications(true))
        .catch(() => {});
    }

    history.push(`${url.pathname}${url.search}`);
  };

  const getAndSetNotifications = useCallback(() => {
    getNotifications(offset, limit).then(({ notifications, count }) => {
      setNotifications(notifications);
      setNotificationCount(count);
    });
  }, [offset, limit]);

  const onDeleteNotification = (id) => {
    deleteNotification(id)
      .then(() => {
        /* handles returning to previous page after the last notification on the page is deleted */
        if (notifications.length === 1) {
          if (offset > 1) {
            return history.push({
              pathname: routesEnum.NOTIFICATIONS,
              search: `?${OFFSET}=${offset - 1}&${LIMIT}=${ITEMS_PER_PAGE}`,
            });
          }
          return history.push(routesEnum.NOTIFICATIONS);
        } else {
          getAndSetNotifications();
        }
      })
      .catch(() => showErrorToast());
    setShouldRefreshUnhandledNotifications(true);
  };

  const onMarkAsReadUnread = (id, data) => {
    updateNotificationIsHandled(id, data)
      .then(getAndSetNotifications)
      .catch(() => showErrorToast());
    setShouldRefreshUnhandledNotifications(true);
  };

  const onMuteNotification = () => {
    muteNotifications({ areNotificationsMuted: !userInfo.areNotificationsMuted })
      .then((user) => {
        setUserInfo({ ...userInfo, ...user });
        showToast(getMuteNotificationsToastText(user.areNotificationsMuted));
      })
      .catch(() => showErrorToast());
  };
  useEffect(() => {
    getAndSetNotifications();
  }, [history.location.search, getAndSetNotifications]);

  if (!notifications) return <LoadingSpinner />;

  return (
    <PageLayout
      title="Notifications"
      rightTitleContent={
        <FlexLayout alignItems="center" space={6}>
          <Button
            iconLeft={getMuteNotificationsIcon(userInfo.areNotificationsMuted)}
            text={getMuteNotificationsButtonText(userInfo.areNotificationsMuted)}
            size="s"
            variant="secondary"
            onClick={onMuteNotification}
          />
        </FlexLayout>
      }
    >
      <Card pb={6} pt={6} spaces={0}>
        {!notifications.length ? (
          <Text color="deep-sapphire">No notifications</Text>
        ) : (
          <>
            {notifications.map(({ id: joinedTableId, notification, isHandled }) => (
              <FlexLayout key={joinedTableId} flexDirection="column">
                <FlexLayout alignItems="center">
                  <UserAvatar fullName={notification.createdByUser.fullName} />
                  <Text color="deep-sapphire" variant="m-spaced-bold" sx={{ marginLeft: '8px' }}>
                    {notification.createdByUser.fullName}
                  </Text>
                  <Text color="deep-sapphire" sx={{ marginLeft: '8px' }}>
                    {NOTIFICATION_ACTION_TEXT[notification.action]}
                  </Text>
                  .
                  <Text
                    color="shakespeare"
                    onClick={onSeeReportClick(notification.id, isHandled, new URL(notification.url))}
                    sx={{ marginLeft: '8px' }}
                  >
                    View Report
                  </Text>
                  <NotificationIndicator isHandled={isHandled} />
                  <FlexLayout sx={{ marginLeft: '16px' }}>
                    <ActionMenu
                      notification={notification}
                      isHandled={isHandled}
                      onDeleteNotification={onDeleteNotification}
                      updateNotificationIsHandled={onMarkAsReadUnread}
                    />
                  </FlexLayout>
                </FlexLayout>
                {notification.note && (
                  <Box sx={{ padding: '12px 0 0 40px', maxWidth: '720px' }}>
                    <Text color="bali-hai" variant="s-spaced-italic">
                      {notification.note}
                    </Text>
                  </Box>
                )}
              </FlexLayout>
            ))}
          </>
        )}
        <Pagination
          canNextPage={notificationCount - Number(offset) * ITEMS_PER_PAGE > Number(offset) * ITEMS_PER_PAGE}
          canPreviousPage={Number(offset) !== 0}
          pageCount={notificationCount / ITEMS_PER_PAGE}
          forcePage={Number(offset)}
          onPageChange={({ selected }) =>
            history.push({
              pathname: routesEnum.NOTIFICATIONS,
              search: `?${OFFSET}=${selected}&${LIMIT}=${ITEMS_PER_PAGE}`,
            })
          }
          isShowing={notificationCount > ITEMS_PER_PAGE}
        />
      </Card>
    </PageLayout>
  );
};

export default NotificationsPage;
