import React from 'react';

import ThreeDotActionMenu from '~/components/Shared/ThreeDotActionMenu';

const getIsHandledLabelAndAction = (id, isHandled, updateNotificationIsHandled) => {
  if (isHandled) {
    return { label: 'Mark as Unread', onClick: () => updateNotificationIsHandled(id, { isHandled: false }) };
  }
  return { label: 'Mark as Read', onClick: () => updateNotificationIsHandled(id, { isHandled: true }) };
};

export const ActionMenu = ({ notification, isHandled, onDeleteNotification, updateNotificationIsHandled }) => {
  return (
    <ThreeDotActionMenu
      options={[
        getIsHandledLabelAndAction(notification.id, isHandled, updateNotificationIsHandled),
        {
          label: 'Delete notification',
          onClick: () => onDeleteNotification(notification.id),
        },
      ]}
    />
  );
};

export const getMuteNotificationsButtonText = (areNotificationsMuted) =>
  areNotificationsMuted ? 'Unmute all notifications' : 'Mute all notifications';

export const getMuteNotificationsIcon = (areNotificationsMuted) => (areNotificationsMuted ? 'bell' : 'bellMuted');

export const getMuteNotificationsToastText = (areNotificationsMuted) =>
  areNotificationsMuted ? 'All notifications muted' : 'All notifications unmuted';
