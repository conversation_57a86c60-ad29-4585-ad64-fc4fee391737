import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

import { getAuditTrailTopCurrencyAccount } from '~/api';
import { Box, Card, NumberInput, PageLayout, RadioGroup, TextInput, LoadingSpinner } from '~/ui';
import { CurrencySingleSelect, OvernightRateSingleSelect, AuditTrailParticipantsTable } from '~/components/Shared';
import { showErrorToast } from '~/ui/components/Toast';

const AuditTrailTopCurrencyAccountPage = () => {
  const [topCurrencyAccount, setTopCurrencyAccount] = useState();
  const [isLoading, setIsLoading] = useState(true);
  const { cashPoolId, auditTrailId, auditTrailTopCurrencyAccountId } = useParams();
  const isFixed = topCurrencyAccount?.interestType === 'fixed';

  useEffect(() => {
    getAuditTrailTopCurrencyAccount({ cashPoolId, auditTrailId, auditTrailTopCurrencyAccountId })
      .then((res) => {
        setTopCurrencyAccount(res);
        setIsLoading(false);
      })
      .catch(() => showErrorToast());
  }, [cashPoolId, auditTrailId, auditTrailTopCurrencyAccountId]);

  if (isLoading) return <LoadingSpinner />;

  return (
    <PageLayout title="Currency Top Account Audit Trail">
      <Card disabled p={6}>
        <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
          <TextInput label="Currency Top Account Name" width="fullWidth" value={topCurrencyAccount.name} />
          <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
            <CurrencySingleSelect variant="extended" width="fullWidth" value={topCurrencyAccount.currency} />
          </Box>
        </Box>
        <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
          <RadioGroup
            label="Interest Type"
            options={[
              { label: 'Fixed', value: 'fixed' },
              { label: 'Float', value: 'float' },
            ]}
            width="fullWidth"
            value={topCurrencyAccount.interestType}
          />
          {!isFixed && (
            <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
              <OvernightRateSingleSelect value={topCurrencyAccount.overnightRate} />
            </Box>
          )}
        </Box>
        <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(4, 1fr)' }}>
          <NumberInput
            inputType="float"
            allowNegatives={!isFixed}
            label={isFixed ? 'Credit Interest Rate' : 'Credit Interest Spread'}
            width="fullWidth"
            unit={isFixed ? '%' : ' basis points'}
            value={topCurrencyAccount.creditInterestRate}
          />
          <NumberInput
            inputType="float"
            allowNegatives={!isFixed}
            label={isFixed ? 'Debit Interest Rate' : 'Debit Interest Spread'}
            width="fullWidth"
            unit={isFixed ? '%' : 'basis points'}
            value={topCurrencyAccount.debitInterestRate}
          />
        </Box>
      </Card>
      <AuditTrailParticipantsTable participants={topCurrencyAccount.participants} />
    </PageLayout>
  );
};

export default AuditTrailTopCurrencyAccountPage;
