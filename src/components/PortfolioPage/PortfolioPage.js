import { useContext, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { FilterColumnsModal } from '~/components/Modals';
import { BackToBackLoansTable, CreditRatingsTable, GuaranteesTable, LoansTable } from '~/components/Shared';
import { TableColumnContext } from '~/context/tableColumn';
import { UserInfoContext } from '~/context/user';
import { REPORT_TYPE, reportEnum } from '~/enums';
import { useQuery, useReports } from '~/hooks';
import { backToBackLoanColumns } from '~/reducers/tableColumns/backToBackLoanColumns.slice';
import { guaranteeColumns } from '~/reducers/tableColumns/guaranteeColumns.slice';
import { loanColumns } from '~/reducers/tableColumns/loanColumns.slice';
import { routesEnum } from '~/routes';
import { <PERSON><PERSON>, Card, FlexLayout, LoadingSpinner, PageLayout, Tabs } from '~/ui';
import { showErrorToast, showToast } from '~/ui/components/Toast';
import { jsonToSheet } from '~/utils/documents';
import { genericTabSetter } from '~/utils/tabs';

import { getSheetData } from '../ReportsPage/ReportsPage.utils';

function PortfolioPage() {
  const history = useHistory();
  const query = useQuery();
  const visibleLoanColumns = useSelector(loanColumns);
  const visibleB2BLoanColumns = useSelector(backToBackLoanColumns);
  const visibleGuaranteeColumns = useSelector(guaranteeColumns);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [tabs, setTabs] = useState([]);
  const [reportType, setReportType] = useState();
  const [isLoading, guaranteeReports, loanReports, creditRatings, b2bLoanReports, { setRefreshTrigger }] = useReports({
    limit: null,
    isPortfolio: true,
  });
  const { userInfo } = useContext(UserInfoContext);
  const { dbTableColumns } = useContext(TableColumnContext);

  function handleOnExportTableClick() {
    const sheetData = getSheetData({
      loanReports,
      visibleLoanColumns: dbTableColumns[reportEnum.LOAN] ?? visibleLoanColumns,
      b2bLoanReports,
      visibleB2BLoanColumns,
      guaranteeReports,
      visibleGuaranteeColumns: dbTableColumns[reportEnum.GUARANTEE] ?? visibleGuaranteeColumns,
      creditRatings,
      userInfo,
    });

    jsonToSheet(sheetData, 'Portfolio.xlsx')
      .then(() => showToast('Sheet has been successfully exported.'))
      .catch(() => showErrorToast());
  }

  const onTabSelect = (tabName) => history.replace(`${routesEnum.PORTFOLIO}?${REPORT_TYPE}=${tabName}`);

  useEffect(() => setReportType(query.get(REPORT_TYPE) || reportType), [query, reportType]);

  useEffect(() => {
    const { features } = userInfo;
    const tabs = [
      { isEnabled: features.loan, label: 'Loans', value: reportEnum.LOAN },
      { isEnabled: features.backToBackLoan, label: 'Back-To-Back Loans', value: reportEnum.BACK_TO_BACK_LOAN },
      { isEnabled: features.guarantee, label: 'Guarantees', value: reportEnum.GUARANTEE },
      { isEnabled: features.creditRating, label: 'Credit Ratings', value: reportEnum.CREDIT_RATING },
    ];
    genericTabSetter(setTabs, setReportType, tabs);
  }, [userInfo]);

  if (isLoading) return <LoadingSpinner />;

  return (
    <PageLayout
      rightTitleContent={
        <Button iconLeft="export" size="s" text="Export" variant="secondary" onClick={handleOnExportTableClick} />
      }
      title="Portfolio"
    >
      <Card pb={3} pt={6}>
        <FlexLayout alignItems="center" justifyContent="space-between">
          <Tabs selectedTab={reportType} tabs={tabs} onTabSelect={onTabSelect} />
          {[reportEnum.LOAN, reportEnum.GUARANTEE].includes(reportType) && (
            <Button
              iconLeft="columns"
              size="s"
              text="Columns"
              variant="secondary"
              onClick={() => setShowFilterModal(true)}
            />
          )}
        </FlexLayout>
        {reportType === reportEnum.LOAN && (
          <LoansTable isEditable data={loanReports} setRefreshTrigger={setRefreshTrigger} isSearchable />
        )}
        {reportType === reportEnum.BACK_TO_BACK_LOAN && (
          <BackToBackLoansTable isEditable data={b2bLoanReports} setRefreshTrigger={setRefreshTrigger} isSearchable />
        )}
        {reportType === reportEnum.GUARANTEE && (
          <GuaranteesTable isEditable data={guaranteeReports} setRefreshTrigger={setRefreshTrigger} isSearchable />
        )}
        {reportType === reportEnum.CREDIT_RATING && (
          <CreditRatingsTable isEditable data={creditRatings} setRefreshTrigger={setRefreshTrigger} isSearchable />
        )}
      </Card>
      {showFilterModal && <FilterColumnsModal type={reportType} handleOnHide={() => setShowFilterModal(false)} />}
    </PageLayout>
  );
}

export default PortfolioPage;
