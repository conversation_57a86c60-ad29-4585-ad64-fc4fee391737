import { useContext, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useLocation } from 'react-router-dom';

import { deleteGuarantee, restoreGuarantee } from '~/api';
import { DeleteModal, RestoreModal } from '~/components/Modals';
import { UserInfoContext } from '~/context/user';
import { TableColumnContext } from '~/context/tableColumn';
import { REPORT_TYPE, reportEnum } from '~/enums';
import { guaranteeColumns, setColumns, resetForm } from '~/reducers/tableColumns/guaranteeColumns.slice';
import { routesEnum } from '~/routes';
import { FlexLayout, Table } from '~/ui';
import { showToast } from '~/ui/components/Toast';
import { errorHandler } from '~/utils/errors';

import { getGuaranteesColumns, getGuaranteesData } from './GuaranteesTable.utils';
import { renderTableActionColumn } from '../ReportsTables.utils';

function GuaranteesTable({ isEditable = false, data = [], setRefreshTrigger, isSearchable = false }) {
  const dispatch = useDispatch();
  const history = useHistory();
  const location = useLocation();
  const visibleColumns = useSelector(guaranteeColumns);
  const [guaranteeReports, setGuaranteeReports] = useState(data);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showRestoreModal, setShowRestoreModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const { userInfo } = useContext(UserInfoContext);
  const { dbTableColumns } = useContext(TableColumnContext);

  const isAnalyses = location.pathname === routesEnum.ANALYSES;
  const isPortfolio = location.pathname === routesEnum.PORTFOLIO;
  const isTrash = location.pathname === routesEnum.TRASH;

  useEffect(() => {
    if (isPortfolio) {
      dispatch(setColumns({ Rate: true }));
    }
    if (isTrash) {
      dispatch(setColumns({ Deleted: true }));
    }

    return () => dispatch(resetForm());
  }, [dispatch, isPortfolio, isTrash]);

  function onItemDelete() {
    deleteGuarantee({ id: selectedItem.id, force: isTrash })
      .then(() => {
        setGuaranteeReports(guaranteeReports.filter((guaranteeReport) => guaranteeReport.id !== selectedItem.id));
        setRefreshTrigger((prev) => !prev);
        setShowDeleteModal(false);
        setSelectedItem(null);
        showToast('Guarantee has been successfully deleted.');
      })
      .catch(errorHandler);
  }

  function onItemRestore() {
    restoreGuarantee(selectedItem.id)
      .then(() => {
        setGuaranteeReports(guaranteeReports.filter((guaranteeReport) => guaranteeReport.id !== selectedItem.id));
        setRefreshTrigger((prev) => !prev);
        setShowRestoreModal(false);
        setSelectedItem(null);
        showToast('Guarantee has been successfully restored.');
      })
      .catch(errorHandler);
  }

  return (
    <FlexLayout flexDirection="column">
      <Table
        actionColumn={
          isEditable &&
          ((item) =>
            renderTableActionColumn({
              item,
              reportType: reportEnum.GUARANTEE,
              setShowDeleteModal,
              setShowRestoreModal,
              setSelectedItem,
              history,
              role: userInfo.role,
              dispatch,
              isAnalyses,
              isPortfolio,
              isTrash,
            }))
        }
        columns={getGuaranteesColumns(dbTableColumns[reportEnum.GUARANTEE] ?? visibleColumns)}
        data={getGuaranteesData(guaranteeReports, userInfo)}
        isSearchable={isSearchable}
        onItemClick={
          isSearchable
            ? ({ id }) => history.push(`${location.pathname}/${id}?${REPORT_TYPE}=${reportEnum.GUARANTEE}`)
            : null
        }
      />
      <DeleteModal
        item="guarantee"
        isShowing={showDeleteModal}
        handleOnDeleteClick={onItemDelete}
        handleOnHide={() => {
          setShowDeleteModal(false);
          setSelectedItem(null);
        }}
        isDeletePermanent={isTrash}
      />
      <RestoreModal
        item="guarantee"
        isShowing={showRestoreModal}
        handleOnRestoreClick={onItemRestore}
        handleOnHide={() => {
          setShowRestoreModal(false);
          setSelectedItem(null);
        }}
      />
    </FlexLayout>
  );
}

export default GuaranteesTable;
