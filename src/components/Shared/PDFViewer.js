import '~/styles/react-pdf-overrides.css';

import PT from 'prop-types';
import React, { useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';

import { FlexLayout, Icon, LoadingSpinner, Text } from '~/ui';

// https://github.com/wojtekmaj/react-pdf#create-react-app
if (typeof window !== 'undefined' && 'Worker' in window) {
  pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.js`;
}

function PDFViewer({ file }) {
  const [isLoading, setIsLoading] = useState(true);
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);

  function onLoadSuccess({ numPages }) {
    setNumPages(numPages);
    setIsLoading(false);
  }

  function DocumentPagination() {
    if (isLoading) {
      return null;
    }

    return (
      <FlexLayout alignItems="center" justifyContent="center" space={3}>
        <Icon
          color="deep-sapphire"
          disabled={pageNumber === 1}
          icon="chevronLeft"
          onClick={() => setPageNumber(pageNumber - 1)}
        />
        <Text color="deep-sapphire" variant="m-spaced-medium">
          Page {pageNumber} of {numPages}
        </Text>
        <Icon
          color="deep-sapphire"
          disabled={pageNumber === numPages}
          icon="chevronRight"
          onClick={() => setPageNumber(pageNumber + 1)}
        />
      </FlexLayout>
    );
  }

  const pages = Array.from(new Array(numPages), (_, index) => {
    const pageIndex = index + 1;
    return (
      <Page
        key={`page_${pageIndex}`}
        pageNumber={pageIndex}
        className={pageIndex === pageNumber ? 'pdf_page_active' : 'pdf_page_inactive'}
      />
    );
  });

  return (
    <FlexLayout alignItems="center" flexDirection="column" flexGrow="1" space={4}>
      <DocumentPagination />
      <Document file={file} loading={<LoadingSpinner />} onLoadSuccess={onLoadSuccess}>
        {pages}
      </Document>
      <DocumentPagination />
    </FlexLayout>
  );
}

PDFViewer.propTypes = {
  file: PT.any.isRequired,
};

export default PDFViewer;
