import _ from 'lodash';
import React from 'react';
import { Dispatch } from 'redux';

import { TextWithTooltip, ThreeDotActionMenu } from 'components/Shared';
import { creditRatings } from 'components/Shared/CreditRatingSingleSelect';
import { allCurrencies } from 'components/Shared/CurrencySingleSelect';
import { updateField } from 'reducers/cashPool.slice';
import { updateAccounts } from 'reducers/cashPoolTopCurrencyAccount.slice';
import { AccountType, CompanyDataType, ExternalId, InTableParticipantType } from 'types/cashPool.types';
import { UserType } from 'types/user.types';
import { TableColumnType } from 'types/various.types';
import { Box, Checkbox, FlexLayout, Text } from 'ui';
import { getCompanyRating, getCompanyRatingAdj } from 'utils/companies';
import { displayNumber } from 'utils/strings';

type SelectParticipantsTableColumnValueType =
  | 'name'
  | 'country'
  | 'rating'
  | 'ratingAdj'
  | 'industry'
  | 'cirString'
  | 'dirString'
  | 'currencyString'
  | 'generateInterestStatementDataString'
  | 'externalIds'
  | 'uniqueId';

export const renderActionColumn = ({
  item,
  accounts,
  setIsCirDirModalShowing,
  setIsCurrencyModalShowing,
  setIsBalanceModalShowing,
  setIsExternalIdsShowing,
  setIsUniqueIdModalShowing,
  isNotional,
  isFixed,
  isEdit,
  dispatch,
  initialSelectedAccounts,
}: {
  item: any;
  accounts: any;
  setIsCirDirModalShowing: React.Dispatch<React.SetStateAction<boolean>>;
  setIsCurrencyModalShowing: React.Dispatch<React.SetStateAction<boolean>>;
  setIsBalanceModalShowing: React.Dispatch<React.SetStateAction<boolean>>;
  setIsExternalIdsShowing: React.Dispatch<React.SetStateAction<boolean>>;
  setIsUniqueIdModalShowing: React.Dispatch<React.SetStateAction<boolean>>;
  isNotional: boolean;
  isFixed: boolean;
  isEdit: boolean;
  dispatch: Dispatch<any>;
  initialSelectedAccounts: any[];
}) => {
  const type: string = isFixed ? 'rate' : 'spread';
  const options = [];

  options.push({
    label: `Edit credit and debit ${type}s`,
    onClick: () => {
      setIsCirDirModalShowing(item);
      dispatch(updateField({ isDirty: true }));
    },
  });

  if (item.isSelected) {
    options.push({
      label: 'Edit external IDs',
      onClick: () => {
        setIsExternalIdsShowing(item);
        dispatch(updateField({ isDirty: true }));
      },
    });

    const account = accounts?.find((account: any) => account?.companyId === item.id);
    options.push({
      label: `${item.generateInterestStatementData ? 'Remove' : 'Add'} interest to statement data`,
      onClick: () => {
        dispatch(
          updateAccounts({
            ...account,
            generateInterestStatementData: !item.generateInterestStatementData,
            value: true,
          })
        );
        dispatch(updateField({ isDirty: true }));
      },
    });
  }
  // New account that is being added
  if (
    item.isSelected &&
    (item.balance === null ||
      item.balance === undefined ||
      !initialSelectedAccounts.find((account: any) => account?.companyId === item.id))
  ) {
    options.push({
      label: 'Set initial balance',
      onClick: () => {
        setIsBalanceModalShowing(item);
        dispatch(updateField({ isDirty: true }));
      },
    });
  }

  if (item.isSelected && isEdit) {
    options.push({
      label: 'Add unique ID',
      onClick: () => {
        setIsUniqueIdModalShowing(item);
        dispatch(updateField({ isDirty: true }));
      },
    });
  }

  if (isNotional) {
    options.push({
      label: 'Assign currency',
      onClick: () => {
        setIsCurrencyModalShowing(item);
        dispatch(updateField({ isDirty: true }));
      },
    });
  }

  return <ThreeDotActionMenu options={options} dataTestId="participantsActionMenu" />;
};

/**
 * !WARNING
 * Changing this will change order of columns which will affect the onUploadTemplateClick since it depends on indices of columns
 */
const getPhysicalCompaniesColumns = ({
  dispatch,
  accounts,
  isFixed,
}: {
  dispatch?: Dispatch;
  accounts?: AccountType[];
  isFixed: boolean;
}): TableColumnType<SelectParticipantsTableColumnValueType>[] => {
  return [
    {
      label: 'Company',
      sortBy: 'name',
      value: 'name',
      wrapText: true,
      width: 250,
      renderCustomCell: ({ id, name }: { id: number; name: string }) => {
        if (!dispatch || !accounts) return null;
        return (
          <FlexLayout alignItems="center" space={2}>
            <Checkbox
              isActive={!!accounts.find((account) => account.companyId === id)}
              onChange={(isSelected: boolean) => {
                dispatch(updateAccounts({ companyId: id, value: isSelected }));
                dispatch(updateField({ isDirty: true }));
              }}
            />
            <Text color="deep-sapphire" variant="m-spaced">
              {name}
            </Text>
          </FlexLayout>
        );
      },
    },
    { label: 'Country', sortBy: 'country', value: 'country' },
    { label: 'Rating', sortArray: creditRatings, sortBy: 'rating', sortType: 'array', value: 'rating' },
    { label: 'Sector', sortBy: 'industry', value: 'industry' },
    { label: 'Rating (ADJ.)', sortBy: 'ratingAdj', value: 'ratingAdj' },
    {
      label: isFixed ? 'Credit Rate' : 'Credit Spread',
      sortBy: 'cirString',
      value: 'cirString',
      renderCustomCell: ({ id, cirString }: { id: number; cirString: string }) => {
        if (!accounts) return null;
        const account: AccountType | undefined = accounts.find((p) => p.companyId === id);
        const errorMessage: string | undefined = account?.error?.creditInterestRate;
        return (
          <TextWithTooltip
            id="cirString"
            color={errorMessage ? 'pomegranate' : 'deep-sapphire'}
            label={cirString?.toString()}
            tooltip={errorMessage}
            variant="m-spaced"
          />
        );
      },
    },
    {
      label: isFixed ? 'Debit Rate' : 'Debit Spread',
      sortBy: 'dirString',
      value: 'dirString',
      renderCustomCell: ({ id, dirString }: { id: number; dirString: string }) => {
        if (!accounts) return null;
        const account: AccountType | undefined = accounts.find((p) => p.companyId === id);
        const errorMessage: string = account?.error?.debitInterestRate;
        return (
          <TextWithTooltip
            id="dirString"
            color={errorMessage ? 'pomegranate' : 'deep-sapphire'}
            label={dirString?.toString()}
            tooltip={errorMessage}
            variant="m-spaced"
          />
        );
      },
    },
    {
      label: 'Interest added',
      sortBy: 'interestAdded',
      value: 'generateInterestStatementDataString',
      renderCustomCell: ({ generateInterestStatementData }: { generateInterestStatementData: boolean }) => {
        return <Text>{generateInterestStatementData ? 'Yes' : 'No'}</Text>;
      },
    },
    {
      label: 'External ID',
      sortBy: 'externalIds',
      value: 'externalIds',
      renderCustomCell: ({ externalIds }: { externalIds: ExternalId[] }) => {
        if (!externalIds || _.isEmpty(externalIds)) return <Text>-</Text>;
        return externalIds.map((id: ExternalId, index) => (
          <Box
            key={id.externalId}
            sx={{
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              display: 'block',
              maxWidth: '100%',
            }}
          >
            <Text>{`${id.externalId}${externalIds.length > 1 && index !== externalIds.length - 1 ? ', ' : ''}`}</Text>
          </Box>
        ));
      },
    },
    {
      label: 'Unique ID',
      sortBy: 'uniqueId',
      value: 'uniqueId',
      renderCustomCell: ({ uniqueId }: { uniqueId: string }) => {
        return <Text>{uniqueId || '-'}</Text>;
      },
    },
  ];
};

export const getCompaniesColumns = ({
  dispatch,
  accounts,
  isFixed,
  isNotional,
}: {
  dispatch?: Dispatch;
  accounts?: AccountType[];
  isNotional?: boolean;
  isFixed: boolean;
}): TableColumnType<SelectParticipantsTableColumnValueType>[] => {
  if (isNotional) {
    return [
      ...getPhysicalCompaniesColumns({ dispatch, accounts, isFixed }),
      {
        label: 'Currency',
        sortBy: 'currencyString',
        value: 'currencyString',
        renderCustomCell: ({ id, currencyString }: { id: number; currencyString: string }) => {
          if (!accounts) return null;
          const account: AccountType | undefined = accounts.find((p) => p.companyId === id);
          const errorMessage: string | undefined = account?.error?.currency;
          return (
            <TextWithTooltip
              id="currencyString"
              color={errorMessage ? 'pomegranate' : 'deep-sapphire'}
              label={currencyString}
              tooltip={errorMessage}
              variant="m-spaced"
            />
          );
        },
      },
    ];
  }

  return getPhysicalCompaniesColumns({ dispatch, accounts, isFixed });
};

function getCompanyData({
  company,
  isFixed,
  userInfo,
  account,
}: {
  company: InTableParticipantType;
  isFixed: boolean;
  userInfo: UserType;
  account?: AccountType;
}): CompanyDataType {
  const {
    country,
    id,
    accountId,
    industry,
    name,
    creditInterestRate,
    debitInterestRate,
    currency,
    value,
    generateInterestStatementData,
    uniqueId,
  } = company;
  const unit = isFixed ? '%' : ' bps';

  return {
    id,
    accountId,
    country,
    industry,
    name,
    generateInterestStatementData,
    rating: getCompanyRating(company),
    ratingAdj: getCompanyRatingAdj(company),
    creditInterestRate,
    cirString:
      creditInterestRate != null ? `${displayNumber(creditInterestRate, userInfo.decimalPoint, '-')}${unit}` : '-',
    debitInterestRate,
    dirString:
      debitInterestRate != null ? `${displayNumber(debitInterestRate, userInfo.decimalPoint, '-')}${unit}` : '-',
    currency,
    currencyString: currency || '-',
    isSelected: value || false,
    externalIds: account?.externalIds ?? [],
    uniqueId: uniqueId || '',
    balance: account?.balance,
  };
}

export function getCompaniesData(
  data: InTableParticipantType[] = [],
  isFixed: boolean,
  userInfo: UserType,
  accounts?: AccountType[]
): CompanyDataType[] {
  const sortedData: CompanyDataType[] = data.map((company) => {
    const account = accounts?.find((acc) => acc.companyId === company.id);
    return getCompanyData({ company, isFixed, userInfo, account });
  });

  // Put selected participants on top of the participants table
  sortedData.sort((a, b) => Number(b.isSelected) - Number(a.isSelected));
  return sortedData;
}

function getCompaniesSheetData(
  inTableParticipants: InTableParticipantType[] = [],
  isNotional: boolean,
  isFixed: boolean,
  userInfo: UserType,
  accounts: Array<AccountType>
) {
  const unit = isFixed ? '%' : ' bps';
  return inTableParticipants.map((company) => {
    const account = accounts.find((acc) => acc.id === company.accountId);
    const sheetData: any = {};
    const companyData: CompanyDataType = getCompanyData({ company, isFixed, userInfo, account });
    const columns: TableColumnType<SelectParticipantsTableColumnValueType>[] = getCompaniesColumns({ isFixed });
    sheetData['Cash Pool Participant'] = company.value ? 'Yes' : 'No';
    for (let i = 0; i < columns.length; i++) {
      const column: TableColumnType<SelectParticipantsTableColumnValueType> = columns[i];
      if (column.value.toLowerCase().includes('string')) continue; // skips numbers (and currency and flag) because of comma they won't be valid, they are added after loop
      if (column.value === 'externalIds') continue;
      if (column.value === 'uniqueId') continue;
      sheetData[column.label as string] = companyData[
        column.value as 'name' | 'country' | 'rating' | 'ratingAdj' | 'industry'
      ].replace(unit, '');
    }
    sheetData[isFixed ? 'Credit Rate' : 'Credit Spread'] = companyData.creditInterestRate;
    sheetData[isFixed ? 'Debit Rate' : 'Debit Spread'] = companyData.debitInterestRate;
    sheetData['Interest Added'] = companyData.generateInterestStatementData ? 'Yes' : 'No';
    if (companyData.externalIds)
      sheetData['External ID'] = companyData.externalIds.map((id) => id.externalId).join(', ');
    else sheetData['External ID'] = '';
    sheetData['Unique ID'] = companyData.uniqueId || '';
    if (isNotional) sheetData['Currency'] = companyData.currency;
    return sheetData;
  });
}

export const getSheetData = ({
  inTableParticipants,
  isNotional,
  isFixed,
  userInfo,
  accounts,
}: {
  inTableParticipants: InTableParticipantType[];
  isNotional: boolean;
  isFixed: boolean;
  userInfo: UserType;
  accounts: Array<AccountType>;
}) => {
  if (isNotional) {
    const allShortCurrencies = _.flatten(allCurrencies.short.map(({ options }) => options)).map(({ value }) => value);
    return [
      {
        sheetData: getCompaniesSheetData(inTableParticipants, isNotional, isFixed, userInfo, accounts),
        sheetName: 'Cash Pools',
      },
      {
        sheetData: allShortCurrencies.map((currency) => ({ 'Allowed Currencies': currency })),
        sheetName: 'Currencies',
      },
    ];
  }
  return [
    {
      sheetData: getCompaniesSheetData(inTableParticipants, isNotional, isFixed, userInfo, accounts),
      sheetName: 'Cash Pools',
    },
  ];
};
