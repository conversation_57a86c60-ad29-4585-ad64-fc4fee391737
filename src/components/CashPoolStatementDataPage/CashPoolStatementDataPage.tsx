import { useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';

import { getCashPool } from 'api';
import { resetCashPool, setCashPool } from 'reducers/cashPool.slice';
import { Card, LoadingSpinner, PageLayout } from 'ui';
import { routesEnum } from 'routes';
import { errorHandler } from 'utils/errors';

import StatementTable from './StatementTable';

const CashPoolStatementDataPage = () => {
  const history = useHistory();
  const dispatch = useDispatch();
  const { cashPoolId } = useParams<{ cashPoolId: string }>();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    getCashPool({ cashPoolId })
      .then((cashPool: any) => {
        dispatch(setCashPool(cashPool));
        setIsLoading(false);
      })
      .catch((error: any) => {
        errorHandler(error);
        history.push(routesEnum.CASH_POOLS);
      });

    return () => {
      dispatch(resetCashPool(null));
    };
  }, [cashPoolId, dispatch, history]);

  if (isLoading) return <LoadingSpinner />;

  return (
    <PageLayout title="Statement Data">
      <Card pb={3} pt={6}>
        <StatementTable />
      </Card>
    </PageLayout>
  );
};

export default CashPoolStatementDataPage;
