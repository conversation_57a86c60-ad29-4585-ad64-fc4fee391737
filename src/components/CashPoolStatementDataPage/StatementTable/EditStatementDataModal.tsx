import { useContext, useEffect, useState } from 'react';

import { UserInfoContext } from 'context';
import { format, parse } from 'date-fns';
import { formatInTimeZone } from 'date-fns-tz';
import { DATE_FNS_FORMATS } from 'enums';
import { Button, DateInput, FlexLayout, Modal, Text, TextInput } from 'ui';
import { showErrorToast } from 'ui/components/Toast';
import { formatDateString } from 'utils/dates';
import { TableRowType } from './StatementDataTable.utils';

type EditStatementDataModalPropsType = {
  dataTestId?: string;
  isShowing: false | TableRowType;
  handleOnEdit: ({
    statementId,
    date,
    statementDate,
    comment,
  }: {
    statementId: number;
    date: string;
    statementDate: string;
    comment?: string;
  }) => Promise<void>;
  handleOnHide: () => void;
};

function EditStatementDataModal({
  isShowing,
  dataTestId = '',
  handleOnEdit,
  handleOnHide,
}: EditStatementDataModalPropsType) {
  const { userInfo } = useContext(UserInfoContext);
  const [statementDate, setStatementDate] = useState<string | null>(null);
  const [date, setDate] = useState<string | null>(null);
  const [comment, setComment] = useState<string>('');

  const resetFields = () => {
    setStatementDate(null);
    setDate(null);
    setComment('');
  };

  const onSubmit = async () => {
    if (!isShowing) return;
    if (!date || !statementDate) return showErrorToast('Please fill in date and statement date.');

    await handleOnEdit({
      statementId: isShowing.id,
      date: date,
      statementDate: statementDate,
      comment,
    });
    resetFields();
  };

  const onHideClick = () => {
    resetFields();
    handleOnHide();
  };

  useEffect(() => {
    if (isShowing) {
      // setDate(isShowing.date ?? null);
      // setStatementDate(isShowing.statementDate ?? null);
      setDate(
        format(
          new Date(parse(isShowing.date ?? '', DATE_FNS_FORMATS[userInfo.dateFormat], new Date()) ?? ''),
          'yyyy-MM-dd'
        )
      );
      setStatementDate(
        format(
          new Date(parse(isShowing.statementDate ?? '', DATE_FNS_FORMATS[userInfo.dateFormat], new Date()) ?? ''),
          'yyyy-MM-dd'
        )
      );
      setComment(isShowing.comment ?? '');
    }
  }, [isShowing, userInfo]);

  if (!isShowing) return null;

  return (
    <Modal
      actionButtons={
        <Button
          text="Submit"
          onClick={onSubmit}
          disabled={date == null || statementDate == null}
          dataTestId={dataTestId}
        />
      }
      title="Edit statement data"
      width="s"
      onHide={onHideClick}
    >
      <FlexLayout flexDirection="column" justifyContent="space-between" sx={{ gap: '12px' }}>
        <Text color="deep-sapphire">
          Value date:{' '}
          {formatDateString(
            parse(isShowing.date ?? '', DATE_FNS_FORMATS[userInfo.dateFormat], new Date()) ?? '',
            userInfo.dateFormat
          )}
        </Text>
        <Text color="deep-sapphire">
          Statement date:{' '}
          {formatDateString(
            parse(isShowing.statementDate ?? '', DATE_FNS_FORMATS[userInfo.dateFormat], new Date()) ?? '',
            userInfo.dateFormat
          )}
        </Text>
        <Text color="deep-sapphire">Balance change: {isShowing.balanceChange}</Text>
        <Text color="deep-sapphire">{isShowing.companyName}</Text>
        <FlexLayout space={4}>
          <DateInput
            label="Value Date"
            value={date}
            onChange={(date: Date) =>
              setDate(formatInTimeZone(date, Intl.DateTimeFormat().resolvedOptions().timeZone, 'yyyy-MM-dd'))
            }
          />
          <DateInput
            label="Statement Date"
            value={statementDate}
            onChange={(date: Date) =>
              setStatementDate(formatInTimeZone(date, Intl.DateTimeFormat().resolvedOptions().timeZone, 'yyyy-MM-dd'))
            }
          />
        </FlexLayout>
        <TextInput label="Comment" value={comment} onChange={setComment} width="fullWidth" />
      </FlexLayout>
    </Modal>
  );
}

export default EditStatementDataModal;
