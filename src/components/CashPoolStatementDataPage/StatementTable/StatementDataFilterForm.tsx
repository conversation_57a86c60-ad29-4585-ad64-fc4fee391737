import { useAppDispatch, useAppSelector } from 'hooks';

import { statementDataFiltersSelector, updateDateRange, updateField } from 'reducers/statementDataFilters.slice';
import { cashPoolSelector } from 'reducers/cashPool.slice';
import { DateInput, DateRangeInput, FlexLayout, SingleSelect } from 'ui';

const StatementDataFilterForm = ({ isShowing }: { isShowing: boolean }) => {
  const dispatch = useAppDispatch();
  const { dateRange, statementDate, createdDate, source, isUsed, companyId } =
    useAppSelector(statementDataFiltersSelector);
  const cashPool = useAppSelector(cashPoolSelector);

  const getCompanyOptions = () => {
    if (!cashPool) return [];

    return cashPool.topCurrencyAccounts[0].accounts.map(({ companyId, participant }: any) => ({
      value: companyId,
      label: participant.company.name,
    }));
  };

  if (!isShowing) return null;

  return (
    <FlexLayout sx={{ gap: 4 }} flexWrap="wrap">
      <DateRangeInput
        label="Value Date"
        dateRange={dateRange}
        onChange={(range: { startDate: Date; endDate: Date }) => dispatch(updateDateRange(range))}
      />
      <DateInput
        label="Statement Date"
        value={statementDate}
        onChange={(statementDate: Date) => dispatch(updateField({ statementDate }))}
      />
      <DateInput
        label="Created Date"
        value={createdDate}
        onChange={(createdDate: Date) => dispatch(updateField({ createdDate }))}
      />
      <SingleSelect
        label="Company"
        options={getCompanyOptions()}
        value={companyId}
        onChange={(companyId: number) => dispatch(updateField({ companyId }))}
      />
      <SingleSelect
        label="Source"
        options={[
          { value: 'MANUALLY_ADDED', label: 'Manual' },
          { value: 'Statement', label: 'Statement' },
          { value: 'INTEREST', label: 'Interest' },
          { value: 'TEMPLATE_UPLOAD', label: 'Template Upload' },
        ]}
        value={source}
        onChange={(source: string) => dispatch(updateField({ source }))}
      />
      <SingleSelect
        label="Used"
        options={[
          { value: true, label: 'Yes' },
          { value: false, label: 'No' },
        ]}
        value={isUsed}
        onChange={(isUsed: string) => dispatch(updateField({ isUsed }))}
      />
    </FlexLayout>
  );
};

export default StatementDataFilterForm;
