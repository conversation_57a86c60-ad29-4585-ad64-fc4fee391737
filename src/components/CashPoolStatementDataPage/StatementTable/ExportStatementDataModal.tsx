import { useState } from 'react';

import { Button, DateInput, FlexLayout, Modal } from 'ui';

type ExportStatementDataModalPropsType = {
  dataTestId?: string;
  isExporting: boolean;
  isShowing: boolean;
  handleOnExport: ({ startDate, endDate }: { startDate: Date; endDate: Date }) => Promise<void>;
  handleOnHide: () => void;
};

function ExportStatementDataModal({
  dataTestId = '',
  isExporting,
  isShowing,
  handleOnExport,
  handleOnHide,
}: ExportStatementDataModalPropsType) {
  const [startDate, setStartDate] = useState<string | null>(null);
  const [endDate, setEndDate] = useState<string | null>(null);

  const resetFields = () => {
    setStartDate(null);
    setEndDate(null);
  };

  const onExportClick = async () => {
    await handleOnExport({ startDate: new Date(String(startDate)), endDate: new Date(String(endDate)) });
    resetFields();
  };

  const onHideClick = () => {
    handleOnHide();
    resetFields();
  };

  if (!isShowing) return null;

  return (
    <Modal
      actionButtons={
        <Button
          text="Export"
          onClick={onExportClick}
          disabled={startDate == null || endDate == null || isExporting}
          loading={isExporting}
          dataTestId={dataTestId}
        />
      }
      title="Export statement data"
      width="s"
      onHide={onHideClick}
    >
      <FlexLayout justifyContent="space-between" sx={{ gap: '12px' }}>
        <DateInput label="Start Date" value={startDate} onChange={setStartDate} />
        <DateInput label="End Date" value={endDate} onChange={setEndDate} />
      </FlexLayout>
    </Modal>
  );
}

export default ExportStatementDataModal;
