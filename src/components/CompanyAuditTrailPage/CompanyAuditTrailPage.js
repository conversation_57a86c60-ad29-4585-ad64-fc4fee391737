import React, { useContext, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import { deleteCompanyAuditTrail, getCompanyAuditTrails } from '~/api';
import { DeleteModal } from '~/components/Modals';
import { ThreeDotActionMenu } from '~/components/Shared';
import { UserInfoContext } from '~/context/user';
import { isAdmin, NOTIFICATION_ACTIONS } from '~/enums';
import { setNotification } from '~/reducers/notifications.slice';
import { Button, Card, FlexLayout, LoadingSpinner, PageLayout, Table } from '~/ui';
import { showToast, showErrorToast } from '~/ui/components/Toast';

import { getCompaniesColumns, getCompaniesData } from './CompanyAuditTrailPage.utils';

function CompanyAuditTrailPage() {
  const dispatch = useDispatch();
  const history = useHistory();
  const { companyId } = useParams();
  const [isLoading, setIsLoading] = useState(true);
  const [auditTrails, setAuditTrails] = useState([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState();
  const { userInfo } = useContext(UserInfoContext);

  useEffect(() => {
    getCompanyAuditTrails(companyId).then((res) => {
      setAuditTrails(res);
      setIsLoading(false);
    });
  }, [companyId]);

  function onItemDelete() {
    deleteCompanyAuditTrail({ companyId, auditId: selectedItem.id })
      .then(() => {
        setAuditTrails(auditTrails.filter((auditTrail) => auditTrail.id !== selectedItem.id));
        setShowDeleteModal(false);
        setSelectedItem(null);
        showToast('Audit trail has been successfully deleted.');
      })
      .catch(() => showErrorToast());
  }

  function renderActionColumn(item) {
    const options = [];

    if (isAdmin(userInfo.role)) {
      options.push({
        label: 'Delete',
        onClick: () => {
          setShowDeleteModal(true);
          setSelectedItem(item);
        },
      });
    } else {
      options.push({
        label: 'Notify admin to delete the audit trail',
        onClick: () =>
          dispatch(
            setNotification({
              action: NOTIFICATION_ACTIONS.DELETE_AUDIT_TRAIL,
              title: 'Notify admin to delete the audit trail',
              id: item.id,
            })
          ),
      });
    }

    return <ThreeDotActionMenu options={options} />;
  }

  if (isLoading) return <LoadingSpinner />;

  return (
    <>
      <PageLayout title="Audit Trail">
        <Card pb={3} pt={6} spaces={0}>
          <Table
            actionColumn={renderActionColumn}
            columns={getCompaniesColumns()}
            data={getCompaniesData(auditTrails)}
            isSearchable
            onItemClick={({ id }) => history.push(`/group-information/${companyId}/audit-trail/${id}`)}
          />
        </Card>
        <FlexLayout alignItems="flex-end" flexGrow="1" justifyContent="space-between">
          <Button text="Back" variant="gray" onClick={history.goBack} />
        </FlexLayout>
      </PageLayout>
      {showDeleteModal && (
        <DeleteModal
          item="audit trail"
          handleOnDeleteClick={onItemDelete}
          handleOnHide={() => {
            setShowDeleteModal(false);
            setSelectedItem(null);
          }}
        />
      )}
    </>
  );
}

export default CompanyAuditTrailPage;
