import React from 'react';

import { DateTime } from '~/components/Shared';
import { Text } from '~/ui';
import { getCompanyAssessment, getCompanyRating, getCompanyRatingAdj } from '~/utils/companies';

export function getCompaniesData(data = []) {
  return data.map((company) => {
    const { country, id, industry, name, updatedAt } = company;

    return {
      id,
      country,
      createdAt: <DateTime withTime>{updatedAt}</DateTime>,
      industry,
      name,
      rating: getCompanyRating(company),
      assessmentName: getCompanyAssessment(company),
      ratingAdj: getCompanyRatingAdj(company),
    };
  });
}

export function getCompaniesColumns() {
  return [
    {
      label: 'Company',
      value: 'name',
      renderCustomCell: (item) => {
        return (
          <Text color="deep-sapphire" variant="m-spaced">
            {item.name}
          </Text>
        );
      },
    },
    { label: 'Country', value: 'country' },
    { label: 'Industry', value: 'industry' },
    { label: 'Rating', value: 'rating', width: 120 },
    { label: 'Assessment', value: 'assessmentName' },
    { label: 'Rating (ADJ).', value: 'ratingAdj', width: 120 },
    { label: 'Created', value: 'createdAt', width: 170 },
  ];
}
