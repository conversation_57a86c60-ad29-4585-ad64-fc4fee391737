import { Text, PageLayout, FlexLayout, Card } from 'ui';
import { routesEnum } from 'routes';

const ErrorBoundary = () => {
  return (
    <FlexLayout
      sx={{ position: 'absolute', top: 0, right: 0, width: '100vw', height: '100vh' }}
      onClick={() => (window.location.href = routesEnum.DASHBOARD)}
    >
      <FlexLayout pl={250} flexGrow="1">
        <PageLayout title="Something went wrong">
          <Card p={4}>
            <Text>Error: Please try a hard refresh to resolve the issue.</Text>
            <Text>
              <b>Windows:</b> <br /> Chrome/Edge/Firefox: Press Ctrl + F5 or Ctrl + Shift + R
            </Text>
            <Text>
              <b>Mac:</b> <br /> Chrome/Edge/Firefox: Press Cmd + Shift + R
            </Text>
            <Text color="shakespeare">If the issue persists, please contact our team.</Text>
          </Card>
        </PageLayout>
      </FlexLayout>
    </FlexLayout>
  );
};

export default ErrorBoundary;
