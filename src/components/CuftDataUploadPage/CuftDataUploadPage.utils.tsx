import { saveAs } from 'file-saver';

import { getCuftDataFile, deleteCuftDataFile } from 'api';
import { ThreeDotActionMenu } from 'components/Shared';
import { formatDateString } from 'utils/dates';
import { showToast, showErrorToast } from 'ui/components/Toast';
import type { CuftDataFileType, UserInfoType } from 'types';

export const columns = [
  { label: 'Name', sortBy: 'name', value: 'name', width: 250 },
  { label: 'Uploaded', sortBy: 'createdAt', value: 'createdAt' },
];

const getCuftFileTableRowData = (cuftDataFile: CuftDataFileType, userInfo: UserInfoType) => {
  const { id, name, createdAt } = cuftDataFile;

  return { id, name, createdAt: formatDateString(createdAt, userInfo.dateFormat) };
};

export const getCuftFileTableData = (data: CuftDataFileType[], userInfo: UserInfoType) =>
  data.map((d) => getCuftFileTableRowData(d, userInfo));

export const renderTableActionColumn = (
  cuftDataFile: CuftDataFileType,
  cuftDataFiles: CuftDataFileType[],
  setCuftDataFiles: React.Dispatch<React.SetStateAction<CuftDataFileType[]>>
) => {
  const options = [
    {
      label: 'Download',
      onClick: () => {
        getCuftDataFile(cuftDataFile.id)
          .then((file: File) => {
            saveAs(file, file.name);
            showToast('CUFT Agreements file downloaded successfully.');
          })
          .catch(() => showErrorToast());
      },
    },
    {
      label: 'Delete',
      onClick: () => {
        deleteCuftDataFile(cuftDataFile.id)
          .then(() => {
            setCuftDataFiles(cuftDataFiles.filter((f) => f.id !== cuftDataFile.id));
            showToast('CUFT Agreements successfully deleted.');
          })
          .catch(() => showErrorToast());
      },
    },
  ];

  return <ThreeDotActionMenu options={options} />;
};
