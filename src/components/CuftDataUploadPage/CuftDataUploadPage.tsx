import { useContext, useEffect, useState } from 'react';

import { getCuftDataFiles, uploadCuftDataFile } from 'api';
import { UserInfoContext } from 'context/user';
import { Button, Card, FileInput, FlexLayout, PageLayout, Table } from 'ui';
import type { CuftDataFileType } from 'types';
import { showErrorToast, showToast } from 'ui/components/Toast';

import { columns, getCuftFileTableData, renderTableActionColumn } from './CuftDataUploadPage.utils';
import { errorHandler } from 'utils/errors';

const CuftDataUploadPage = () => {
  const { userInfo } = useContext(UserInfoContext);
  const [cuftDataFiles, setCuftDatFiles] = useState<CuftDataFileType[]>([]);
  const [isUploading, setIsUploading] = useState<boolean>(false);

  const onImportFileClick = async (file: File) => {
    if (!file) return showErrorToast('File uploaded failed. Please try again.');

    setIsUploading(true);
    uploadCuftDataFile(file)
      .then(() => {
        getCuftDataFiles().then(setCuftDatFiles);
        showToast('CUFT Agreements successfully uploaded.');
      })
      .catch(errorHandler)
      .finally(() => setIsUploading(false));
  };

  useEffect(() => {
    getCuftDataFiles().then(setCuftDatFiles);
  }, []);

  return (
    <PageLayout
      title="CUFT Agreements Upload"
      rightTitleContent={
        <FileInput onChange={onImportFileClick} accept=".xlsx">
          <Button
            iconLeft="upload"
            text="Upload CUFT Agreements"
            size="s"
            variant="secondary"
            onClick={() => {}}
            loading={isUploading}
          />
        </FileInput>
      }
    >
      <Card pb={3} pt={6}>
        <FlexLayout flexDirection="column">
          <Table
            customPageSize={50}
            columns={columns}
            data={getCuftFileTableData(cuftDataFiles, userInfo)}
            actionColumn={(item: CuftDataFileType) => renderTableActionColumn(item, cuftDataFiles, setCuftDatFiles)}
          />
        </FlexLayout>
      </Card>
    </PageLayout>
  );
};

export default CuftDataUploadPage;
