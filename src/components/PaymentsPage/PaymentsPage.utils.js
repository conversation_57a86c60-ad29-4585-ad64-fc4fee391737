import { saveAs } from 'file-saver';
import _ from 'lodash';
import qs from 'query-string';

import { exportCashPoolPaymentsAsExcel, exportGuaranteePaymentsAsExcel, exportLoanPaymentsAsExcel } from '~/api';
import { reportEnum } from '~/enums';
import { showToast } from '~/ui/components/Toast';
import { errorHandler } from '~/utils/errors';

const getExportFunction = (reportType) => {
  if (reportType === reportEnum.LOAN) return exportLoanPaymentsAsExcel;
  if (reportType === reportEnum.GUARANTEE) return exportGuaranteePaymentsAsExcel;
  if (reportType === reportEnum.CASH_POOL) return exportCashPoolPaymentsAsExcel;
};

export const handleOnExportTableClick = async ({
  setIsExportButtonDisabled,
  history,
  getQueryParams,
  reportType,
  getVisibleColumns,
}) => {
  try {
    setIsExportButtonDisabled(true);
    const searchQuery = {
      ...qs.parse(history.location.search),
      ...getQueryParams(),
      columns: getVisibleColumns().join(', '),
    };
    const exportPayments = getExportFunction(reportType);
    const file = await exportPayments({ searchQuery });
    saveAs(file, file.name);
    showToast('Sheet successfully exported.');
    setIsExportButtonDisabled(false);
  } catch (err) {
    errorHandler(err);
  }
};

export const getFiltersText = (formData, filterKeys) => {
  const activeFilters = Object.values(_.pick(formData, filterKeys)).reduce((previousValue) => previousValue + 1, 0);

  if (activeFilters) {
    return `Filters (${activeFilters})`;
  }
  return 'Filters';
};

export const setNumberOfPaymentsForPagination = (reportType, setNumberOfPayments, paymentsWithTotalCount) => {
  const {
    loanPaymentsWithCount,
    guaranteePaymentsWithCount,
    cashPoolPaymentsWithCount,
    withholdingTaxPaymentsWithCount,
  } = paymentsWithTotalCount;

  if (reportType === reportEnum.LOAN) return setNumberOfPayments(loanPaymentsWithCount.totalNumberOfPayments);
  if (reportType === reportEnum.GUARANTEE) return setNumberOfPayments(guaranteePaymentsWithCount.totalNumberOfPayments);
  if (reportType === reportEnum.CASH_POOL) return setNumberOfPayments(cashPoolPaymentsWithCount.totalNumberOfPayments);
  if (reportType === reportEnum.WITHHOLDING_TAX)
    return setNumberOfPayments(withholdingTaxPaymentsWithCount.totalNumberOfPayments);
};
