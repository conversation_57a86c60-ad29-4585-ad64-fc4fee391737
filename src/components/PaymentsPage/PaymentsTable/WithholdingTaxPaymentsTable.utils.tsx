import type { AppDispatch } from 'store';

import { errorHand<PERSON> } from 'utils/errors';
import { getLoanWHTPayments, markWHTLoanPaymentAsPaid } from 'api';
import { ThreeDotActionMenu } from 'components/Shared';
import { updateField } from 'reducers/payment.slice';
import { UserInfoType, WHTPayment } from 'types';
import { Text } from 'ui';
import { showToast } from 'ui/components/Toast';
import { formatDateString } from 'utils/dates';
import { getReportUnit } from 'utils/report';
import { capitalize, displayNumber2 } from 'utils/strings';
import { getVisibleColumns } from 'utils/tables';

type RenderTableActionColumnPropsType = {
  item: any;
  setPayments: React.Dispatch<React.SetStateAction<{ withholdingTaxPaymentsWithCount?: WHTPayment[] }>>;
  searchQuery: any;
  dispatch: AppDispatch;
};

function getWithholdingTaxPaymentData(payment: WHTPayment, userInfo: UserInfoType) {
  const { isPaid, loan, paymentNumber, paymentAmount, totalNumberOfPayments } = payment;

  const isBullet = loan.type === 'Bullet';
  const isFloat = loan.rateType.type === 'float';

  const {
    amount,
    currency,
    editable,
    lender,
    borrower,
    id,
    report,
    paymentFrequency,
    issueDate,
    maturityDate,
    rateType,
  } = loan;
  const numberDisplayOptions = { decimalPoint: userInfo.decimalPoint, defaultValue: '-', minDig: 2, maxDig: 2 };

  return {
    id,
    paymentId: payment.id,
    isBullet,
    isFloat,
    editable,
    payer: lender.name,
    recipient: borrower.name,
    currency,
    paymentNumberNum: paymentNumber,
    paymentAmount: displayNumber2(paymentAmount, numberDisplayOptions),
    isPaid: isPaid ? 'Published' : 'Unpublished',
    loanType: isBullet ? 'Bullet' : 'Balloon',
    paymentNumber: `${paymentNumber} of ${totalNumberOfPayments}`,
    finalInterestRate: `${report.finalInterestRate} ${getReportUnit(rateType, true)}`,
    principalAmount: displayNumber2(amount, numberDisplayOptions),
    paymentFrequency,
    issueDate: formatDateString(issueDate, userInfo.dateFormat),
    issueDateNotFormatted: issueDate,
    maturityDate: formatDateString(maturityDate, userInfo.dateFormat),
    maturityDateNotFormatted: maturityDate,
    referenceRate: rateType.type === 'float' ? rateType.referenceRate : '-',
    rateType: capitalize(rateType.type),
    referenceRateMaturity: rateType.type === 'float' ? rateType.referenceRate : '-',
  };
}

export function getWithholdingTaxPaymentsData(data = [], userInfo: UserInfoType) {
  return data.map((item) => getWithholdingTaxPaymentData(item, userInfo));
}

const columns = [
  { label: 'Payer', sortBy: 'payer', value: 'payer', wrapText: true },
  { label: 'Recipient', sortBy: 'recipient', value: 'recipient', wrapText: true },
  { label: 'Loan type', sortBy: 'loanType', value: 'loanType' },
  { label: 'Rate type', sortBy: 'rateType', value: 'rateType' },
  { label: 'Currency', sortBy: 'currency', value: 'currency' },
  { label: 'Interes amount', sortBy: 'paymentAmount', value: 'paymentAmount', justifyContent: 'flex-end' },
  {
    label: 'Status',
    sortBy: 'isPaid',
    value: 'isPaid',
    renderCustomCell: ({ isPaid }: { isPaid: string }) => {
      return (
        <Text color={isPaid === 'Published' ? 'deep-sapphire' : 'blaze-orange'} variant="m-spaced">
          {isPaid === 'Published' ? 'Published' : 'Unpublished'}
        </Text>
      );
    },
  },
  { label: 'Interest number', sortBy: 'paymentNumber', value: 'paymentNumber' },
  { label: 'Issue date', sortBy: 'issueDate', value: 'issueDate' },
  { label: 'Maturity date', sortBy: 'maturityDate', value: 'maturityDate' },
  { label: 'Rate', sortBy: 'finalInterestRate', value: 'finalInterestRate', justifyContent: 'flex-end' },
  { label: 'Principal amount', sortBy: 'principalAmount', value: 'principalAmount', justifyContent: 'flex-end' },
];

export function getWithholdingTaxPaymentsColumns(visibleColumns: any) {
  return getVisibleColumns({ columns, visibleColumns });
}

export const renderTableActionColumn = ({
  item,
  setPayments,
  searchQuery,
  dispatch,
}: RenderTableActionColumnPropsType) => {
  const options = [];

  options.push({
    label: 'Show all interest',
    onClick: () => dispatch(updateField({ loanId: item.id })),
  });

  if (item.paymentAmount) {
    options.push({
      label: item.isPaid === 'Published' ? 'Unpublish' : 'Publish',
      onClick: async () => {
        await markWHTLoanPaymentAsPaid(item.id, item.paymentId, { isPaid: !(item.isPaid === 'Published') });

        getLoanWHTPayments({ searchQuery })
          .then((withholdingTaxPaymentsWithCount: WHTPayment[]) => setPayments({ withholdingTaxPaymentsWithCount }))
          .then(() => showToast(`Payment ${item.isPaid === 'Published' ? 'unpublished' : 'published'}`))
          .catch(errorHandler);
      },
    });
  }

  return <ThreeDotActionMenu options={options} />;
};

export const whtTooltips = {
  firstCompany: 'Select the company that pays withholding tax.', // payer
  secondCompany: 'Select the company that receives withholding tax.', // recipient
  currency: 'Select the currency in which the loan is denominated.',
};
