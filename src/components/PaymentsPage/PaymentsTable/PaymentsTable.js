import { useContext, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { UserInfoContext } from '~/context/user';
import { reportEnum } from '~/enums';
import { cashPoolPaymentColumns } from '~/reducers/tableColumns/cashPoolPaymentColumns.slice';
import { guaranteePaymentColumns } from '~/reducers/tableColumns/guaranteePaymentColumns.slice';
import { loanPaymentColumns } from '~/reducers/tableColumns/loanPaymentColumns.slice';
import { withholdingTaxPaymentColumns } from '~/reducers/tableColumns/withholdingTaxPaymentsColumns.slice';
import { paymentSelector, updateField } from '~/reducers/payment.slice';
import { routesEnum } from '~/routes';
import { FlexLayout, LoadingSpinner, Table } from '~/ui';

import AddInterestToBalanceModal from './AddInterestToBalanceModal';

import {
  getCashPoolPaymentsColumns,
  getCashPoolPaymentsData,
  renderTableActionColumn as renderCashPoolPaymentsTableActionColumn,
} from './CashPoolPaymentsTable.utils';
import {
  getGuaranteePaymentsColumns,
  getGuaranteePaymentsData,
  renderTableActionColumn as renderGuaranteePaymentsTableActionColumn,
} from './GuaranteePaymentsTable.utils';
import {
  getLoanPaymentsColumns,
  getLoanPaymentsData,
  renderTableActionColumn as renderLoanPaymentsTableActionColumn,
} from './LoanPaymentsTable.utils';
import {
  getWithholdingTaxPaymentsColumns,
  getWithholdingTaxPaymentsData,
  renderTableActionColumn as renderWithholdingTaxPaymentsTableActionColumn,
} from './WithholdingTaxPaymentsTable.utils';

const PaymentsTable = ({
  payments,
  setPayments,
  searchQuery,
  isLoading,
  reportType,
  setIsRepayPrincipalModalShowing,
  setIsReferenceModalShowing,
}) => {
  const history = useHistory();
  const dispatch = useDispatch();
  const [isAddInterestToBalanceModalShowing, setIsAddInterestToBalanceModalShowing] = useState(false); // false or set to table row values of payment
  const payment = useSelector(paymentSelector);
  const visibleLoanColumns = useSelector(loanPaymentColumns);
  const visibleGuaranteeColumns = useSelector(guaranteePaymentColumns);
  const visibleCashPoolColumns = useSelector(cashPoolPaymentColumns);
  const visibleWithholdingTaxColumns = useSelector(withholdingTaxPaymentColumns);
  const { userInfo } = useContext(UserInfoContext);

  const getTableSetupData = () => {
    if (reportType === reportEnum.LOAN)
      return [
        visibleLoanColumns,
        getLoanPaymentsColumns,
        renderLoanPaymentsTableActionColumn,
        getLoanPaymentsData,
        payments?.loanPaymentsWithCount?.payments,
      ];
    if (reportType === reportEnum.GUARANTEE)
      return [
        visibleGuaranteeColumns,
        getGuaranteePaymentsColumns,
        renderGuaranteePaymentsTableActionColumn,
        getGuaranteePaymentsData,
        payments?.guaranteePaymentsWithCount?.payments,
      ];
    if (reportType === reportEnum.CASH_POOL)
      return [
        visibleCashPoolColumns,
        getCashPoolPaymentsColumns,
        renderCashPoolPaymentsTableActionColumn,
        getCashPoolPaymentsData,
        payments?.cashPoolPaymentsWithCount?.payments,
      ];
    if (reportType === reportEnum.WITHHOLDING_TAX)
      return [
        visibleWithholdingTaxColumns,
        getWithholdingTaxPaymentsColumns,
        renderWithholdingTaxPaymentsTableActionColumn,
        getWithholdingTaxPaymentsData,
        payments?.withholdingTaxPaymentsWithCount?.payments,
      ];

    return [];
  };

  const [visibleColumns, getColumns, renderTableActionColumn, getData, data] = getTableSetupData();

  const onItemClick = ({ id, cashPoolId }) => {
    if (reportType === reportEnum.CASH_POOL) {
      return history.push(`${routesEnum.CASH_POOLS}/${cashPoolId}`);
    }
    if (reportType === reportEnum.WITHHOLDING_TAX) {
      return history.push(`${routesEnum.PORTFOLIO}/${id}?reportType=loan`);
    }
    history.push(`${routesEnum.PORTFOLIO}/${id}?reportType=${reportType}`);
  };

  if (isLoading) return <LoadingSpinner />;

  return (
    <FlexLayout flexDirection="column">
      <Table
        actionColumn={(item) =>
          renderTableActionColumn({
            item,
            setPayments,
            searchQuery,
            dispatch,
            setIsRepayPrincipalModalShowing,
            setIsReferenceModalShowing,
            setIsAddInterestToBalanceModalShowing,
            history,
          })
        }
        columns={getColumns(visibleColumns)}
        data={getData(data, userInfo)}
        onItemClick={onItemClick}
        serverSideSorting={true}
        updateSorting={(columnName) => dispatch(updateField({ sort: columnName }))}
        sortedBy={payment?.sort}
      />
      <AddInterestToBalanceModal
        item={isAddInterestToBalanceModalShowing}
        onHide={() => setIsAddInterestToBalanceModalShowing(false)}
        searchQuery={searchQuery}
        setPayments={setPayments}
      />
    </FlexLayout>
  );
};

export default PaymentsTable;
