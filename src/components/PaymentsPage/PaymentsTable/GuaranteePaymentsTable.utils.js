import React from 'react';

import { getGuaranteePayments, markGuaranteePaymentAsPaid } from '~/api';
import { ThreeDotActionMenu } from '~/components/Shared';
import { updateField } from '~/reducers/payment.slice';
import { Text } from '~/ui';
import { showToast } from '~/ui/components/Toast';
import { formatDateString } from '~/utils/dates';
import { displayNumber2 } from '~/utils/strings';
import { getVisibleColumns } from '~/utils/tables';

function getGuaranteeData(payment, userInfo) {
  const { paymentDueDate, isPaid, guarantee, bulletPayment, paymentNumber, paymentAmount, totalNumberOfPayments } =
    payment;

  const { amount, currency, editable, guarantor, id, principal, report, paymentFrequency, issueDate, terminationDate } =
    guarantee;
  const numberDisplayOptions = { decimalPoint: userInfo.decimalPoint, minDig: 2, maxDig: 2 };

  return {
    id,
    paymentId: payment.id,
    editable,
    guarantor: guarantor?.name,
    principal: principal?.name,
    currency,
    paymentAmount: displayNumber2(paymentAmount, numberDisplayOptions),
    interestPayment: displayNumber2(bulletPayment.interestPayment, numberDisplayOptions),
    paymentDueDate: formatDateString(paymentDueDate, userInfo.dateFormat),
    isPaid: isPaid ? 'Published' : 'Unpublished',
    paymentNumber: `${paymentNumber} of ${totalNumberOfPayments}`,
    finalInterestRate: report?.finalInterestRate + '%',
    principalAmount: displayNumber2(amount, numberDisplayOptions),
    paymentFrequency,
    issueDate: formatDateString(issueDate, userInfo.dateFormat),
    terminationDate: formatDateString(terminationDate, userInfo.dateFormat),
  };
}

export function getGuaranteePaymentsData(data = [], userInfo) {
  return data.map((item) => getGuaranteeData(item, userInfo));
}

const columns = [
  { label: 'Guarantor', sortBy: 'guarantor', value: 'guarantor', wrapText: true },
  { label: 'Principal', sortBy: 'principal', value: 'principal', wrapText: true },
  { label: 'Currency', sortBy: 'currency', value: 'currency' },
  { label: 'Interest amount', sortBy: 'paymentAmount', value: 'paymentAmount', justifyContent: 'flex-end' },
  { label: 'Interest due', sortBy: 'paymentDueDate', value: 'paymentDueDate' },
  {
    label: 'Status',
    sortBy: 'isPaid',
    value: 'isPaid',
    // important isPaid is a string for export
    renderCustomCell: ({ isPaid }) => (
      <Text color={isPaid === 'Published' ? 'deep-sapphire' : 'blaze-orange'} variant="m-spaced">
        {isPaid === 'Published' ? 'Published' : 'Unpublished'}
      </Text>
    ),
  },
  { label: 'Interest number', sortBy: 'paymentNumber', value: 'paymentNumber', justifyContent: 'flex-end' },
  { label: 'Fee interest frequency', sortBy: 'paymentFrequency', value: 'paymentFrequency' },
  { label: 'Issue date', sortBy: 'issueDate', value: 'issueDate' },
  { label: 'Termination date', sortBy: 'terminationDate', value: 'terminationDate' },
  { label: 'Rate', sortBy: 'finalInterestRate', value: 'finalInterestRate', justifyContent: 'flex-end' },
  { label: 'Principal amount', sortBy: 'principalAmount', value: 'principalAmount', justifyContent: 'flex-end' },
  { label: 'Guarantee Fee', sortBy: 'interestPayment', value: 'interestPayment', justifyContent: 'flex-end' },
];

export function getGuaranteePaymentsColumns(visibleColumns) {
  return getVisibleColumns({ columns, visibleColumns });
}

export const renderTableActionColumn = ({ item, setPayments, searchQuery, dispatch }) => {
  const options = [];

  options.push({
    label: 'Show all interest',
    onClick: () => dispatch(updateField({ guaranteeId: item.id })),
  });

  options.push({
    label: item.isPaid === 'Published' ? 'Unpublish' : 'Publish',
    onClick: async () => {
      await markGuaranteePaymentAsPaid(item.id, item.paymentId, { isPaid: !(item.isPaid === 'Published') });

      getGuaranteePayments({ searchQuery })
        .then((guaranteePaymentsWithCount) => setPayments({ guaranteePaymentsWithCount }))
        .then(() => showToast(`Interest ${item.isPaid === 'Published' ? 'unpublished' : 'published'}`));
    },
  });

  return <ThreeDotActionMenu options={options} />;
};

export const guaranteeTooltips = {
  firstCompany: 'Select the company that provides the guarantee.', // guarantor
  secondCompany: 'Select the company that receives the guarantee.', // principal
  currency: 'Select the currency of the liability that the guarantee covers.',
};
