import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
  CashPoolSingleSelect,
  CompanySingleSelect,
  CurrencySingleSelect,
  PaymentStatusSingleSelect,
} from '~/components/Shared';
import { reportEnum } from '~/enums';
import { paymentSelector, updateField, updatePaymentRateType } from '~/reducers/payment.slice';
import { Box, DateInput, FlexLayout, NumberInput, RadioGroup } from '~/ui';
import { capitalize } from '~/utils/strings';

import { cashPoolTooltips, guaranteeTooltips, loanTooltips, whtTooltips } from './PaymentsTable';

const getTooltips = (reportType) => {
  if (reportType === reportEnum.LOAN) return loanTooltips;
  if (reportType === reportEnum.GUARANTEE) return guaranteeTooltips;
  if (reportType === reportEnum.CASH_POOL) return cashPoolTooltips;
  if (reportType === reportEnum.WITHHOLDING_TAX) return whtTooltips;
};

const fieldMapper = (reportType, name) => {
  const firstCompany = {
    [reportEnum.LOAN]: 'lender',
    [reportEnum.GUARANTEE]: 'guarantor',
    [reportEnum.CASH_POOL]: 'creditor',
    [reportEnum.WITHHOLDING_TAX]: 'lender',
  };
  const secondCompany = {
    [reportEnum.LOAN]: 'borrower',
    [reportEnum.GUARANTEE]: 'principal',
    [reportEnum.CASH_POOL]: 'debtor',
    [reportEnum.WITHHOLDING_TAX]: 'borrower',
  };

  if (name === 'firstCompany') return firstCompany[reportType];
  if (name === 'secondCompany') return secondCompany[reportType];
};

const FilterForm = ({ isShowing, reportType }) => {
  const dispatch = useDispatch();
  const {
    lender,
    borrower,
    guarantor,
    principal,
    creditor,
    debtor,
    cashPoolId = null,
    currency,
    isPaid,
    startDate,
    endDate,
    rateType,
    paymentAmount,
  } = useSelector(paymentSelector);
  const [paymentAmountState, setPaymentAmountState] = useState(paymentAmount);
  const [timeoutId, setTimeoutId] = useState();
  const tooltips = getTooltips(reportType);

  // prevents requests to the server on every change
  const onPaymentAmountChange = (paymentAmount) => {
    clearTimeout(timeoutId);
    setPaymentAmountState(paymentAmount);

    setTimeoutId(
      setTimeout(() => {
        dispatch(updateField({ paymentAmount }));
      }, 500)
    );
  };

  useEffect(() => {
    setPaymentAmountState(paymentAmount);
  }, [paymentAmount]);

  if (!isShowing) return null;

  return (
    <FlexLayout flexDirection="column" space={8}>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
        <CompanySingleSelect
          label={capitalize(fieldMapper(reportType, 'firstCompany'))}
          excludedValues={[borrower?.id, principal?.id, debtor?.id]}
          tooltip={tooltips.firstCompany}
          value={Number(lender?.id || guarantor?.id || creditor?.id)}
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ [fieldMapper(reportType, 'firstCompany')]: value }))}
        />
        <CompanySingleSelect
          label={capitalize(fieldMapper(reportType, 'secondCompany'))}
          excludedValues={[lender?.id, guarantor?.id, creditor?.id]}
          tooltip={tooltips.secondCompany}
          value={Number(borrower?.id || principal?.id || debtor?.id)}
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ [fieldMapper(reportType, 'secondCompany')]: value }))}
        />
      </Box>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(4, 1fr)' }}>
        {reportType !== reportEnum.WITHHOLDING_TAX && (
          <>
            <DateInput
              label="Start Date"
              tooltip="Select the start date of interest."
              value={startDate}
              width="fullWidth"
              onChange={(startDate) => dispatch(updateField({ startDate }))}
            />
            <DateInput
              label="End Date"
              tooltip="Select the end date of interest."
              value={endDate}
              width="fullWidth"
              onChange={(endDate) => dispatch(updateField({ endDate }))}
            />
          </>
        )}
        <CurrencySingleSelect
          tooltip={tooltips.currency}
          value={currency}
          width="fullWidth"
          onChange={(currency) => dispatch(updateField({ currency }))}
        />
        <PaymentStatusSingleSelect
          value={isPaid}
          width="fullWidth"
          onChange={(isPaid) => dispatch(updateField({ isPaid }))}
          tooltip="Select interest status."
        />
      </Box>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(4, 1fr)' }}>
        {reportType === reportEnum.CASH_POOL && (
          <CashPoolSingleSelect
            value={cashPoolId}
            tooltip="Select the cash pool."
            onChange={(cashPoolId) => dispatch(updateField({ cashPoolId }))}
          />
        )}
        {reportType !== reportEnum.CASH_POOL && (
          <NumberInput
            allowNegatives={false}
            label="Interest amount"
            inputType="float"
            value={paymentAmountState}
            width="fullWidth"
            onChange={onPaymentAmountChange}
          />
        )}
        {(reportType === reportEnum.LOAN || reportType === reportEnum.WITHHOLDING_TAX) && (
          <RadioGroup
            options={[
              { label: 'Fixed', value: 'fixed' },
              { label: 'Float', value: 'float' },
            ]}
            label="Interest Type"
            tooltip="Select the interest type of the loan."
            value={rateType}
            onChange={(rateType) => dispatch(updatePaymentRateType(rateType))}
            variant="row"
            checkboxVariant="outlined"
          />
        )}
      </Box>
    </FlexLayout>
  );
};

export default FilterForm;
