import { useDispatch } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { CompanySingleSelect } from 'components/Shared';
import {
  report,
  updateField,
  updateB2BLenderField,
  updateB2BBorrower<PERSON>ield,
  addB2BRow,
  removeB2BRow,
  clearB2BAllRows,
} from 'reducers/report.slice';
import { useAppSelector } from 'hooks';
import { Box, Button, FlexLayout, Icon, Checkbox, WithTooltip, Switch } from 'ui';
import { EmbeddedCompanyType } from 'types';

import { loanTooltips } from '../PriceAndBenchmarkPage.utils';

const B2BLegsSection = () => {
  const dispatch = useDispatch();
  const history = useHistory();
  const { lenders, borrowers, riskTakerId, isRenumerationSectionShown } = useAppSelector(report);
  const isEdit = history.location.pathname.includes('/edit');
  const excludedValues = [...lenders, ...borrowers].map((company) => company.id).filter(Boolean);
  const areLegsSet =
    lenders.length >= 2 &&
    borrowers.length >= 2 &&
    lenders.every((l: EmbeddedCompanyType) => l?.id) &&
    borrowers.every((b: EmbeddedCompanyType) => b?.id);

  return (
    <>
      {lenders.map((lender: EmbeddedCompanyType, index: number) => (
        <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }} key={index}>
          <CompanySingleSelect
            checkCreditRating
            label={index === 0 ? 'Primary Lender' : 'Lender'}
            dataTestId="lenderCompanySelect"
            tooltip={loanTooltips.lender}
            disabled={isEdit || index > 0}
            width="fullWidth"
            excludedValues={excludedValues.filter((id) => lender?.id !== id)}
            value={lender?.id}
            onChange={(lender: EmbeddedCompanyType) => dispatch(updateB2BLenderField({ lender, index }))}
          />
          <FlexLayout alignItems="center" sx={{ width: '100%' }}>
            <CompanySingleSelect
              checkCreditRating
              label={index === lenders.length - 1 ? 'Ultimate Borrower' : 'Borrower'}
              rightSideLabelComponent={
                <WithTooltip
                  disabled={borrowers[index]?.id == null}
                  label={`RiskTakerLabel-${index}`}
                  tooltip="Select a borrower first"
                >
                  <Checkbox
                    label="Risk taker"
                    labelColor="bali-hai"
                    labelSide="left"
                    disabled={borrowers[index]?.id == null || isEdit}
                    isActive={riskTakerId === borrowers[index]?.id}
                    isShowing={index < lenders.length - 1}
                    onChange={() => dispatch(updateField({ riskTakerId: borrowers[index]?.id }))}
                  />
                </WithTooltip>
              }
              dataTestId="borrowerCompanySelect"
              tooltip={loanTooltips.borrower}
              disabled={isEdit}
              width="fullWidth"
              sx={{ width: '93%' }}
              excludedValues={excludedValues.filter((id) => borrowers[index]?.id !== id)}
              value={borrowers[index]?.id}
              onChange={(borrower: EmbeddedCompanyType) => dispatch(updateB2BBorrowerField({ borrower, index }))}
            />
            <Icon
              icon="x"
              size="xs"
              isShowing={index > 1 && index === lenders.length - 1 && !isEdit}
              sx={{ margin: '27px 0 0 18px' }}
              onClick={() => dispatch(removeB2BRow({ index }))}
            />
          </FlexLayout>
        </Box>
      ))}

      {areLegsSet && (
        <FlexLayout bg="alabaster" p={4} sx={{ border: 'border-dashed-link-water', marginRight: '40px' }}>
          <Switch
            switchBg="white"
            labelColor="bali-hai"
            label="Include standard remuneration for pass-through entities"
            isActive={isRenumerationSectionShown}
            onChange={() => dispatch(updateField({ isRenumerationSectionShown: !isRenumerationSectionShown }))}
          />
        </FlexLayout>
      )}

      {!isEdit && (
        <FlexLayout space={4} pb={8} sx={{ borderBottom: 'border-dashed' }}>
          <Button text="Add another leg" variant="secondary" onClick={() => dispatch(addB2BRow())} />
          <Button text="Clear all" variant="gray" onClick={() => dispatch(clearB2BAllRows())} />
        </FlexLayout>
      )}
    </>
  );
};

export default B2BLegsSection;
