import { useCallback, useContext, useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';

import { getBeta, getEquityRiskPremium } from 'api';
import { useAppDispatch, useAppSelector } from 'hooks';
import { UserInfoContext } from 'context';
import { b2bLoansEnums } from 'enums';
import { Box, FlexLayout, Icon, InputGroup, WithTooltip } from 'ui';
import { TextWithTooltip } from 'components/Shared';
import {
  setInitialCapmData,
  setInitialExpectedLossData,
  updateMainField,
  resetToRecommendation,
} from 'reducers/report.slice';
import { errorHandler } from 'utils/errors';
import { reportUtils, dateUtils } from 'utils';
import { Company } from 'types/database/Company.type';

import { CapmModel, ExpectedLossModal } from './Modals';

const B2BRates = () => {
  const history = useHistory();
  const dispatch = useAppDispatch();
  const {
    lenders,
    borrowers,
    capm,
    expectedLoss,
    issueDate,
    maturityDate,
    currency,
    amount,
    rateType,
    paymentFrequency,
    seniority,
    type,
    pricingApproach,
  } = useAppSelector((state: any) => state.report);
  const [isCapmModalOpen, setIsCapmModalOpen] = useState(false);
  const [isExpectedLossModalOpen, setIsExpectedLossModalOpen] = useState(false);
  const { userInfo } = useContext(UserInfoContext);
  const isEdit = history.location.pathname.includes('/edit');
  const shouldLoanDetailInputsShow =
    lenders.length >= 2 &&
    borrowers.length >= 2 &&
    lenders.every((l: Company) => l?.id) &&
    borrowers.every((b: Company) => b?.id);
  const isShowing =
    [issueDate, maturityDate, currency, amount, rateType?.type, paymentFrequency, seniority, type].every(
      (field) => field != null
    ) && shouldLoanDetailInputsShow;
  const ultimateBorrower = borrowers[borrowers.length - 1];
  const damodaranSource = 'Damodaran';
  const industry = userInfo.client.industry;

  const setupCapmRates = useCallback(async () => {
    try {
      if (!isShowing) return;

      const initialBetaType = b2bLoansEnums.betaTypes.beta;
      const initialBetaRegion = b2bLoansEnums.betaRegions.global;
      const ultimateBorrowerCountry = ultimateBorrower.country;
      const riskFreeRate = undefined; // TODO fetch from external API and uncomment requiredRateOfReturn calculation

      const { equityRiskPremium } = await getEquityRiskPremium({ countryName: ultimateBorrowerCountry, issueDate });
      const { beta } = await getBeta({ industry, region: initialBetaRegion, type: initialBetaType, issueDate });
      const requiredRateOfReturn = undefined; // reportUtils.calculateRequiredRateOfReturn({ riskFreeRate, beta, equityRiskPremium });

      dispatch(
        setInitialCapmData({
          requiredRateOfReturn,
          riskFreeRate,
          riskFreeRateSource: damodaranSource,
          beta,
          betaSource: damodaranSource,
          betaIndustrySector: industry,
          betaRegion: initialBetaRegion,
          betaType: initialBetaType,
          equityRiskPremium,
          equityRiskPremiumSource: damodaranSource,
          equityRiskPremiumCountry: ultimateBorrowerCountry ?? null,
        })
      );
    } catch (err) {
      errorHandler(err);
    }
  }, [isShowing, ultimateBorrower.country, issueDate, industry, dispatch]);

  const setupExpectedLossRates = useCallback(async () => {
    try {
      if (!isShowing) return;

      const lossGivenDefault = reportUtils.getLossGivenDefault(seniority, industry);
      const probabilityOfDefault = reportUtils.getCumulativeProbabilityOfDefault(
        ultimateBorrower,
        dateUtils.createTenor(issueDate, maturityDate),
        pricingApproach
      );
      const expectedLossValue = reportUtils.calculateExpectedLoss({ probabilityOfDefault, lossGivenDefault });

      dispatch(
        setInitialExpectedLossData({
          expectedLoss: expectedLossValue,
          probabilityOfDefault,
          probabilityOfDefaultSource: 'modeFinance',
          probabilityOfDefaultType: 'Cumulative',
          lossGivenDefault,
          lossGivenDefaultSource: 'Basel III',
        })
      );
    } catch (err) {
      errorHandler(err);
    }
  }, [isShowing, issueDate, maturityDate, pricingApproach, seniority, ultimateBorrower, industry, dispatch]);

  useEffect(() => {
    if (isEdit) return;
    setupCapmRates();
    setupExpectedLossRates();
  }, [isEdit, setupCapmRates, setupExpectedLossRates]);

  if (!isShowing) return null;

  return (
    <>
      <FlexLayout sx={{ gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
        <Box
          bg="alabaster"
          px={6}
          py={6}
          sx={{ display: 'grid', gridGap: 8, border: 'border-dashed-link-water', minWidth: 477 }}
        >
          <FlexLayout flexDirection="column">
            <FlexLayout justifyContent="space-between" pb={6}>
              <TextWithTooltip
                tooltip="Required Rate of Return = Risk Free Rate + (Beta x Equity Risk Premium)"
                variant="xl-spaced-bold"
                color="deep-sapphire"
                label="Capital Asset Pricing Model"
                id="Capital Asset Pricing Model"
              />
              <FlexLayout sx={{ gap: 2 }}>
                <WithTooltip disabled label="capmResetTooltip" tooltip="Revert to TPAccurate recommendation">
                  <Box bg="white" px={2} py={1} sx={{ border: 'border', borderRadius: 'm' }}>
                    <Icon
                      icon="reset"
                      color="bali-hai"
                      onClick={() => dispatch(resetToRecommendation({ isCapmReset: true }))}
                    />
                  </Box>
                </WithTooltip>
                <Box bg="white" px={2} py={1} sx={{ border: 'border', borderRadius: 'm' }}>
                  <Icon icon="edit" color="bali-hai" onClick={() => setIsCapmModalOpen(true)} />
                </Box>
              </FlexLayout>
            </FlexLayout>
            <Box sx={{ marginLeft: '17px' }}>
              <InputGroup
                isLinked={true}
                showTopInputOnly={false}
                inputs={[
                  {
                    level: 0,
                    bg: 'white',
                    label: 'Required Rate of Return',
                    tooltip: 'RequiredRateOfReturnTooltip',
                    unit: '%',
                    isPercentage: true,
                    value: capm.requiredRateOfReturn,
                    onChange: (requiredRateOfReturn: number) =>
                      dispatch(updateMainField({ isCapm: true, requiredRateOfReturn })),
                  },
                  {
                    level: 1,
                    bg: 'white',
                    allowNegatives: false,
                    label: 'Risk Free Rate',
                    tooltip: 'RiskFreeRateTooltip',
                    disabled: true,
                    unit: '%',
                    isPercentage: true,
                    value: capm.riskFreeRate,
                  },
                  {
                    level: 1,
                    bg: 'white',
                    allowNegatives: false,
                    label: 'Beta',
                    tooltip: 'BetaTooltip',
                    disabled: true,
                    value: capm.beta,
                  },
                  {
                    level: 1,
                    label: 'Equity Risk Premium',
                    tooltip: 'EquityRiskPremiumTooltip',
                    bg: 'white',
                    allowNegatives: false,
                    disabled: true,
                    unit: '%',
                    isPercentage: true,
                    value: capm.equityRiskPremium,
                  },
                ]}
                variant="fourGroup"
              />
            </Box>
          </FlexLayout>
        </Box>
        <Box
          bg="alabaster"
          px={6}
          py={6}
          sx={{ display: 'grid', gridGap: 8, border: 'border-dashed-link-water', minWidth: 477 }}
        >
          <FlexLayout flexDirection="column">
            <FlexLayout justifyContent="space-between" pb={6}>
              <TextWithTooltip
                tooltip="Expected Loss = Probability of Default x Loss Given Default"
                variant="xl-spaced-bold"
                color="deep-sapphire"
                label="Expected Loss"
                id="Expected Loss"
              />
              <FlexLayout sx={{ gap: 2 }}>
                <WithTooltip disabled label="elResetTooltip" tooltip="Revert to TPAccurate recommendation">
                  <Box bg="white" px={2} py={1} sx={{ border: 'border', borderRadius: 'm' }}>
                    <Icon
                      icon="reset"
                      color="bali-hai"
                      onClick={() => dispatch(resetToRecommendation({ isExpectedLossReset: true }))}
                    />
                  </Box>
                </WithTooltip>
                <Box bg="white" px={2} py={1} sx={{ border: 'border', borderRadius: 'm' }}>
                  <Icon icon="edit" color="bali-hai" onClick={() => setIsExpectedLossModalOpen(true)} />
                </Box>
              </FlexLayout>
            </FlexLayout>
            <Box sx={{ marginLeft: '17px' }}>
              <InputGroup
                isLinked={true}
                showTopInputOnly={false}
                inputs={[
                  {
                    level: 0,
                    label: 'Expected Loss',
                    tooltip: 'ExpectedLossTooltip',
                    bg: 'white',
                    unit: '%',
                    isPercentage: true,
                    value: expectedLoss.expectedLoss,
                    onChange: (expectedLoss: number) =>
                      dispatch(updateMainField({ isExpectedLoss: true, expectedLoss })),
                  },
                  {
                    level: 1,
                    label: 'Probability of Default',
                    tooltip: 'ProbabilityOfDefaultTooltip',
                    bg: 'white',
                    allowNegatives: false,
                    disabled: true,
                    unit: '%',
                    isPercentage: true,
                    value: expectedLoss.probabilityOfDefault,
                  },
                  {
                    level: 1,
                    label: 'Loss Given Default',
                    tooltip: 'LossGivenDefaultTooltip',
                    bg: 'white',
                    allowNegatives: false,
                    disabled: true,
                    unit: '%',
                    isPercentage: true,
                    value: expectedLoss.lossGivenDefault,
                  },
                ]}
                variant="threeGroup"
              />
            </Box>
          </FlexLayout>
        </Box>
      </FlexLayout>
      <CapmModel isShowing={isCapmModalOpen} onHide={() => setIsCapmModalOpen(false)} />
      <ExpectedLossModal isShowing={isExpectedLossModalOpen} onHide={() => setIsExpectedLossModalOpen(false)} />
    </>
  );
};

export default B2BRates;
