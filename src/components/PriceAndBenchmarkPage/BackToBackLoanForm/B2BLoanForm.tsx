import { FlexLayout, LoadingSpinner } from 'ui';
import { useAppSelector } from 'hooks';
import { report } from 'reducers/report.slice';

import B2BLegsSection from './B2BLegsSection';
import B2BDetailSection from './B2BDetailSection';
import B2BRates from './B2BRates';

function BackToBackLoanForm() {
  const { lenders } = useAppSelector(report);

  if (!lenders || lenders?.length === 0) return <LoadingSpinner />;

  return (
    <FlexLayout flexDirection="column" space={8}>
      <B2BLegsSection />

      <B2BDetailSection />

      <B2BRates />
    </FlexLayout>
  );
}

export default BackToBackLoanForm;
