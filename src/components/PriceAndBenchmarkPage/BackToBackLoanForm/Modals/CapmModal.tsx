import { useContext } from 'react';

import { getBeta, getEquityRiskPremium } from 'api';
import { Button, DateInput, FlexLayout, Modal, NumberInput, SingleSelect, Switch, Text, TextInput } from 'ui';
import { useAppDispatch, useAppSelector } from 'hooks';
import { UserInfoContext } from 'context';
import { CountrySingleSelect } from 'components/Shared';
import { toggleOverride, updateB2BDetailField, setCapmData, resetSectionToRecommendation } from 'reducers/report.slice';
import { displayNumber2 } from 'utils/strings';
import { errorHandler } from 'utils/errors';
import { reportUtils, dateUtils } from 'utils';
import { b2bLoansEnums } from 'enums';
import { BetaType, BetaRegionsType, CountryValueType } from 'types';

import B2BTag from './B2BTag';
import betaSectorOptions from './BetaSectorOptions';

const { betaRegions, betaTypes } = b2bLoansEnums;

const CapmModel = ({ isShowing, onHide }: { isShowing: boolean; onHide: () => void }) => {
  const dispatch = useAppDispatch();
  const { capm, capmOverride, overrideToggles, issueDate, maturityDate, currency } = useAppSelector(
    (state: any) => state.report
  );
  const isCapm = true;
  const { userInfo } = useContext(UserInfoContext);
  const numberDisplayOptions = { decimalPoint: userInfo.decimalPoint, minDig: 0, maxDig: 2, defaultValue: '-' };

  const riskFreeRateToggle = overrideToggles.riskFreeRate;
  const betaToggle = overrideToggles.beta;
  const equityRiskPremiumToggle = overrideToggles.equityRiskPremium;

  // Data is shown and used based on the override toggles for each field
  const riskFreeRateData = riskFreeRateToggle ? capmOverride : capm;
  const betaData = betaToggle ? capmOverride : capm;
  const equityRiskPremiumData = equityRiskPremiumToggle ? capmOverride : capm;

  const isBetaUnlevered = (betaType: BetaType): boolean => {
    return betaType === betaTypes.unleveredBeta || betaType === betaTypes.unleveredBetaCorrectedForCash;
  };

  const getReleveredBeta = (beta: number, debt: number, equity: number): number => {
    if (debt == null || equity == null) return beta;

    const releveredBeta = beta * (1 + debt / equity);

    if (!Number.isFinite(releveredBeta)) return 0;

    return releveredBeta;
  };

  /* Relever if user is not overriding and one of unlevered types is selected */
  const releverBeta = (beta: number, betaType: BetaType) => {
    if (!betaToggle && isBetaUnlevered(betaType)) {
      const { betaDebt, betaEquity } = betaData;
      const releveredBeta = getReleveredBeta(beta, betaDebt, betaEquity);
      dispatch(
        updateB2BDetailField({ beta: releveredBeta, betaDebt, betaEquity, isOverrideField: betaToggle, isCapm })
      );
    }
  };

  const onBetaTypeChange = async (betaType: BetaType) => {
    if (!betaData.betaIndustrySector || !betaData.betaRegion) {
      return dispatch(updateB2BDetailField({ betaType, isOverrideField: betaToggle, isCapm }));
    }

    try {
      const { betaIndustrySector, betaRegion } = betaData;
      const { beta } = await getBeta({ type: betaType, industry: betaIndustrySector, region: betaRegion, issueDate });

      dispatch(updateB2BDetailField({ betaType, beta, betaUnlevered: beta, isOverrideField: betaToggle, isCapm }));

      releverBeta(beta, betaType);
    } catch (err) {
      errorHandler(err);
    }
  };

  const onBetaRegionChange = async (betaRegion: BetaRegionsType) => {
    if (!betaData.betaIndustrySector || !betaData.betaType) {
      return dispatch(updateB2BDetailField({ betaRegion, isOverrideField: betaToggle, isCapm }));
    }

    try {
      const { betaIndustrySector, betaType } = betaData;
      const { beta } = await getBeta({ region: betaRegion, industry: betaIndustrySector, type: betaType, issueDate });

      dispatch(updateB2BDetailField({ betaRegion, beta, betaUnlevered: beta, isOverrideField: betaToggle, isCapm }));

      releverBeta(beta, betaData.betaType);
    } catch (err) {
      errorHandler(err);
    }
  };

  const onBetaIndustryChange = async (betaIndustrySector: string) => {
    if (!betaData.betaRegion || !betaData.betaType) {
      return dispatch(updateB2BDetailField({ betaIndustrySector, isOverrideField: betaToggle, isCapm }));
    }

    try {
      const { betaRegion, betaType } = betaData;
      const { beta } = await getBeta({ industry: betaIndustrySector, region: betaRegion, type: betaType, issueDate });

      dispatch(
        updateB2BDetailField({ betaIndustrySector, beta, betaUnlevered: beta, isOverrideField: betaToggle, isCapm })
      );

      releverBeta(beta, betaData.betaType);
    } catch (err) {
      errorHandler(err);
    }
  };

  const onEquityRiskPremiumCountryChange = async (equityRiskPremiumCountry: CountryValueType) => {
    // used to prevent the api call when the user is in override mode
    if (equityRiskPremiumToggle) {
      dispatch(updateB2BDetailField({ equityRiskPremiumCountry, isOverrideField: equityRiskPremiumToggle, isCapm }));
      return;
    }

    try {
      const { equityRiskPremium } = await getEquityRiskPremium({ countryName: equityRiskPremiumCountry, issueDate });

      dispatch(updateB2BDetailField({ equityRiskPremiumCountry, isOverrideField: equityRiskPremiumToggle, isCapm }));
      dispatch(updateB2BDetailField({ equityRiskPremium, isOverrideField: equityRiskPremiumToggle, isCapm }));
    } catch (err) {
      errorHandler(err);
    }
  };

  const getBetaValueHelperText = (): 'Re-levered' | undefined => {
    if (!isBetaUnlevered(betaData.betaType)) return;
    if (betaData.betaDebt == null || betaData.betaEquity == null) return;

    return 'Re-levered';
  };

  const onCapmSubmit = () => {
    const { riskFreeRate } = riskFreeRateData;
    const { beta } = betaData;
    const { equityRiskPremium } = equityRiskPremiumData;

    const capmObject = {
      riskFreeRate,
      riskFreeRateSource: riskFreeRateData.riskFreeRateSource,

      beta,
      betaUnlevered: betaData.betaUnlevered,
      betaDebt: betaData.betaDebt,
      betaEquity: betaData.betaEquity,
      betaSource: betaData.betaSource,
      betaType: betaData.betaType,
      betaIndustrySector: betaData.betaIndustrySector,
      betaRegion: betaData.betaRegion,

      equityRiskPremium,
      equityRiskPremiumCountry: equityRiskPremiumData.equityRiskPremiumCountry,
      equityRiskPremiumSource: equityRiskPremiumData.equityRiskPremiumSource,

      requiredRateOfReturn: reportUtils.calculateRequiredRateOfReturn({ riskFreeRate, beta, equityRiskPremium }),
    };

    dispatch(setCapmData(capmObject));
    onHide();
  };

  const isSubmitDisabled = (): boolean => {
    if (!riskFreeRateData.riskFreeRate && riskFreeRateData.riskFreeRate !== 0) return true;
    if (!riskFreeRateData.riskFreeRateSource) return true;

    if (!betaData.beta && betaData.beta !== 0) return true;
    if (!betaData.betaSource) return true;
    if (!betaData.betaType) return true;
    if (!betaData.betaIndustrySector) return true;
    if (!betaData.betaRegion) return true;
    if (isBetaUnlevered(betaData.betaType) && (betaData.betaDebt == null || betaData.betaEquity == null)) return true;

    if (!equityRiskPremiumData.equityRiskPremium && equityRiskPremiumData.equityRiskPremium !== 0) return true;
    if (!equityRiskPremiumData.equityRiskPremiumCountry) return true;
    if (!equityRiskPremiumData.equityRiskPremiumSource) return true;

    return false;
  };

  if (!isShowing) return null;

  return (
    <Modal
      width="746px"
      overflowY="scroll"
      onHide={onHide}
      actionButtons={<Button text="Submit" disabled={isSubmitDisabled()} onClick={onCapmSubmit} />}
    >
      <FlexLayout justifyContent="space-between">
        <Text variant="xl-spaced-bold" color="deep-sapphire">
          Risk Free Rate
        </Text>
        <Switch
          label="Override"
          isActive={riskFreeRateToggle}
          onChange={() => {
            dispatch(toggleOverride('riskFreeRate'));
            dispatch(resetSectionToRecommendation({ section: 'riskFreeRate', toggle: riskFreeRateToggle }));
          }}
        />
      </FlexLayout>
      <FlexLayout sx={{ gap: 6 }}>
        <NumberInput
          label="Value"
          tooltip="RiskFreeRateTooltip"
          unit="%"
          width="xs"
          inputType="float"
          isPercentage
          disabled={!riskFreeRateToggle}
          value={riskFreeRateData.riskFreeRate}
          onChange={(riskFreeRate: number) =>
            dispatch(updateB2BDetailField({ riskFreeRate, isOverrideField: riskFreeRateToggle, isCapm }))
          }
        />
        <TextInput
          label="Source"
          tooltip="RiskFreeRateSourceTooltip"
          disabled={!riskFreeRateToggle}
          value={riskFreeRateData.riskFreeRateSource}
          onChange={(riskFreeRateSource: string) =>
            dispatch(updateB2BDetailField({ riskFreeRateSource, isOverrideField: riskFreeRateToggle, isCapm }))
          }
        />
      </FlexLayout>
      <FlexLayout sx={{ gap: 4, paddingBottom: '32px', borderBottom: '1px solid #E0E5EE' }}>
        {riskFreeRateToggle ? (
          <>
            <DateInput
              label="Date"
              tooltip="riskFreeRateIssueDateTooltip"
              value={riskFreeRateData.riskFreeRateIssueDate}
              onChange={(riskFreeRateIssueDate: Date) =>
                dispatch(updateB2BDetailField({ riskFreeRateIssueDate, isOverrideField: riskFreeRateToggle, isCapm }))
              }
            />
            <TextInput
              label="Currency"
              tooltip="riskFreeRateCurrencyTooltip"
              width="xs"
              value={riskFreeRateData.riskFreeRateCurrency}
              onChange={(riskFreeRateCurrency: string) =>
                dispatch(updateB2BDetailField({ riskFreeRateCurrency, isOverrideField: riskFreeRateToggle, isCapm }))
              }
            />
            <NumberInput
              label="Tenor"
              tooltip="riskFreeRateTenorTooltip"
              width="xs"
              allowNegatives={false}
              value={riskFreeRateData.riskFreeRateTenor}
              onChange={(riskFreeRateTenor: string) =>
                dispatch(updateB2BDetailField({ riskFreeRateTenor, isOverrideField: riskFreeRateToggle, isCapm }))
              }
            />
          </>
        ) : (
          <>
            <B2BTag label="Date" value={dateUtils.formatDateString(issueDate, userInfo.dateFormat)} />
            <B2BTag label="Currency" value={currency} />
            <B2BTag
              label="Tenor"
              value={displayNumber2(dateUtils.createTenor(issueDate, maturityDate), numberDisplayOptions)}
            />
          </>
        )}
      </FlexLayout>

      <FlexLayout justifyContent="space-between">
        <Text variant="xl-spaced-bold" color="deep-sapphire">
          Beta
        </Text>
        <Switch
          label="Override"
          isActive={betaToggle}
          onChange={() => {
            dispatch(toggleOverride('beta'));
            dispatch(resetSectionToRecommendation({ section: 'beta', toggle: betaToggle }));
          }}
        />
      </FlexLayout>
      <FlexLayout flexDirection="column" sx={{ paddingBottom: '32px', borderBottom: '1px solid #E0E5EE' }}>
        <FlexLayout sx={{ gap: 6 }}>
          <NumberInput
            label="Value"
            tooltip="BetaTooltip"
            width="xs"
            inputType="float"
            helperText={getBetaValueHelperText()}
            disabled={!betaToggle}
            value={betaData.beta}
            onChange={(beta: number) => dispatch(updateB2BDetailField({ beta, isOverrideField: betaToggle, isCapm }))}
          />
          {betaToggle ? (
            <TextInput
              label="Industry Sector"
              tooltip="IndustrySectorTooltip"
              value={betaData.betaIndustrySector}
              onChange={(betaIndustrySector: string) =>
                dispatch(updateB2BDetailField({ betaIndustrySector, isOverrideField: betaToggle, isCapm }))
              }
            />
          ) : (
            <SingleSelect
              label="Industry Sector"
              tooltip="IndustrySectorTooltip"
              options={betaSectorOptions}
              value={betaData.betaIndustrySector}
              onChange={onBetaIndustryChange}
            />
          )}
          {betaToggle ? (
            <TextInput
              label="Region"
              tooltip="BetaCountryTooltip"
              value={betaData.betaRegion}
              onChange={(betaRegion: BetaRegionsType) =>
                dispatch(updateB2BDetailField({ betaRegion, isOverrideField: betaToggle, isCapm }))
              }
            />
          ) : (
            <SingleSelect
              label="Region"
              tooltip="BetaCountryTooltip"
              options={[
                { label: 'Global', value: betaRegions.global },
                { label: 'Europe', value: betaRegions.europe },
                { label: 'Emerging Markets', value: betaRegions.emerging },
                { label: 'Japan', value: betaRegions.japan },
                { label: 'United States', value: betaRegions.us },
              ]}
              value={betaData.betaRegion}
              onChange={onBetaRegionChange}
            />
          )}
        </FlexLayout>
        <FlexLayout sx={{ gap: 6, marginTop: '24px' }}>
          {betaToggle ? (
            <TextInput
              label="Beta Type"
              tooltip="BetaTypeTooltip"
              value={betaData.betaType}
              onChange={(betaType: BetaType) =>
                dispatch(updateB2BDetailField({ betaType, isOverrideField: betaToggle, isCapm }))
              }
            />
          ) : (
            <SingleSelect
              label="Beta Type"
              tooltip="BetaTypeTooltip"
              options={[
                { value: betaTypes.beta, label: betaTypes.beta },
                { value: betaTypes.unleveredBeta, label: betaTypes.unleveredBeta },
                { value: betaTypes.unleveredBetaCorrectedForCash, label: betaTypes.unleveredBetaCorrectedForCash },
              ]}
              value={betaData.betaType}
              onChange={onBetaTypeChange}
            />
          )}
          <TextInput
            label="Source"
            tooltip="BetaSourceTooltip"
            disabled={!betaToggle}
            value={betaData.betaSource}
            onChange={(betaSource: string) =>
              dispatch(updateB2BDetailField({ betaSource, isOverrideField: betaToggle, isCapm }))
            }
          />
        </FlexLayout>
        {!betaToggle && isBetaUnlevered(betaData.betaType) && (
          <FlexLayout sx={{ gap: 6, marginTop: '24px' }}>
            <NumberInput
              label="Debt"
              tooltip="DebtTooltip"
              allowNegatives={false}
              value={betaData.betaDebt}
              onChange={(betaDebt: number) => {
                const releveredBeta = getReleveredBeta(betaData.betaUnlevered, betaDebt, betaData.betaEquity);
                dispatch(updateB2BDetailField({ betaDebt, beta: releveredBeta, isOverrideField: betaToggle, isCapm }));
              }}
            />
            <NumberInput
              label="Equity"
              tooltip="EquityTooltip"
              allowNegatives={false}
              value={betaData.betaEquity}
              onChange={(betaEquity: number) => {
                if (betaEquity === 0) return;
                const releveredBeta = getReleveredBeta(betaData.betaUnlevered, betaData.betaDebt, betaEquity);
                dispatch(
                  updateB2BDetailField({ betaEquity, beta: releveredBeta, isOverrideField: betaToggle, isCapm })
                );
              }}
            />
          </FlexLayout>
        )}
        <FlexLayout sx={{ gap: 4, marginTop: '24px' }}>
          {betaToggle ? (
            <DateInput
              label="Date"
              tooltip="betaIssueDateTooltip"
              value={betaData.betaIssueDate}
              onChange={(betaIssueDate: Date) =>
                dispatch(updateB2BDetailField({ betaIssueDate, isOverrideField: betaToggle, isCapm }))
              }
            />
          ) : (
            <B2BTag label="Date" value={String(new Date(issueDate)?.getFullYear())} />
          )}
        </FlexLayout>
      </FlexLayout>

      <FlexLayout justifyContent="space-between">
        <Text variant="xl-spaced-bold" color="deep-sapphire">
          Equity Risk Premium
        </Text>
        <Switch
          label="Override"
          isActive={equityRiskPremiumToggle}
          onChange={() => {
            dispatch(toggleOverride('equityRiskPremium'));
            dispatch(resetSectionToRecommendation({ section: 'equityRiskPremium', toggle: equityRiskPremiumToggle }));
          }}
        />
      </FlexLayout>
      <FlexLayout sx={{ gap: 6 }}>
        <NumberInput
          label="Value"
          unit="%"
          width="xs"
          tooltip="EquityRiskPremiumTooltip"
          inputType="float"
          isPercentage
          disabled={!equityRiskPremiumToggle}
          value={equityRiskPremiumData.equityRiskPremium}
          onChange={(equityRiskPremium: number) =>
            dispatch(updateB2BDetailField({ equityRiskPremium, isOverrideField: equityRiskPremiumToggle, isCapm }))
          }
        />
        <CountrySingleSelect
          tooltip="EquityRiskPremiumCountryTooltip"
          value={equityRiskPremiumData.equityRiskPremiumCountry}
          onChange={onEquityRiskPremiumCountryChange}
        />
        <TextInput
          label="Source"
          tooltip="EquityRiskPremiumSourceTooltip"
          disabled={!equityRiskPremiumToggle}
          value={equityRiskPremiumData.equityRiskPremiumSource}
          onChange={(equityRiskPremiumSource: string) =>
            dispatch(
              updateB2BDetailField({ equityRiskPremiumSource, isOverrideField: equityRiskPremiumToggle, isCapm })
            )
          }
        />
      </FlexLayout>
      <FlexLayout sx={{ gap: 4 }}>
        {equityRiskPremiumToggle ? (
          <DateInput
            label="Date"
            tooltip="equityRiskPremiumIssueDateTooltip"
            value={equityRiskPremiumData.equityRiskPremiumIssueDate}
            onChange={(equityRiskPremiumIssueDate: Date) =>
              dispatch(
                updateB2BDetailField({ equityRiskPremiumIssueDate, isOverrideField: equityRiskPremiumToggle, isCapm })
              )
            }
          />
        ) : (
          <B2BTag label="Date" value={String(new Date(issueDate)?.getFullYear())} />
        )}
      </FlexLayout>
    </Modal>
  );
};

export default CapmModel;
