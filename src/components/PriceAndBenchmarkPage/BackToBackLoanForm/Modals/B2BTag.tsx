import { FlexLayout, Text } from 'ui';

const B2BTag = ({ label, value }: { label: string; value: string | undefined }) => {
  return (
    <FlexLayout bg="alabaster" alignItems="baseline" px={4} py={2} sx={{ border: 'border', borderRadius: 'm' }}>
      <Text variant="s-spaced-medium-caps" color="bali-hai">
        {label}
      </Text>
      <Text variant="m-spaced" color="deep-sapphire" sx={{ marginLeft: 2 }}>
        {value ?? '-'}
      </Text>
    </FlexLayout>
  );
};

export default B2BTag;
