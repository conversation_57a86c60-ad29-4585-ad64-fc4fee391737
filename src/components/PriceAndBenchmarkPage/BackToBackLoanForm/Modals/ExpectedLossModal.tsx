import { useAppDispatch, useAppSelector } from 'hooks';
import { Button, FlexLayout, Modal, NumberInput, Switch, Text, TextInput, RadioGroup, Box } from 'ui';
import { reportUtils, dateUtils } from 'utils';
import {
  toggleOverride,
  updateB2BDetailField,
  setExpectedLossData,
  resetSectionToRecommendation,
} from 'reducers/report.slice';

import B2BTag from './B2BTag';

const ExpectedLossModal = ({ isShowing, onHide }: { isShowing: boolean; onHide: () => void }) => {
  const dispatch = useAppDispatch();
  const {
    borrowers,
    issueDate,
    maturityDate,
    expectedLoss,
    expectedLossOverride,
    overrideToggles,
    seniority,
    pricingApproach,
  } = useAppSelector((state: any) => state.report);
  const isExpectedLoss = true;

  const podToggle = overrideToggles.probabilityOfDefault;
  const lgdToggle = overrideToggles.lossGivenDefault;

  const expectedLossPodData = podToggle ? expectedLossOverride : expectedLoss;
  const expectedLossLgdData = lgdToggle ? expectedLossOverride : expectedLoss;

  const onProbabilityOfDefaultTypeChange = (probabilityOfDefaultType: 'Cumulative' | 'Annualized') => {
    const ultimateBorrower = borrowers[borrowers.length - 1];

    // Just change between types when user is overriding and prevent recalculating
    if (podToggle) {
      return dispatch(updateB2BDetailField({ probabilityOfDefaultType, isOverrideField: podToggle, isExpectedLoss }));
    }

    if (probabilityOfDefaultType === 'Cumulative') {
      const probabilityOfDefault = reportUtils.getCumulativeProbabilityOfDefault(
        ultimateBorrower,
        dateUtils.createTenor(issueDate, maturityDate),
        pricingApproach
      );
      dispatch(updateB2BDetailField({ probabilityOfDefault, isOverrideField: podToggle, isExpectedLoss }));
    }
    if (probabilityOfDefaultType === 'Annualized') {
      const probabilityOfDefault = reportUtils.getProbabilityOfDefault(ultimateBorrower, pricingApproach);
      dispatch(updateB2BDetailField({ probabilityOfDefault, isOverrideField: podToggle, isExpectedLoss }));
    }

    dispatch(updateB2BDetailField({ probabilityOfDefaultType, isOverrideField: podToggle, isExpectedLoss }));
  };

  const onExpectedLossSubmit = () => {
    const { probabilityOfDefault } = expectedLossPodData;
    const { lossGivenDefault } = expectedLossLgdData;

    const elObject = {
      probabilityOfDefault,
      probabilityOfDefaultType: expectedLossPodData.probabilityOfDefaultType,
      probabilityOfDefaultSource: expectedLossPodData.probabilityOfDefaultSource,

      lossGivenDefault,
      lossGivenDefaultSource: expectedLossLgdData.lossGivenDefaultSource,

      expectedLoss: reportUtils.calculateExpectedLoss({ probabilityOfDefault, lossGivenDefault }),
    };

    dispatch(setExpectedLossData(elObject));
    onHide();
  };

  const isSubmitDisabled = (): boolean => {
    if (!expectedLossPodData.probabilityOfDefault && expectedLossPodData.probabilityOfDefault !== 0) return true;
    if (!expectedLossPodData.probabilityOfDefaultType) return true;
    if (!expectedLossPodData.probabilityOfDefaultSource) return true;

    if (!expectedLossLgdData.lossGivenDefault && expectedLossLgdData.lossGivenDefault !== 0) return true;
    if (!expectedLossLgdData.lossGivenDefaultSource) return true;

    return false;
  };

  if (!isShowing) return null;

  return (
    <Modal
      width="746px"
      overflowY="scroll"
      onHide={onHide}
      actionButtons={<Button text="Submit" disabled={isSubmitDisabled()} onClick={onExpectedLossSubmit} />}
    >
      <FlexLayout justifyContent="space-between">
        <Text variant="xl-spaced-bold" color="deep-sapphire">
          Probability of Default
        </Text>
        <Switch
          label="Override"
          isActive={podToggle}
          onChange={() => {
            dispatch(toggleOverride('probabilityOfDefault'));
            dispatch(resetSectionToRecommendation({ section: 'probabilityOfDefault', toggle: podToggle }));
          }}
        />
      </FlexLayout>
      <FlexLayout sx={{ gap: 6 }}>
        <NumberInput
          label="Value"
          tooltip="PoDValueTooltip"
          isPercentage
          allowNegatives={false}
          unit="%"
          width="xs"
          inputType="float"
          disabled={!podToggle}
          value={expectedLossPodData.probabilityOfDefault}
          onChange={(probabilityOfDefault: number) =>
            dispatch(updateB2BDetailField({ probabilityOfDefault, isOverrideField: podToggle, isExpectedLoss }))
          }
        />
        <Box sx={{ marginTop: '27px' }}>
          <RadioGroup
            options={[
              { label: 'Cumulative', value: 'Cumulative' },
              { label: 'Annualized', value: 'Annualized' },
            ]}
            value={expectedLossPodData.probabilityOfDefaultType}
            onChange={onProbabilityOfDefaultTypeChange}
          />
        </Box>
      </FlexLayout>
      <TextInput
        label="Source"
        tooltip="PoDSourceTooltip"
        sx={{ paddingBottom: '32px', borderBottom: '1px solid #E0E5EE' }}
        disabled={!podToggle}
        value={expectedLossPodData.probabilityOfDefaultSource}
        onChange={(probabilityOfDefaultSource: number) =>
          dispatch(updateB2BDetailField({ probabilityOfDefaultSource, isOverrideField: podToggle, isExpectedLoss }))
        }
      />

      <FlexLayout justifyContent="space-between">
        <Text variant="xl-spaced-bold" color="deep-sapphire">
          Loss Given Default
        </Text>
        <Switch
          label="Override"
          isActive={lgdToggle}
          onChange={() => {
            dispatch(toggleOverride('lossGivenDefault'));
            dispatch(resetSectionToRecommendation({ section: 'lossGivenDefault', toggle: lgdToggle }));
          }}
        />
      </FlexLayout>
      <FlexLayout sx={{ gap: 6 }}>
        <NumberInput
          label="Value"
          tooltip="LGDValueTooltip"
          isPercentage
          allowNegatives={false}
          unit="%"
          width="xs"
          inputType="float"
          disabled={!lgdToggle}
          value={expectedLossLgdData.lossGivenDefault}
          onChange={(lossGivenDefault: number) =>
            dispatch(updateB2BDetailField({ lossGivenDefault, isOverrideField: lgdToggle, isExpectedLoss }))
          }
        />
        <TextInput
          label="Source"
          tooltip="LDSourceTooltip"
          disabled={!lgdToggle}
          value={expectedLossLgdData.lossGivenDefaultSource}
          onChange={(lossGivenDefaultSource: number) =>
            dispatch(updateB2BDetailField({ lossGivenDefaultSource, isOverrideField: lgdToggle, isExpectedLoss }))
          }
        />
      </FlexLayout>
      <FlexLayout sx={{ gap: 4 }}>
        <B2BTag label="Seniority tier of loan" value={seniority} />
      </FlexLayout>
    </Modal>
  );
};

export default ExpectedLossModal;
