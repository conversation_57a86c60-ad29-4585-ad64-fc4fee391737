import { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';

import {
  CurrencySingleSelect,
  LoanTypeSingleSelect,
  PaymentFrequencySingleSelect,
  SenioritySingleSelect,
} from 'components/Shared';
import {
  report,
  updateCurrency,
  updateField,
  updateIssueDate,
  updateRateType,
  restartStandardRenumeration,
} from 'reducers/report.slice';
import { useAppDispatch, useAppSelector } from 'hooks';
import { Box, DateInput, NumberInput, RadioGroup } from 'ui';
import { reportEnum } from 'enums';
import { LoanTypeType, PaymentFrequencyType, SeniorityType } from 'types';
import { Company } from 'types/database/Company.type';

import RenumerationSection from './RemunerationSection';
import EndDateInput from '../../EndDateInput';
import { loanTooltips, getPricingOptions } from '../../PriceAndBenchmarkPage.utils';

type PricingOptionsType = {
  tooltip: string;
  label: string;
  value: string;
};

const B2BDetailSection = () => {
  const history = useHistory();
  const dispatch = useAppDispatch();
  const {
    lenders,
    borrowers,
    issueDate,
    maturityDate,
    currency,
    amount,
    rateType,
    type,
    paymentFrequency,
    seniority,
    pricingApproach,
  } = useAppSelector(report);
  const [pricingOptionsState, setPricingOptionsState] = useState<Array<PricingOptionsType>>([]);
  const shouldLoanDetailInputsShow =
    lenders.length >= 2 &&
    borrowers.length >= 2 &&
    lenders.every((l: Company) => l?.id) &&
    borrowers.every((b: Company) => b?.id);
  const isEdit = history.location.pathname.includes('/edit');

  useEffect(() => {
    if (!shouldLoanDetailInputsShow) return;

    const { pricingOptions, initialOption } = getPricingOptions({
      lender: lenders[0],
      borrower: borrowers[borrowers.length - 1],
      reportType: reportEnum.BACK_TO_BACK_LOAN,
    });
    setPricingOptionsState(pricingOptions);
    if (!isEdit) {
      dispatch(updateField({ pricingApproach: initialOption }));
    }
  }, [shouldLoanDetailInputsShow, borrowers, lenders, isEdit, dispatch]);

  if (!shouldLoanDetailInputsShow) return null;

  return (
    <>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(16, 1fr)' }}>
        <DateInput
          label="Issue Date"
          dataTestId="issueDateInput"
          maxDate={new Date()}
          tooltip={loanTooltips.issueDate}
          value={issueDate}
          width="fullWidth"
          sx={{ gridColumn: 'span 3' }}
          onChange={(issueDate: Date) => dispatch(updateIssueDate({ issueDate }))}
        />
        <EndDateInput />
        <Box sx={{ gridColumn: 'span 4' }}>
          <CurrencySingleSelect
            tooltip={loanTooltips.currency}
            dataTestId="currencySelect"
            width="fullWidth"
            limitCurrencies
            value={currency}
            onChange={(currency: string) => dispatch(updateCurrency(currency))}
          />
        </Box>
        <Box sx={{ gridColumn: 'span 4' }}>
          <NumberInput
            allowNegatives={false}
            dataTestId="principalAmountInput"
            label="Principal Amount"
            tooltip={loanTooltips.amount}
            value={amount}
            width="fullWidth"
            onChange={(amount: number) => dispatch(updateField({ amount }))}
          />
        </Box>
      </Box>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(4, 1fr)' }}>
        <RadioGroup
          label="Interest Type"
          options={[
            { label: 'Fixed', value: 'fixed' },
            { label: 'Float', value: 'float' },
          ]}
          tooltip={loanTooltips.rateType}
          value={rateType?.type}
          width="fullWidth"
          onChange={(type) => {
            dispatch(updateRateType({ rateType: { type } }));
            dispatch(restartStandardRenumeration());
          }}
        />
        <PaymentFrequencySingleSelect
          currency={currency}
          issueDate={issueDate}
          endDate={maturityDate}
          rateType={rateType?.type}
          tooltip={loanTooltips.paymentFrequency}
          dataTestId="paymentFrequencySelect"
          value={paymentFrequency}
          width="fullWidth"
          onChange={(paymentFrequency: PaymentFrequencyType) => dispatch(updateField({ paymentFrequency }))}
        />
        <SenioritySingleSelect
          tooltip={loanTooltips.seniority}
          dataTestId="senioritySelect"
          value={seniority}
          width="fullWidth"
          onChange={(seniority: SeniorityType) => dispatch(updateField({ seniority }))}
        />
        <LoanTypeSingleSelect
          tooltip={loanTooltips.type}
          dataTestId="loanTypeSelect"
          value={type}
          width="fullWidth"
          onChange={(type: LoanTypeType) => dispatch(updateField({ type }))}
        />
      </Box>
      <RenumerationSection />
      <Box
        sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: pricingOptionsState?.length === 2 ? undefined : '48%' }}
      >
        <RadioGroup
          label="Pricing Approach"
          variant="row"
          radioVariant="primary"
          width="fullWidth"
          options={pricingOptionsState}
          value={pricingApproach}
          onChange={(pricingApproach) => dispatch(updateField({ pricingApproach }))}
        />
      </Box>
    </>
  );
};

export default B2BDetailSection;
