import { useHistory } from 'react-router-dom';
import _ from 'lodash';

import { useAppDispatch, useAppSelector } from 'hooks';
import { report, updateStandardRenumeration, setStandardRenumeration } from 'reducers/report.slice';
import { FlexLayout, NumberInput, RadioGroup, TextInput } from 'ui';
import { b2bLoansEnums } from 'enums';
import { Company } from 'types/database/Company.type';
import { useEffect } from 'react';

const RemunerationSection = () => {
  const history = useHistory();
  const dispatch = useAppDispatch();
  const { borrowers, riskTakerId, isRenumerationSectionShown, standardRemuneration, rateType } = useAppSelector(report);
  const { standardRenumerationTypes } = b2bLoansEnums;
  const [standardRenumerationOption, standardRenumerationOptionLabel] =
    rateType.type === 'fixed'
      ? [standardRenumerationTypes.percentage, standardRenumerationTypes.percentage]
      : [standardRenumerationTypes.basisPoints, 'Basis Points'];
  const isEdit = history.location.pathname.includes('/edit');

  useEffect(() => {
    // passthroughEntities are all borrowers except last (ultimate) borrower
    const passthroughEntities = borrowers.slice(0, -1);
    const defaultStandardRemuneration = passthroughEntities.map((borrower: Company) => ({
      id: borrower.id,
      name: borrower.name,
      type: standardRenumerationTypes.opexAndMarkup,
    }));

    if (isEdit && _.isEmpty(standardRemuneration)) {
      dispatch(setStandardRenumeration(defaultStandardRemuneration));
      return;
    }

    if (isEdit) return;

    // Used to set remuneration inputs in Price and benchmark
    if (_.isEmpty(standardRemuneration) && isRenumerationSectionShown) {
      dispatch(setStandardRenumeration(defaultStandardRemuneration));
    }
  }, [
    dispatch,
    borrowers,
    riskTakerId,
    standardRenumerationTypes,
    standardRemuneration,
    isRenumerationSectionShown,
    isEdit,
  ]);

  if (Object.keys(standardRemuneration).length === 0 || !isRenumerationSectionShown) return null;

  return (
    <FlexLayout bg="alabaster" flexDirection="column" p={4} space={4} sx={{ border: 'border-dashed-link-water' }}>
      {Object.values<Company>(standardRemuneration).map(({ name, id: borrowerId }: Company) => (
        <FlexLayout space={8} key={borrowerId}>
          <TextInput disabled label="Borrower" tooltip="BorrowerTooltip" value={name} />
          <RadioGroup
            bg="white"
            label="Remuneration"
            tooltip="StandardRemunerationTooltip"
            variant="row"
            radioVariant="primary"
            width="fitContent"
            options={[
              { value: standardRenumerationTypes.opexAndMarkup, label: 'Operating Cost & Markup' },
              { value: standardRenumerationOption, label: standardRenumerationOptionLabel },
            ]}
            value={standardRemuneration[borrowerId]?.type}
            onChange={(type: string) => dispatch(updateStandardRenumeration({ type, borrowerId }))}
          />
          <FlexLayout space={4}>
            {standardRemuneration[borrowerId]?.type === standardRenumerationTypes.opexAndMarkup && (
              <>
                <NumberInput
                  bg="white"
                  allowNegatives={false}
                  label="Operating Cost"
                  inputType="float"
                  width="s"
                  value={standardRemuneration[borrowerId]?.operationalCost}
                  onChange={(operationalCost: number) =>
                    dispatch(updateStandardRenumeration({ operationalCost, borrowerId }))
                  }
                />
                <NumberInput
                  bg="white"
                  allowNegatives={false}
                  label="Markup"
                  unit="%"
                  inputType="float"
                  width="xs"
                  value={standardRemuneration[borrowerId]?.markup}
                  onChange={(markup: number) => dispatch(updateStandardRenumeration({ markup, borrowerId }))}
                />
              </>
            )}
            {standardRemuneration[borrowerId]?.type === standardRenumerationOption && (
              <NumberInput
                bg="white"
                allowNegatives={false}
                label="Margin"
                unit={standardRenumerationOption === '%' ? '%' : 'bps'}
                inputType="float"
                width="xs"
                value={standardRemuneration[borrowerId]?.margin}
                onChange={(margin: number) => dispatch(updateStandardRenumeration({ margin, borrowerId }))}
              />
            )}
          </FlexLayout>
        </FlexLayout>
      ))}
    </FlexLayout>
  );
};

export default RemunerationSection;
