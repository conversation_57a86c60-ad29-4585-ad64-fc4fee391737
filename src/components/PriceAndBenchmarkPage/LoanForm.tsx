import { useContext, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import {
  CompanySingleSelect,
  CurrencySingleSelect,
  LoanTypeSingleSelect,
  PaymentFrequencySingleSelect,
  SenioritySingleSelect,
} from 'components/Shared';
import { UserInfoContext } from 'context/user';
import { report, updateCurrency, updateField, updateIssueDate, updateRateType } from 'reducers/report.slice';
import { LoanTypeType, PaymentFrequencyType, SeniorityType } from 'types';
import { Box, DateInput, FlexLayout, NumberInput, RadioGroup, SingleSelect, Switch, withLabel } from 'ui';
import { checkIsDisabled } from 'utils/features';

import EndDateInput from './EndDateInput';
import { loanTooltips } from './PriceAndBenchmarkPage.utils';

const DAYCOUNT_OPTIONS = {
  'ACT/365': { value: 'ACT/365', label: 'ACT/365' },
  'ACT/364': { value: 'ACT/364', label: 'ACT/364' },
  'ACT/360': { value: 'ACT/360', label: 'ACT/360' },
  'ACT/252': { value: 'ACT/252', label: 'ACT/252' },
  'BUS/252': { value: 'BUS/252', label: 'BUS/252' },
  '30/360': { value: '30/360', label: '30/360' },
};

const CURRENCY_DAYCOUNT_MAPPER = {
  USD: DAYCOUNT_OPTIONS['ACT/360'],
  EUR: DAYCOUNT_OPTIONS['ACT/360'],
  GBP: DAYCOUNT_OPTIONS['ACT/365'],
  CHF: DAYCOUNT_OPTIONS['ACT/360'],
  AUD: DAYCOUNT_OPTIONS['ACT/360'],
  JPY: DAYCOUNT_OPTIONS['ACT/360'],
  CAD: DAYCOUNT_OPTIONS['ACT/360'],
  CNH: DAYCOUNT_OPTIONS['ACT/360'],
  CNY: DAYCOUNT_OPTIONS['ACT/360'],
  HKD: DAYCOUNT_OPTIONS['ACT/365'],
  INR: DAYCOUNT_OPTIONS['ACT/365'],
  IDR: DAYCOUNT_OPTIONS['ACT/360'],
  KRW: DAYCOUNT_OPTIONS['ACT/365'],
  MYR: DAYCOUNT_OPTIONS['ACT/365'],
  PHP: DAYCOUNT_OPTIONS['ACT/360'],
  SGD: DAYCOUNT_OPTIONS['ACT/365'],
  TWD: DAYCOUNT_OPTIONS['ACT/365'],
  THB: DAYCOUNT_OPTIONS['ACT/365'],
  DKK: DAYCOUNT_OPTIONS['ACT/360'],
  NOK: DAYCOUNT_OPTIONS['ACT/360'],
  SEK: DAYCOUNT_OPTIONS['ACT/360'],
  CZK: DAYCOUNT_OPTIONS['ACT/360'],
  HUF: DAYCOUNT_OPTIONS['ACT/360'],
  RON: DAYCOUNT_OPTIONS['ACT/360'],
  PLN: DAYCOUNT_OPTIONS['ACT/360'],
  RUB: DAYCOUNT_OPTIONS['ACT/365'],
  NZD: DAYCOUNT_OPTIONS['ACT/360'],
  BRL: DAYCOUNT_OPTIONS['BUS/252'],
  MXN: DAYCOUNT_OPTIONS['ACT/360'],
  PEN: DAYCOUNT_OPTIONS['ACT/360'],
  ILS: DAYCOUNT_OPTIONS['ACT/365'],
  TRY: DAYCOUNT_OPTIONS['ACT/360'],
  ZAR: DAYCOUNT_OPTIONS['ACT/365'],
};

const SwitchWithLabel = withLabel(Switch);

function LoanForm() {
  const dispatch = useDispatch();
  const history = useHistory();
  const {
    lender,
    borrower,
    issueDate,
    maturityDate,
    currency,
    amount,
    rateType,
    type,
    paymentFrequency,
    seniority,
    dayCount,
    prepayment,
  } = useSelector(report);
  const { userInfo } = useContext(UserInfoContext);
  const { features } = userInfo;
  const isEdit = history.location.pathname.includes('/edit');

  useEffect(() => {
    if (rateType?.type !== 'fixed') {
      dispatch(updateField({ prepayment: false, isDirty: false }));
    }
  }, [rateType?.type]);

  return (
    <FlexLayout flexDirection="column" space={8}>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
        <CompanySingleSelect
          isLoanApproachCalculated={userInfo.client.isLoanApproachCalculated}
          checkCreditRating
          label="Lender"
          dataTestId="lenderCompanySelect"
          excludedValues={[borrower?.id]}
          tooltip={loanTooltips.lender}
          disabled={checkIsDisabled(isEdit, features.loanNumber)}
          value={lender?.id}
          width="fullWidth"
          onChange={(value: number) => dispatch(updateField({ lender: value }))}
        />
        <CompanySingleSelect
          checkCreditRating
          label="Borrower"
          dataTestId="borrowerCompanySelect"
          excludedValues={[lender?.id]}
          tooltip={loanTooltips.borrower}
          disabled={checkIsDisabled(isEdit, features.loanNumber)}
          value={borrower?.id}
          width="fullWidth"
          onChange={(value: number) => dispatch(updateField({ borrower: value }))}
        />
      </Box>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(16, 1fr)' }}>
        <DateInput
          label="Issue Date"
          dataTestId="issueDateInput"
          minDate={new Date('2014-07-01')}
          maxDate={new Date()}
          tooltip={loanTooltips.issueDate}
          value={issueDate}
          width="fullWidth"
          sx={{ gridColumn: 'span 3' }}
          onChange={(value: Date) => dispatch(updateIssueDate({ issueDate: value }))}
        />
        <EndDateInput />
        <Box sx={{ gridColumn: 'span 4' }}>
          <CurrencySingleSelect
            tooltip={loanTooltips.currency}
            dataTestId="currencySelect"
            width="fullWidth"
            limitCurrencies
            value={currency}
            onChange={(value: string) => {
              if (value in CURRENCY_DAYCOUNT_MAPPER) {
                dispatch(
                  updateField({
                    dayCount: CURRENCY_DAYCOUNT_MAPPER[value as keyof typeof CURRENCY_DAYCOUNT_MAPPER].value,
                  })
                );
              }
              dispatch(updateCurrency(value));
            }}
          />
        </Box>
        <Box sx={{ gridColumn: 'span 4' }}>
          <NumberInput
            inputType="float"
            decimalPlaces={2}
            allowNegatives={false}
            dataTestId="principalAmountInput"
            label="Principal Amount"
            tooltip={loanTooltips.amount}
            value={amount}
            width="fullWidth"
            onChange={(value: number) => dispatch(updateField({ amount: value }))}
          />
        </Box>
      </Box>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(4, 1fr)' }}>
        <RadioGroup
          label="Interest Type"
          options={[
            { label: 'Fixed', value: 'fixed' },
            { label: 'Float', value: 'float' },
          ]}
          tooltip={loanTooltips.rateType}
          value={rateType?.type}
          width="fullWidth"
          onChange={(value) => dispatch(updateRateType({ rateType: { type: value } }))}
        />
        <PaymentFrequencySingleSelect
          currency={currency}
          issueDate={issueDate}
          endDate={maturityDate}
          rateType={rateType?.type}
          tooltip={loanTooltips.paymentFrequency}
          dataTestId="paymentFrequencySelect"
          value={paymentFrequency}
          width="fullWidth"
          onChange={(paymentFrequency: PaymentFrequencyType) => dispatch(updateField({ paymentFrequency }))}
        />
        <SenioritySingleSelect
          tooltip={loanTooltips.seniority}
          dataTestId="senioritySelect"
          value={seniority}
          width="fullWidth"
          onChange={(seniority: SeniorityType) => dispatch(updateField({ seniority }))}
        />
        <LoanTypeSingleSelect
          tooltip={loanTooltips.type}
          dataTestId="loanTypeSelect"
          value={type}
          width="fullWidth"
          onChange={(type: LoanTypeType) => dispatch(updateField({ type }))}
        />
        <SingleSelect
          label="Day Count"
          width="fullWidth"
          options={Object.values(DAYCOUNT_OPTIONS)}
          value={dayCount}
          onChange={(dayCount: string) => dispatch(updateField({ dayCount }))}
        />
        {features.prepayment && rateType?.type === 'fixed' && (
          <SwitchWithLabel
            label="Borrower prepayment option"
            tooltip="Enable if borrower can prepay loan principal at any time without penalty."
            onChange={(prepayment: boolean) => dispatch(updateField({ prepayment }))}
            isActive={prepayment}
          />
        )}
      </Box>
    </FlexLayout>
  );
}

export default LoanForm;
