import type { AppDispatch } from 'store';
import { setReportData } from 'reducers/report.slice';
import { createCompanyValue } from 'utils/companies';
import { reportEnum, pricingMethodologyEnum, frequencyEnum } from 'enums';
import { Company } from 'types/database/Company.type';

const properties = ['id', 'parentCompanyId', 'creditRating', 'name', 'industry', 'country'];

const onFillDataClick = ({
  data,
  companies,
  dispatch,
}: {
  data: any;
  companies: Array<Company>;
  dispatch: AppDispatch;
}) => {
  const company1 = createCompanyValue(companies[0], properties);
  const company2 = createCompanyValue(companies[1], properties);
  const amount = 1_000_000;
  const currency = 'USD';
  const paymentFrequency = frequencyEnum.MONTHLY;
  const seniority = 'Senior Secured';
  const issueDate = '2023-01-01';
  const maturityDate = '2024-01-01';

  if (data.reportType === reportEnum.LOAN) {
    return dispatch(
      setReportData({
        reportType: reportEnum.LOAN,
        borrower: company1,
        lender: company2,
        amount,
        currency,
        issueDate,
        maturityDate,
        paymentFrequency,
        seniority,
        interestRate: 5,
        rateType: { type: 'fixed' },
        type: 'Bullet',
      })
    );
  }
  if (data.reportType === reportEnum.GUARANTEE) {
    return dispatch(
      setReportData({
        reportType: reportEnum.GUARANTEE,
        guarantor: company1,
        principal: company2,
        amount,
        currency,
        issueDate,
        terminationDate: maturityDate,
        paymentFrequency,
        seniority,
        pricingMethodology: pricingMethodologyEnum.YIELD_EXPECTED_LOSS_APPROACH,
      })
    );
  }
};

export default onFillDataClick;
