import { format } from 'date-fns';
import _ from 'lodash';

import { createGuarantees, createLoans, getGuaranteeMassUploadTemplate, getLoanMassUploadTemplate } from '~/api';
import { DATE_FNS_FORMATS, pricingApproachEnum, pricingMethodologyEnum, reportEnum } from '~/enums';
import { getCompanyRating, getCompanyRatingAdj, isParentCompany } from '~/utils/companies';
import { getPricingApproachFromTemplates } from '~/utils/report';

const requiredCompanyFields = ['id', 'industry', 'name', 'parentCompanyId', 'country', 'creditRating'];

function getLoansFromSheet(sheet, companies) {
  if (sheet.length === 0) {
    throw new Error('Sheet is empty. Please upload populated sheet.');
  }
  if (!('Lender' in sheet[0])) throw new Error('Wrong template.');
  const loans = [];
  for (let i = 0, len = sheet.length; i < len; i++) {
    const loan = sheet[i];

    const lender = companies.find((company) => company.name === loan['Lender']);
    if (!lender) throw new Error(`Lender company in row ${i + 2} does not exist`);

    const borrower = companies.find((company) => company.name === loan['Borrower']);
    if (!borrower) throw new Error(`Borrower company in row ${i + 2} does not exist`);

    loans.push({
      lender: _.pick(lender, requiredCompanyFields),
      borrower: _.pick(borrower, requiredCompanyFields),
      issueDate: format(loan['Issue Date'], DATE_FNS_FORMATS.ISO),
      maturityDate: format(loan['Maturity Date'], DATE_FNS_FORMATS.ISO),
      currency: loan['Currency'],
      amount: loan['Principal Amount'],
      paymentFrequency: loan['Compounding Frequency'],
      seniority: loan['Seniority'],
      pricingApproach: getPricingApproachFromTemplates(loan['Pricing Approach']),
      rateType: { type: loan['Interest Type']?.toLowerCase() },
      type: loan['Type'],
      dayCount: loan['Day Count'],
      prepayment: loan['Prepayment'] === 'Yes' ? true : false,
    });
  }

  for (const loan of loans) {
    for (const [key, value] of Object.entries(loan)) {
      if (key === 'rateType' && value?.type == null) throw new Error('Interest Type is required');
      if (value == null) throw new Error('All fields are required');
    }
  }

  return loans;
}

function getGuaranteesFromSheet(sheet, companies) {
  if (sheet.length === 0) {
    throw new Error('Sheet is empty. Please upload populated sheet.');
  }
  if (!('Guarantor' in sheet[0])) throw new Error('Wrong template.');
  const guarantees = [];
  for (let i = 0, len = sheet.length; i < len; i++) {
    const guarantee = sheet[i];

    const guarantor = companies.find((company) => company.name === guarantee['Guarantor']);
    if (!guarantor) throw new Error(`Guarantor company in row ${i + 2} does not exist`);

    const principal = companies.find((company) => company.name === guarantee['Principal']);
    if (!principal) throw new Error(`Principal company in row ${i + 2} does not exist`);

    const pricingMethodology = guarantee['Pricing Methodology'];

    guarantees.push({
      guarantor: _.pick(guarantor, requiredCompanyFields),
      principal: _.pick(principal, requiredCompanyFields),
      issueDate: format(guarantee['Issue Date'], DATE_FNS_FORMATS.ISO),
      terminationDate: format(guarantee['Termination Date'], DATE_FNS_FORMATS.ISO),
      currency: guarantee['Currency'],
      amount: guarantee['Amount'],
      paymentFrequency: guarantee['Payment Frequency'],
      seniority: pricingMethodology === pricingMethodologyEnum.SECURITY_APPROACH ? null : guarantee['Seniority'],
      pricingApproach: getPricingApproachFromTemplates(guarantee['Pricing Approach']),
      pricingMethodology,
    });
  }

  return guarantees;
}

export const reportsUploadTemplateMethodMapper = {
  [reportEnum.LOAN]: { getReportsFromSheet: getLoansFromSheet, createReports: createLoans },
  [reportEnum.BACK_TO_BACK_LOAN]: {
    getReportsFromSheet: () => {
      throw new Error('Feature not available for back-to-back loans yet.aa');
    },
    createReports: () => {
      throw new Error('Feature not available for back-to-back loans yet.');
    },
  },
  [reportEnum.GUARANTEE]: { getReportsFromSheet: getGuaranteesFromSheet, createReports: createGuarantees },
};

export const reportsDownloadTemplateMethodMapper = {
  [reportEnum.LOAN]: getLoanMassUploadTemplate,
  [reportEnum.BACK_TO_BACK_LOAN]: async () => {
    throw new Error('Feature not available for back-to-back loans yet.');
  },
  [reportEnum.GUARANTEE]: getGuaranteeMassUploadTemplate,
};

export const loanTooltips = {
  lender: 'Select the company that will provide the loan.',
  borrower: 'Select the company that will receive the loan.',
  issueDate:
    'Select the date on which the loan will be issued from the lender to the borrower.<br/>' +
    'The data applied to the loan will be specific to the issue date (or the closest date before with available data).',
  maturityDate:
    'Select the date on which the loan will mature and be repaid by the borrower.<br/>' +
    'The data applied to the loan will be specific to the tenor of the loan ' +
    '(the length of time between the issue date and maturity date).',
  tenor: 'Enter the tenor of loan in the number of years and months.',
  currency:
    'Select the currency in which the loan will be denominated.<br/>' +
    'The data applied to the loan will be specific to the selected currency.',
  amount: 'Enter the quantum of the loan, or the amount of money being lent and borrowed.',
  rateType:
    'Select the desired interest type of the loan.<br/>' +
    'The data applied to the loan will be specific to the selected interest type.',
  seniority:
    'Select the tranche or level of seniority of the loan from highest (Senior Secured) to lowest (Subordinated).<br/>' +
    'The data applied to the loan will be specific to the selected seniority.',
  standAloneApproach: 'This pricing approach applies comparable data at the stand-alone issuer rating of the borrower.',
  implicitSupportApproach:
    'OECD Recommended.<br/>' +
    'This pricing approach applies comparable data at the implicit support adjusted rating of the borrower.',
  paymentFrequency:
    'Select the frequency at which interest compounds on the loan.<br/>' +
    'The interest frequency will correspond to this selection except in the case of balloon loans.',
  type:
    'Select the loan type.<br/>' +
    'Bullet loans have interest paid periodically and principal repaid at maturity.<br/>' +
    'Balloon loans have both interest and principal repaid at maturity.',
};

export const guaranteeTooltips = {
  guarantor:
    'Select the company that will provide the guarantee.<br /><br />In the case of multiple Guarantors, create these<br /> as a new single entry in the Group Information section.',
  principal:
    'Select the company that will receive the guarantee.<br />' +
    'Note: Only companies with a probability of default are shown.',
  issueDate:
    'Select the date on which the guarantee will be issued from the guarantor to the principal.<br/>' +
    'The data applied to the guarantee will be specific to the issue date (or the closest date before with available data).',
  tenor: 'Enter the tenor of guarantee in the number of years and months.',
  terminationDate:
    'Select the date on which the guarantee will terminate and no longer be valid.<br/>' +
    'The data applied to the guarantee will be specific to the period of the guarantee ' +
    '(the length of time between the issue date and termination date).',
  currency:
    'Select the currency of denomination of the liability that the guarantee covers.<br/>' +
    'The data applied to the guarantee will be specific to the selected currency.',
  amount: 'Enter the quantum of the liability that the guarantee covers.',
  standAloneApproach:
    'This pricing approach applies comparable data at the stand-alone issuer ratings of the guarantor and principal.',
  implicitSupportApproach:
    'OECD Recommended.<br/>' +
    'This pricing approach applies comparable data at the ' +
    'implicit support adjusted ratings of the guarantor and principal<br/>' +
    '(note: the parent company cannot have an ' +
    'implicit support adjusted rating so its issuer rating is applied under this approach).',
  paymentFrequency: 'Select the frequency of fee interest for the guarantee.',
  seniority: 'Select the tranche or level of seniority of the liability that the guarantee covers.',
  pricingMethodology:
    "The Yield - Expected Loss approach calculates the range as a function of the savings the Principal would receive<br /> from the guarantee (yield) and the Guarantor's cost of the guarantee (expected loss).<br /><br /> The Security approach calculates the range as a function of the spread<br /> between interest levels of senior secured, unsubordinated, and subordinated comparables.",
};

export function getPricingOptions(formData) {
  const isSecurityMethodology = formData?.pricingMethodology === pricingMethodologyEnum.SECURITY_APPROACH;
  const isLoan = formData?.reportType === reportEnum.LOAN || formData?.reportType === reportEnum.BACK_TO_BACK_LOAN;

  let company1st, company2nd;
  if (isLoan) {
    company1st = formData?.lender;
    company2nd = formData?.borrower;
  } else {
    company1st = formData?.guarantor;
    company2nd = formData?.principal;
  }

  const company1stName = company1st?.name;
  const company1stIsParent = isParentCompany(company1st);
  const company1stRating = getCompanyRating(company1st);
  const company1stRatingAdj = company1stIsParent ? '' : `, ${getCompanyRatingAdj(company1st)}`;

  const company2ndName = company2nd?.name;
  const company2ndIsParent = isParentCompany(company2nd);
  const company2ndRating = getCompanyRating(company2nd);
  const company2ndRatingAdj = company2ndIsParent ? '' : `, ${getCompanyRatingAdj(company2nd)}`;

  const pricingOptions = [];
  let initialOption = pricingApproachEnum.STANDALONE;

  if (isLoan) {
    if (company2nd?.creditRating?.ratingAdj) {
      pricingOptions.push({
        label: `Implicit support adjusted rating (${company2ndName}${company2ndRatingAdj})`,
        tooltip: loanTooltips.implicitSupportApproach,
        value: pricingApproachEnum.IMPLICIT,
      });
      initialOption = pricingApproachEnum.IMPLICIT;
    }
    pricingOptions.push({
      label: `Stand-alone rating (${company2ndName}, ${company2ndRating})`,
      tooltip: loanTooltips.standAloneApproach,
      value: pricingApproachEnum.STANDALONE,
    });
  } else {
    const principalHasImplicitRating = company2nd?.creditRating?.ratingAdj != null;
    const principalHasStandaloneRating = company2nd?.creditRating?.rating != null;
    const principalHasImplicitPoD = company2nd?.creditRating?.probabilityOfDefaultAdj != null;
    const principalHasStandalonePoD = company2nd?.creditRating?.probabilityOfDefault != null;

    if (principalHasStandalonePoD || (principalHasStandaloneRating && isSecurityMethodology)) {
      pricingOptions.push({
        label: `Stand-alone ratings (${company1stName}, ${company1stRating} \u2014 ${company2ndName}, ${company2ndRating})`,
        tooltip: guaranteeTooltips.standAloneApproach,
        value: pricingApproachEnum.STANDALONE,
      });
    }

    if (
      ((company1stIsParent || company1st?.creditRating?.ratingAdj) &&
        principalHasImplicitRating &&
        principalHasImplicitPoD) ||
      (principalHasImplicitRating && isSecurityMethodology)
    ) {
      pricingOptions.push({
        label: `Implicit support adjusted ratings (${company1stName}${company1stRatingAdj} \u2014 ${company2ndName}${company2ndRatingAdj})`,
        tooltip: guaranteeTooltips.implicitSupportApproach,
        value: pricingApproachEnum.IMPLICIT,
      });
      initialOption = pricingApproachEnum.IMPLICIT;
    }
  }

  return { pricingOptions, initialOption };
}
