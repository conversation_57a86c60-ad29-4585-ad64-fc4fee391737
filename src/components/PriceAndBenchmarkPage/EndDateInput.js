import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { END_DATE_INPUT_TYPE_DATE, reportEnum } from '~/enums/reports';
import { report, toggleEndDateInputType, updateEndDate, updateTenor } from '~/reducers/report.slice';
import { Box, Button, DateInput, FlexLayout, NumberInput } from '~/ui';

import { guaranteeTooltips, loanTooltips } from './PriceAndBenchmarkPage.utils';

function CalendarButton(props) {
  const dispatch = useDispatch();
  return (
    <Button
      iconLeft={props.icon}
      dataTestId="calendarButton"
      variant="gray"
      size="m2"
      onClick={() => dispatch(toggleEndDateInputType())}
      sx={{ mt: 'auto', height: 'input-height' }}
    />
  );
}

function EndDateInput() {
  const dispatch = useDispatch();
  const { issueDate, maturityDate, terminationDate, tenor, endDateInputType, reportType } = useSelector(report);
  const isLoan = [reportEnum.LOAN, reportEnum.BACK_TO_BACK_LOAN].includes(reportType);
  const label = isLoan ? 'Maturity Date' : 'Termination Date';
  const tooltip = isLoan ? loanTooltips.maturityDate : guaranteeTooltips.terminationDate;

  return endDateInputType === END_DATE_INPUT_TYPE_DATE ? (
    <FlexLayout sx={{ gridGap: '4', gridColumn: 'span 5' }}>
      <Box sx={{ flexGrow: '1' }} disabled={issueDate == null}>
        <DateInput
          label={label}
          dataTestId="endDateInput"
          minDate={issueDate || new Date()}
          tooltip={tooltip}
          value={maturityDate ?? terminationDate}
          width="fullWidth"
          onChange={(endDate) => dispatch(updateEndDate({ endDate }))}
        />
      </Box>
      <CalendarButton icon="calendarDark" />
    </FlexLayout>
  ) : (
    <FlexLayout sx={{ gridGap: '4', gridColumn: 'span 5' }}>
      <Box sx={{ flexGrow: '1' }} disabled={issueDate == null}>
        <NumberInput
          label="Tenor"
          dataTestId="tenorYearInput"
          allowNegatives={false}
          tooltip={loanTooltips.tenor}
          value={tenor?.years}
          width="fullWidth"
          unit="Yr"
          onChange={(years) => dispatch(updateTenor({ tenor: { ...tenor, years } }))}
        />
      </Box>
      <Box sx={{ flexGrow: '1', mt: 'auto' }} disabled={issueDate == null}>
        <NumberInput
          allowNegatives={false}
          value={tenor?.months}
          dataTestId="tenorMonthInput"
          width="fullWidth"
          unit="Mo"
          onChange={(months) => dispatch(updateTenor({ tenor: { ...tenor, months } }))}
        />
      </Box>
      <CalendarButton icon="calendarLight" />
    </FlexLayout>
  );
}

export default EndDateInput;
