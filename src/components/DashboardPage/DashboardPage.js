import { useContext, useEffect, useState } from 'react';
import { useHistory } from 'react-router';

import { getNextGuaranteePayments, getNextLoanPayments } from '~/api';
import { getNextPath, removeNextPath } from '~/auth';
import { ReportsCard, LoansTable, GuaranteesTable, CreditRatingsTable } from '~/components/Shared';
import { UserInfoContext } from '~/context/user';
import { reportEnum } from '~/enums';
import { useReports } from '~/hooks';
import { Card, FlexLayout, LoadingSpinner, PageLayout, Tabs } from '~/ui';
import { showErrorToast } from '~/ui/components/Toast';
import { genericTabSetter } from '~/utils/tabs';

import ExchangeRatesGraph from './ExchangeRatesChart';
import MapChart from './MapChart';
import UpcomingPaymentsTable from './UpcomingPaymentsTable';

function DashboardPage() {
  const history = useHistory();
  const [isLoading, guarantees, loans, creditRatings] = useReports({ limit: 3, isPortfolio: true });
  const [selectedTab, setSelectedTab] = useState(reportEnum.LOAN);
  const [nextLoanPayments, setNextLoanPayments] = useState();
  const [nextGuaranteePayments, setNextGuaranteePayments] = useState();
  const [nextPaymentSelectedTab, setNextPaymentSelectedTab] = useState(reportEnum.LOAN);
  const [recentlyPricesTabs, setRecentlyPricesTabs] = useState([]);
  const [nextPaymentTabs, setNextPaymentTabs] = useState([]);
  const { userInfo } = useContext(UserInfoContext);
  const { features } = userInfo;

  useEffect(() => {
    const _next = getNextPath();
    if (_next) {
      history.replace(_next);
      removeNextPath();
    }
  }, [history]);

  useEffect(() => {
    if (!features.payment) return;

    const nextPaymentsPromises = [undefined, undefined];
    if (features.loan) nextPaymentsPromises[0] = getNextLoanPayments();
    if (features.guarantee) nextPaymentsPromises[1] = getNextGuaranteePayments();

    Promise.all(nextPaymentsPromises)
      .then(([loanPayments, guaranteePayments]) => {
        setNextLoanPayments(loanPayments);
        setNextGuaranteePayments(guaranteePayments);
      })
      .catch(() => showErrorToast());
  }, [features.payment, features.loan, features.guarantee]);

  useEffect(() => {
    const recentlyPricesTabs = [
      { isEnabled: features.loan, label: 'Loans', value: reportEnum.LOAN },
      { isEnabled: features.guarantee, label: 'Guarantees', value: reportEnum.GUARANTEE },
      { isEnabled: features.creditRating, label: 'Credit Ratings', value: reportEnum.CREDIT_RATING },
    ];
    genericTabSetter(setRecentlyPricesTabs, setSelectedTab, recentlyPricesTabs);

    const nextPaymentTabs = [
      { isEnabled: features.loan && features.payment, label: 'Loans', value: reportEnum.LOAN },
      { isEnabled: features.guarantee && features.payment, label: 'Guarantees', value: reportEnum.GUARANTEE },
    ];
    genericTabSetter(setNextPaymentTabs, setNextPaymentSelectedTab, nextPaymentTabs);
  }, [features.loan, features.guarantee, features.creditRating, features.payment]);

  if (isLoading) return <LoadingSpinner />;

  return (
    <PageLayout title="Dashboard">
      <FlexLayout alignItems="center" space={6}>
        <Card p={6} sx={{ flexBasis: '55%', height: '100%' }}>
          <ExchangeRatesGraph />
        </Card>
        <Card flexGrow="1" p={6} sx={{ height: '100%' }}>
          <MapChart />
        </Card>
      </FlexLayout>
      <FlexLayout flexDirection="column" space={6}>
        <Tabs selectedTab={selectedTab} tabs={recentlyPricesTabs} onTabSelect={setSelectedTab} />
        {selectedTab === reportEnum.LOAN && (
          <ReportsCard title="Recently Priced Loans" table={<LoansTable data={loans} />} />
        )}
        {selectedTab === reportEnum.GUARANTEE && (
          <ReportsCard title="Recently Priced Guarantees" table={<GuaranteesTable data={guarantees} />} />
        )}
        {selectedTab === reportEnum.CREDIT_RATING && (
          <ReportsCard title="Recently Calculated Credit Ratings" table={<CreditRatingsTable data={creditRatings} />} />
        )}
      </FlexLayout>
      {features.payment && (
        <FlexLayout flexDirection="column" space={6}>
          <Tabs selectedTab={nextPaymentSelectedTab} tabs={nextPaymentTabs} onTabSelect={setNextPaymentSelectedTab} />
          {nextPaymentSelectedTab === reportEnum.LOAN && (
            <ReportsCard
              title="Upcoming Loan Interest"
              table={<UpcomingPaymentsTable data={nextLoanPayments} isLoan={true} />}
            />
          )}
          {nextPaymentSelectedTab === reportEnum.GUARANTEE && (
            <ReportsCard
              title="Upcoming Guarantee Interest"
              table={<UpcomingPaymentsTable data={nextGuaranteePayments} isLoan={false} />}
            />
          )}
        </FlexLayout>
      )}
    </PageLayout>
  );
}

export default DashboardPage;
