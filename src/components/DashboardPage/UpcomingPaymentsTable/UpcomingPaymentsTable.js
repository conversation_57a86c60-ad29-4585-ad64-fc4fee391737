import React, { useContext } from 'react';
import { useHistory } from 'react-router-dom';

import { UserInfoContext } from '~/context/user';
import { REPORT_TYPE, reportEnum } from '~/enums';
import { routesEnum } from '~/routes';
import { FlexLayout, Table } from '~/ui';

import { getColumnData, getColumns } from './UpcomingPaymentsTable.utils';

function UpcomingPaymentsTable({ data = [], isLoan }) {
  const history = useHistory();
  const { userInfo } = useContext(UserInfoContext);
  const reportType = isLoan ? reportEnum.LOAN : reportEnum.GUARANTEE;

  return (
    <FlexLayout flexDirection="column">
      <Table
        columns={getColumns(isLoan)}
        data={getColumnData(data, userInfo)}
        onItemClick={({ reportId }) => history.push(`${routesEnum.PORTFOLIO}/${reportId}?${REPORT_TYPE}=${reportType}`)}
      />
    </FlexLayout>
  );
}

export default UpcomingPaymentsTable;
