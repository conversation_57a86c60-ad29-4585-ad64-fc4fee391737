import { formatDateString } from '~/utils/dates';
import { displayNumber } from '~/utils/strings';

function getData(payment, userInfo) {
  const { paymentDueDate, loan, guarantee, isPaid, paidPayments, totalNumberOfPayments, paymentAmount } = payment;
  const report = loan || guarantee;

  return {
    type: report.type || 'Bullet',
    lenderName: report?.lender?.name,
    borrowerName: report?.borrower?.name,
    guarantorName: report?.guarantor?.name,
    principalName: report?.principal?.name,
    paymentAmount: paymentAmount
      ? `${report.currency} ${displayNumber(paymentAmount, userInfo.decimalPoint)}`
      : 'Unknown',
    paymentDueDate: formatDateString(paymentDueDate, userInfo.dateFormat),
    paymentsPaid: `${paidPayments} of ${totalNumberOfPayments}`,
    reportId: report.id,
    isPaid,
  };
}

export function getColumnData(data = [], userInfo) {
  return data.map((item) => getData(item, userInfo));
}

export function getColumns(isLoan) {
  const endSharedColumns = [
    { label: 'Interest amount', sortBy: 'paymentAmount', value: 'paymentAmount', justifyContent: 'flex-end' },
    { label: 'Next interest due', sortBy: 'paymentDueDate', value: 'paymentDueDate' },
    { label: 'Type', sortBy: 'type', value: 'type' },
    { label: 'Interest paid', sortBy: 'paymentsPaid', value: 'paymentsPaid' },
  ];

  if (isLoan) {
    return [
      { label: 'Lender', sortBy: 'lenderName', value: 'lenderName' },
      { label: 'Borrower', sortBy: 'borrowerName', value: 'borrowerName' },
      ...endSharedColumns,
    ];
  }

  return [
    { label: 'Guarantor', sortBy: 'guarantorName', value: 'guarantorName' },
    { label: 'Principal', sortBy: 'principalName', value: 'principalName' },
    ...endSharedColumns,
  ];
}
