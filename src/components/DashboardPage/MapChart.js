import React, { useState } from 'react';
import { ComposableMap, Geographies, Geography, ZoomableGroup } from 'react-simple-maps';
import ReactTooltip from 'react-tooltip';

import { useCompanies } from '~/hooks';
import { Box, FlexLayout, Text } from '~/ui';
import colors from '~/ui/theme/colors';

const geoUrl = 'https://cdn.jsdelivr.net/npm/world-atlas@2/countries-110m.json';

function Legend() {
  return (
    <FlexLayout
      flexDirection="column"
      bg="white"
      p={3}
      space={3}
      sx={{ border: 'border', borderRadius: 'm', bottom: 0, left: 0, position: 'absolute', zIndex: 'page' }}
    >
      <FlexLayout alignItems="center" space={2}>
        <Box bg="shakespeare" sx={{ borderRadius: 'round', height: '8px', width: '8px' }} />
        <Text color="deep-sapphire" variant="s-spaced" sx={{ lineHeight: 'initial' }}>
          Parent
        </Text>
      </FlexLayout>
      <FlexLayout alignItems="center" space={2}>
        <Box bg="deep-sapphire" sx={{ borderRadius: 'round', height: '8px', width: '8px' }} />
        <Text color="deep-sapphire" variant="s-spaced" sx={{ lineHeight: 'initial' }}>
          Subsidiary
        </Text>
      </FlexLayout>
    </FlexLayout>
  );
}

function Map({ countries, parentCountry, setTooltipContent }) {
  return (
    <ComposableMap data-tip="">
      <ZoomableGroup>
        <Geographies geography={geoUrl}>
          {({ geographies }) =>
            geographies.map((geo) => {
              const countryName = geo.properties.name;
              return (
                <Geography
                  fill={
                    countryName === parentCountry
                      ? colors['shakespeare']
                      : countries.includes(countryName)
                      ? colors['deep-sapphire']
                      : colors['link-water']
                  }
                  geography={geo}
                  key={geo.rsmKey}
                  stroke={colors['white']}
                  style={{
                    default: { outline: 'none' },
                    hover: { outline: 'none' },
                    pressed: { outline: 'none' },
                  }}
                  onMouseEnter={() => setTooltipContent(countryName)}
                  onMouseLeave={() => setTooltipContent('')}
                />
              );
            })
          }
        </Geographies>
      </ZoomableGroup>
    </ComposableMap>
  );
}

function MapChart() {
  const companies = useCompanies();
  const [tooltipContent, setTooltipContent] = useState('');

  const parentCompany = companies.find((company) => company.id === company.parentCompanyId);
  const nonParentCompanies = companies.filter((company) => company.id !== company.parentCompanyId);

  const parentCountry = parentCompany ? parentCompany.country : null;
  const countries = nonParentCompanies.map((company) => company.country);

  return (
    <FlexLayout
      flexGrow="1"
      sx={{
        maxHeight: '100%',
        maxWidth: '100%',
        position: 'relative',
        '& svg': {
          flexGrow: '1',
          maxHeight: '100%',
          maxWidth: '100%',
        },
      }}
      dataTestId="worldMapContainer"
    >
      <Legend />
      <Map countries={countries} parentCountry={parentCountry} setTooltipContent={setTooltipContent} />
      <ReactTooltip>{tooltipContent}</ReactTooltip>
    </FlexLayout>
  );
}

export default MapChart;
