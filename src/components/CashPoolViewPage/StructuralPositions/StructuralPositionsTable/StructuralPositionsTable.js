import React, { useContext } from 'react';

import { UserInfoContext } from '~/context/user';
import { FlexLayout, Table } from '~/ui';

import { columns, getStructuralPositionsData } from './StructuralPositionsTable.utils';

const StructuralPositionsTable = ({ isShowing, structuralPositions }) => {
  const { userInfo } = useContext(UserInfoContext);
  if (!isShowing) return null;

  return (
    <FlexLayout flexDirection="column">
      <Table columns={columns} data={getStructuralPositionsData(structuralPositions, userInfo)} />
    </FlexLayout>
  );
};

export default StructuralPositionsTable;
