import { formatDateString } from '~/utils/dates';

function getStructuralPositionData(structuralPosition, userInfo) {
  const { company, position, startDate, endDate, duration, positionStatus } = structuralPosition;

  return {
    company,
    position,
    startDate: formatDateString(startDate, userInfo.dateFormat, false),
    endDate: formatDateString(endDate, userInfo.dateFormat, false),
    duration,
    status: positionStatus,
  };
}

export function getStructuralPositionsData(data = [], userInfo) {
  return data.map((structuralPosition) => getStructuralPositionData(structuralPosition, userInfo));
}

export const columns = [
  { label: 'Company', sortBy: 'company', value: 'company' },
  { label: 'Position', sortBy: 'position', value: 'position' },
  { label: 'Start Date', sortBy: 'startDate', value: 'startDate' },
  { label: 'End Date', sortBy: 'endDate', value: 'endDate' },
  { label: 'Duration', sortBy: 'duration', value: 'duration' },
  { label: 'Status', sortBy: 'status', value: 'status' },
];
