import { isWithinInterval } from 'date-fns';
import { useContext, useRef } from 'react';
import { Line } from 'react-chartjs-2';
import { ChartOptions, ChartData } from 'chart.js';

import { UserInfoContext } from 'context/user';
import { Box, Text } from 'ui';
import { displayNumber2 } from 'utils/strings';
import { DATE_FNS_FORMATS } from 'enums/dates';

type StructuralPositionsChartPropsType = {
  isShowing: boolean;
  trails: any;
  structuralPositions: any;
};

const StructuralPositionsChart = ({ isShowing, trails, structuralPositions }: StructuralPositionsChartPropsType) => {
  const chartRef = useRef(null);
  const { userInfo } = useContext(UserInfoContext);
  const currency = trails?.topCurrencyAccount?.currency;

  if (!isShowing || !trails) return null;

  function formatYAxisData(tickValue: number | string) {
    const numberDisplayOptions = {
      decimalPoint: userInfo.decimalPoint,
      unit: '',
      minDig: 2,
      currency: currency,
    };
    return displayNumber2(tickValue, numberDisplayOptions);
  }

  const getLineGraphData = (): ChartData<'line'> => {
    const labels = [];

    for (const trail of trails.accountTrails) {
      labels.push(trail.date);
    }

    const structuralPositionsIntervals = [];

    // Get date intervals of all structural positions
    for (const structuralPosition of structuralPositions) {
      structuralPositionsIntervals.push({
        start: new Date(structuralPosition.startDate),
        end: new Date(structuralPosition.endDate),
      });
    }

    const datasets = [];
    const nonStructuralPositionData = [];
    const structuralPositionData = [];
    /**
     * Go through every account trail, i.e. every balance/date record and check if
     * record is in time period that was part of structural position or not.
     */
    for (const accountTrail of trails.accountTrails) {
      let dateFilled = false;
      /**
       * For loop because there could be multiple structural positions and
       * we need to check if date of current record is part of any of them
       */
      for (const structuralPosition of structuralPositionsIntervals) {
        if (isWithinInterval(new Date(accountTrail.date), structuralPosition)) {
          structuralPositionData.push(accountTrail.balance);
          dateFilled = true;
        }
      }
      /**
       * If current record was part of this structural position add null to non-structural positions data for this date,
       * otherwise add current record to non-structural positions data and null to structural positions data.
       */
      if (dateFilled) {
        nonStructuralPositionData.push(null);
      } else {
        nonStructuralPositionData.push(accountTrail.balance);
        structuralPositionData.push(null);
      }
    }
    datasets.push({
      label: 'Non-Structural Position',
      data: nonStructuralPositionData,
      borderColor: '#000000',
      backgroundColor: '#000000',
      normalized: true,
      tension: 0.4,
    });
    datasets.push({
      label: 'Structural Position',
      data: structuralPositionData,
      borderColor: '#FF5E03',
      backgroundColor: '#FF5E03',
      normalized: true,
      tension: 0.4,
    });

    return {
      labels,
      datasets,
    };
  };

  const getGraphOptions = (): ChartOptions<'line'> => ({
    maintainAspectRatio: false,
    plugins: { legend: { display: true, position: 'bottom' } },
    scales: {
      yAxes: {
        type: 'linear',
        ticks: {
          maxTicksLimit: 8,
          callback: formatYAxisData,
        },
      },

      xAxes: {
        type: 'timeseries',
        time: {
          unit: 'day',
          displayFormats: {
            day: DATE_FNS_FORMATS[userInfo.dateFormat],
          },
          tooltipFormat: DATE_FNS_FORMATS[userInfo.dateFormat],
        },
        ticks: {
          autoSkip: true,
          autoSkipPadding: 50,
          maxRotation: 0,
        },
      },
    },
  });

  const lineGraphData = getLineGraphData();
  const graphOptions = getGraphOptions();

  if (lineGraphData.datasets.length === 0) {
    return (
      <Text color="bali-hai" variant="m-spaced">
        No entries.
      </Text>
    );
  }

  return (
    <Box sx={{ height: '300px' }}>
      <Line options={graphOptions} data={lineGraphData} ref={chartRef} />
    </Box>
  );
};

export default StructuralPositionsChart;
