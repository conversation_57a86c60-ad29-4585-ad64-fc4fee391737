import { formatInTimeZone } from 'date-fns-tz';
import { dateFnsFormatMapper, formatDateString } from '~/utils/dates';

function getSummary({ name, dateRange, user }) {
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const exportData = [
    ['Cash Pool', name],
    [
      'Date Range Exported',
      `${formatDateString(dateRange.startDate, user.dateFormat)} - ${formatDateString(
        dateRange.endDate,
        user.dateFormat
      )}`,
    ],
    ['Export Date', formatInTimeZone(new Date(), timezone, dateFnsFormatMapper(user.dateFormat))],
    ['Export Time', formatInTimeZone(new Date(), timezone, 'HH:mm:ss')],
    ['Exported By User', user.fullName],
  ];

  return exportData;
}

function getStructuralPositionsData({ structuralPositions, user }) {
  const sheetData = [];

  for (const position of structuralPositions) {
    let row = {};
    row['Company'] = position.company;
    row['Position'] = position.position;
    row['Start Date'] = formatDateString(position.startDate, user.dateFormat);
    row['End Date'] = formatDateString(position.endDate, user.dateFormat);
    row['Duration'] = position.duration;
    row['Status'] = position.positionStatus;

    sheetData.push(row);
  }

  return sheetData;
}

export async function getSheetData({ name, dateRange, user, structuralPositions }) {
  const summaryData = {
    sheetData: getSummary({ name, dateRange, user }),
    sheetName: 'Export Summary',
  };
  const structuralPositionsData = {
    sheetData: getStructuralPositionsData({ structuralPositions, user }),
    sheetName: 'Structural Positions',
  };

  return [summaryData, structuralPositionsData];
}
