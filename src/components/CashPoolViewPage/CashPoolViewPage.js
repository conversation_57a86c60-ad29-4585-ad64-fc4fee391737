import { useContext, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';
import 'chartjs-adapter-date-fns';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Tooltip,
  Legend,
  TimeSeriesScale,
} from 'chart.js';

import { deleteCashPool, getCashPool, getCashPoolParticipants, updateCashPoolNote } from '~/api';
import { DeleteModal } from '~/components/Modals';
import { NotesCard, ThreeDotActionMenu } from '~/components/Shared';
import { UserInfoContext } from '~/context/user';
import { cashPoolSelector, resetCashPool, setCashPool, updateField } from '~/reducers/cashPool.slice';
import { resetCashPoolData, updateField as updateFieldDataSlice } from '~/reducers/cashPoolData.slice';
import { resetStructuralPositionsData } from '~/reducers/cashPoolStructuralPositions.slice';
import { routesEnum } from '~/routes';
import { Card, FlexLayout, LoadingSpinner, PageLayout } from '~/ui';
import { showErrorToast, showToast } from '~/ui/components/Toast';
import { errorHandler } from '~/utils/errors';

import BatchesTable from './BatchesTable';
import CashPoolData from './CashPoolData/CashPoolData';
import CashPoolInfoCard from './CashPoolInfoCard';
import StructuralPositions from './StructuralPositions/StructuralPositions';
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, BarElement, Legend, Tooltip, TimeSeriesScale);

const CashPoolViewPage = () => {
  const history = useHistory();
  const dispatch = useDispatch();
  const { cashPoolId } = useParams();
  const { userInfo } = useContext(UserInfoContext);
  const cashPool = useSelector(cashPoolSelector);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const onNoteSave = () => {
    const data = { note: cashPool.note };
    updateCashPoolNote({ cashPoolId, data })
      .then(() => showToast('Note has been successfully updated.'))
      .catch(() => showErrorToast());
  };

  const onDeleteCashPool = () => {
    dispatch(resetCashPool());
    deleteCashPool(cashPoolId)
      .then(() => {
        showToast('Cash Pool deleted successfully');
        history.push(`${routesEnum.CASH_POOLS}`);
      })
      .catch(() => showErrorToast());
  };

  const getActionOptions = () => {
    return [
      {
        label: 'Edit',
        onClick: () => {
          dispatch(resetCashPool());
          history.push(`${routesEnum.CASH_POOLS}/${cashPoolId}/edit`);
        },
      },
      {
        label: 'Audit Trail',
        onClick: () => {
          dispatch(resetCashPool());
          history.push(`${routesEnum.CASH_POOLS}/${cashPoolId}/audit-trail`);
        },
      },
      {
        label: 'Statement Data',
        onClick: () => {
          dispatch(resetCashPool());
          history.push(`${routesEnum.CASH_POOLS}/${cashPoolId}/statement-data`);
        },
      },
       {
        label: 'Uploaded Files',
        onClick: () => {
          dispatch(resetCashPool());
          history.push(`${routesEnum.CASH_POOLS}/${cashPoolId}/uploaded-files`);
        },
      },
      {
        label: 'Delete',
        onClick: () => setShowDeleteModal(true),
      },
    ];
  };

  useEffect(() => {
    getCashPool({ cashPoolId })
      .then((cashPool) => {
        dispatch(setCashPool(cashPool));
        setIsLoading(false);
      })
      .catch((err) => {
        errorHandler(err);
        history.push(routesEnum.CASH_POOLS);
      });

    return () => dispatch(resetCashPool());
  }, [cashPoolId, history, dispatch]);

  useEffect(() => {
    getCashPoolParticipants({ cashPoolId }).then((participants) => {
      dispatch(updateFieldDataSlice({ allParticipants: participants }));
    });

    return () => {
      dispatch(resetCashPoolData());
      dispatch(resetStructuralPositionsData());
    };
  }, [dispatch, cashPoolId]);

  if (isLoading) return <LoadingSpinner />;

  return (
    <PageLayout
      title={cashPool.name}
      rightTitleContent={<ThreeDotActionMenu dataTestId="cashPoolViewActionMenu" options={getActionOptions()} />}
    >
      <Card pb={3} pt={6}>
        <CashPoolInfoCard cashPool={cashPool} userInfo={userInfo} />
        <FlexLayout flexDirection="column">
          <BatchesTable />
        </FlexLayout>
      </Card>
      <CashPoolData />
      <StructuralPositions />
      <NotesCard
        description="Include any notes on the cash pool here."
        note={cashPool.note}
        onChange={(note) => dispatch(updateField({ note }))}
        onSave={onNoteSave}
      />
      {showDeleteModal && (
        <DeleteModal
          item="Cash Pool"
          dataTestId="confirmDeleteCashPoolButton"
          handleOnDeleteClick={onDeleteCashPool}
          handleOnHide={() => setShowDeleteModal(false)}
        />
      )}
    </PageLayout>
  );
};

export default CashPoolViewPage;
