import { useContext } from 'react';

import { UserInfoContext } from 'context/user';

import ImportTemplateButton from './ImportTemplateButton';
import CreateBatch from './CreateBatch';
import { FlexLayout } from '../../../../ui';

type BatchesCreateButtonPropsType = {
  setBatches: React.Dispatch<React.SetStateAction<undefined>>;
};

const BatchesCreateButton = ({ setBatches }: BatchesCreateButtonPropsType) => {
  const { userInfo } = useContext(UserInfoContext);

  if (userInfo.features.isTemplateCashPoolBatchUpload) {
    return (
      <FlexLayout alignItems="center" space={2}>
        <ImportTemplateButton setBatches={setBatches} />
        <CreateBatch setBatches={setBatches} />
      </FlexLayout>
    );
  }

  return <CreateBatch setBatches={setBatches} />;
};

export default BatchesCreateButton;
