import { useState } from 'react';
import { useParams } from 'react-router-dom';

import { getCashPoolBatches, getCashPoolStatementData, uploadStatementDataFile } from 'api';
import { ConfirmModal } from 'components/Modals';
import { parse } from 'date-fns';
import { Button, FileInput, FlexLayout } from 'ui';
import { showToast } from 'ui/components/Toast';
import { mapHeaderToValue, sheetToJson } from 'utils/documents';
import { errorHandler } from 'utils/errors';

type ImportButtonPropsType = {
  setBatches: React.Dispatch<React.SetStateAction<undefined>>;
};

const ImportButton = ({ setBatches }: ImportButtonPropsType) => {
  const [isUploading, setIsUploading] = useState(false);
  const { cashPoolId } = useParams<{ cashPoolId: string }>();
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);

  const onUploadTemplateClick = async (file: File) => {
    setIsUploading(true);
    try {
      const sheet = await sheetToJson(await file.arrayBuffer(), 1);
      const headers = sheet[0];
      sheet.shift(); // remove header row
      const fileData = mapHeaderToValue(headers as Array<string>, sheet);
      const startDate = parse(String(fileData[0]?.valueDate), 'dd/MM/yyyy', new Date());
      const endDate = parse(String(fileData[fileData.length - 1]?.valueDate), 'dd/MM/yyyy', new Date());

      const statementData = await getCashPoolStatementData({
        cashPoolId,
        dateRange: { startDate, endDate },
        createdDate: null,
        source: null,
        isUsed: null,
        statementDate: null,
        companyId: null,
        offset: 0,
        limit: 1,
      });

      if (statementData.count > 0) {
        setIsConfirmationModalOpen(true);
        setUploadedFile(file);
        setIsUploading(false);
        return;
      }

      await createStatements(file);
    } catch (err) {
      errorHandler(err);
    } finally {
      setIsUploading(false);
    }
  };

  const onModalConfirmClick = async () => {
    setIsUploading(true);
    try {
      await createStatements(uploadedFile);
      setIsConfirmationModalOpen(false);
    } catch (err) {
      errorHandler(err);
    } finally {
      setIsUploading(false);
    }
  };

  const createStatements = async (file: File | null) => {
    if (!file) errorHandler('Something went wrong');
    await uploadStatementDataFile({ cashPoolId, file });
    await getCashPoolBatches({ cashPoolId }).then(setBatches);
    showToast('Statement data successfully uploaded.');
  };

  return (
    <>
      <FlexLayout alignItems="center" space={6}>
        <FileInput accept=".xls, .xlsx" sx={{ alignSelf: 'center' }} onChange={onUploadTemplateClick}>
          <Button
            iconLeft="upload"
            loading={isUploading}
            size="s"
            text="Upload template"
            variant="secondary"
            onClick={() => {}}
          />
        </FileInput>
      </FlexLayout>
      <ConfirmModal
        buttonText="Proceed"
        isShowing={isConfirmationModalOpen}
        onClick={onModalConfirmClick}
        onHide={() => setIsConfirmationModalOpen(false)}
        isButtonDisabled={isUploading}
        additionalInfo="Data already exists in the statement data section for the date range covered by this file. Please review the contents carefully to avoid creating duplicate transactions.
        Note: In no case can data be uploaded that falls within a range of an already performed interest calculation batch."
        title="Potential Duplicate Data Detected"
        modalWidth="s"
      />
    </>
  );
};

export default ImportButton;
