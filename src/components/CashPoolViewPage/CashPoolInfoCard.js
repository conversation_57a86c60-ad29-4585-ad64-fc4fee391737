import { NORDIC, NOTIONAL, PHYSICAL } from '~/enums';
import { FlexLayout, Text } from '~/ui';
import { formatDateString } from '~/utils/dates';
import { displayNumber2 } from '~/utils/strings';

const CashPoolInfoCard = ({ cashPool, userInfo }) => {
  const isPhysical = cashPool.type === PHYSICAL;
  const isNotional = cashPool.type === NOTIONAL;
  const isNordic = cashPool.type === NORDIC;
  const isFixed = cashPool.interestType === 'fixed';
  const unit = isFixed ? '%' : ' bps';
  const benefitNumberDisplayOptions = {
    decimalPoint: userInfo.decimalPoint,
    defaultValue: '-',
    currency: cashPool.currencies,
  };

  const getCashPoolCurrencyInFirstPlace = (currencies, cashPoolCurrency) => {
    const currenciesWithoutCashPoolCurrency = [...new Set(currencies)].filter((c) => c !== cashPoolCurrency);
    currenciesWithoutCashPoolCurrency.unshift(cashPoolCurrency);
    return currenciesWithoutCashPoolCurrency.join(', ');
  };

  const getCurrencies = () => {
    const cashPoolCurrency = cashPool.currencies;

    if (isPhysical) return cashPoolCurrency;
    if (isNotional) {
      const accountCurrencies = cashPool.topCurrencyAccounts[0].accounts.map(({ currency }) => currency);
      return getCashPoolCurrencyInFirstPlace(accountCurrencies, cashPoolCurrency);
    }
    if (isNordic) {
      const topAccountsCurrencies = cashPool.topCurrencyAccounts.map(({ currency }) => currency);
      return getCashPoolCurrencyInFirstPlace(topAccountsCurrencies, cashPoolCurrency);
    }
  };

  const getFormattedRates = (rate) => {
    if (isFixed) return `${rate}${unit}`;

    if (rate >= 0) return `${cashPool.overnightRate} + ${rate}${unit}`;
    return `${cashPool.overnightRate} - ${Math.abs(rate)}${unit}`;
  };

  return (
    <FlexLayout p={4} space={8} bg="alabaster" sx={{ border: 'border', borderRadius: 'm', borderColor: 'link-water' }}>
      <FlexLayout flexDirection="column" space={3}>
        <FlexLayout space={2} alignItems="center">
          <Text color="bali-hai" variant="s-spaced-medium">
            TYPE:
          </Text>
          <Text color="deep-sapphire">{cashPool.type}</Text>
        </FlexLayout>
        <FlexLayout space={2} alignItems="center">
          <Text color="bali-hai" variant="s-spaced-medium">
            COUNTRY:
          </Text>
          <Text color="deep-sapphire">{cashPool.country}</Text>
        </FlexLayout>
        <FlexLayout space={2} alignItems="center">
          <Text color="bali-hai" variant="s-spaced-medium">
            RISK ASSESSMENT:
          </Text>
          <Text color="deep-sapphire">{cashPool.assessment || 'n/a'}</Text>
        </FlexLayout>
      </FlexLayout>
      <FlexLayout sx={{ border: 'border', borderLeft: 0, borderColor: 'link-water' }} />
      <FlexLayout flexDirection="column" space={3}>
        <FlexLayout space={2} alignItems="center">
          <Text color="bali-hai" variant="s-spaced-medium">
            {isPhysical ? 'CURRENCY:' : 'CURRENCIES:'}
          </Text>
          <Text color="deep-sapphire">{getCurrencies()}</Text>
        </FlexLayout>
        <FlexLayout space={2} alignItems="center">
          <Text color="bali-hai" variant="s-spaced-medium">
            LEADER:
          </Text>
          <Text color="deep-sapphire">{cashPool.leader?.name}</Text>
        </FlexLayout>
        <FlexLayout space={2} alignItems="center">
          <Text color="bali-hai" variant="s-spaced-medium">
            UPDATED:
          </Text>
          <Text color="deep-sapphire">{formatDateString(cashPool.updatedAt, userInfo.dateFormat, true)}</Text>
        </FlexLayout>
      </FlexLayout>
      {!isNordic && (
        <>
          <FlexLayout sx={{ border: 'border', borderLeft: 0, borderColor: 'link-water' }} />
          <FlexLayout flexDirection="column" space={3}>
            <FlexLayout space={2} alignItems="center">
              <Text color="bali-hai" variant="s-spaced-medium">
                POOL CREDIT {isFixed ? 'RATE' : 'SPREAD'}:
              </Text>
              <Text color="deep-sapphire">{getFormattedRates(cashPool.creditInterestRate)}</Text>
            </FlexLayout>
            <FlexLayout space={2} alignItems="center">
              <Text color="bali-hai" variant="s-spaced-medium">
                POOL DEBIT {isFixed ? 'RATE' : 'SPREAD'}:
              </Text>
              <Text color="deep-sapphire">{getFormattedRates(cashPool.debitInterestRate)}</Text>
            </FlexLayout>
          </FlexLayout>
        </>
      )}
      <FlexLayout sx={{ border: 'border', borderLeft: 0, borderColor: 'link-water' }} />
      {isPhysical && (
        <FlexLayout flexDirection="column" space={3}>
          <FlexLayout space={2} alignItems="center">
            <Text color="bali-hai" variant="s-spaced-medium">
              GROSS BENEFIT:
            </Text>
            <Text color="deep-sapphire">{displayNumber2(cashPool.grossBenefit, benefitNumberDisplayOptions)}</Text>
          </FlexLayout>
          <FlexLayout space={2} alignItems="center">
            <Text color="bali-hai" variant="s-spaced-medium">
              LEADER BENEFIT:
            </Text>
            <Text color="deep-sapphire">
              {displayNumber2(cashPool.leaderTotalBenefit, benefitNumberDisplayOptions)}
            </Text>
          </FlexLayout>
          <FlexLayout space={2} alignItems="center">
            <Text color="bali-hai" variant="s-spaced-medium">
              PARTICIPANTS' BENEFIT:
            </Text>
            <Text color="deep-sapphire">
              {displayNumber2(cashPool.participantsTotalBenefit, benefitNumberDisplayOptions)}
            </Text>
          </FlexLayout>
        </FlexLayout>
      )}
    </FlexLayout>
  );
};

export default CashPoolInfoCard;
