import { useContext, useRef } from 'react';
import { Line } from 'react-chartjs-2';
import { useSelector } from 'react-redux';

import { UserInfoContext } from '~/context/user';
import { NORDIC } from '~/enums';
import { cashPoolSelector } from '~/reducers/cashPool.slice';
import { cashPoolDataSelector } from '~/reducers/cashPoolData.slice';
import { Box, Text } from '~/ui';
import { chartUtils } from '~/utils';
import { formatDateString } from '~/utils/dates';
import { displayNumber2 } from '~/utils/strings';

// used for attaching unit and to multiply to get percentages
const dataWithUnit = [
  'creditInterestRate',
  'debitInterestRate',
  'adjustedCreditInterestRate',
  'adjustedDebitInterestRate',
];
// used for attaching currency
const dataWithCurrency = ['balance', 'adjustedCreditInterestReceived', 'adjustedDebitInterestPaid'];

const CashPoolCharts = ({ isShowing }) => {
  const { type } = useSelector(cashPoolSelector);
  const { chosenCompanyIds, participantsTrails, selectedData, leaderBenefit } = useSelector(cashPoolDataSelector);
  const chartRef = useRef(null);
  const { userInfo } = useContext(UserInfoContext);
  const interestType = participantsTrails[0]?.topCurrencyAccount?.interestType;
  const currency = participantsTrails[0]?.topCurrencyAccount?.currency;

  const getLabel = (trail) => {
    if (type === NORDIC) {
      return `${trail.participant.company.name} (${trail.topCurrencyAccount.name})`;
    }
    return trail.participant.company.name;
  };

  const formatYAxisData = (number) => {
    const unit = interestType === 'fixed' ? '%' : ' bps';

    const unitToShow = dataWithUnit.includes(selectedData) ? unit : '';
    const currencyToShow = dataWithCurrency.includes(selectedData) ? currency : '';

    const numberDisplayOptions = {
      decimalPoint: userInfo.decimalPoint,
      unit: unitToShow,
      minDig: 2,
      currency: currencyToShow,
    };
    return displayNumber2(number, numberDisplayOptions);
  };

  const getLineGraphData = () => {
    const chartColorsLength = chartUtils.chartColors.length;
    const labels = [];

    if (selectedData === 'leaderBenefit') {
      return {
        labels: leaderBenefit.map(({ date }) => formatDateString(date, userInfo.dateFormat)),
        datasets: [
          {
            label: 'Leader Benefit',
            data: leaderBenefit.map(({ leaderBenefit }) => leaderBenefit),
            borderColor: chartUtils.chartColors[0],
            backgroundColor: chartUtils.chartColors[0],
            tension: 0.4,
          },
        ],
      };
    }

    // doesn't matter which participant we choose for labels because they should all have the same amount of trails
    if (participantsTrails[0]?.accountTrails) {
      for (const trail of participantsTrails[0]?.accountTrails) {
        labels.push(formatDateString(trail.date, userInfo.dateFormat));
      }
    }

    const datasets = [];
    for (let i = 0, len = participantsTrails.length; i < len; i++) {
      const trail = participantsTrails[i];
      if (chosenCompanyIds?.includes(trail.participant.company.id)) {
        const label = getLabel(trail);
        datasets.push({
          label,
          data: trail.accountTrails.map((trail) => {
            if (dataWithUnit.includes(selectedData) && trail[selectedData] != null) {
              // rates are decimal numbers so they are multiplied to be shown as percentages/bps
              return trail[selectedData] * (interestType === 'fixed' ? 100 : 10000);
            }
            return trail[selectedData];
          }),
          borderColor: chartUtils.chartColors[i % chartColorsLength],
          backgroundColor: chartUtils.chartColors[i % chartColorsLength],
          tension: 0.4,
        });
      }
    }

    return { labels, datasets };
  };

  const getGraphOptions = () => ({
    maintainAspectRatio: false,
    plugins: { legend: { display: true, position: 'bottom' } },
    scales: {
      yAxes: {
        id: 'y-axis-linear',
        type: 'linear',
        ticks: { maxTicksLimit: 8, callback: formatYAxisData },
      },
      xAxis: {
        type: 'category',
        // time: {
        //   unit: 'day',
        //   round: 'day',
        //   displayFormats: { day: DATE_FNS_FORMATS[userInfo.dateFormat] },
        //   tooltipFormat: DATE_FNS_FORMATS[userInfo.dateFormat],
        // },
        ticks: { autoSkip: true, maxTicksLimit: 8, autoSkipPadding: 50, maxRotation: 0 },
      },
    },
    tooltips: {
      callbacks: {
        label: (tooltipItem, data) => {
          const { label = '' } = data.datasets[tooltipItem.datasetIndex];
          const numberDisplayOptions = { decimalPoint: userInfo.decimalPoint, defaultValue: '-' };
          return `${label}: ${displayNumber2(tooltipItem.yLabel, numberDisplayOptions)}`;
        },
      },
    },
  });

  if (!isShowing) return null;

  const lineGraphData = getLineGraphData();
  const graphOptions = getGraphOptions();

  if (lineGraphData.datasets.length === 0) {
    return (
      <Text color="bali-hai" variant="m-spaced">
        No entries.
      </Text>
    );
  }

  return (
    <Box sx={{ height: '300px' }}>
      <Line options={graphOptions} data={lineGraphData} ref={chartRef} />
    </Box>
  );
};

export default CashPoolCharts;
