import { CHART, TABLE } from '~/enums';
import { Box, FlexLayout, Text } from '~/ui';

const CashPoolTableChartPicker = ({ selectedView, setSelectedView }) => {
  const isChartViewSelected = selectedView === CHART;

  const style = {
    border: 'border',
    borderRadius: 'm',
    padding: '6px 16px',
    textAlign: 'center',
  };
  const borderTable = isChartViewSelected ? {} : { border: 0 };
  const borderChart = isChartViewSelected ? { border: 0 } : {};

  return (
    <FlexLayout alignItems="flex-end">
      <Box>
        <FlexLayout>
          <Box
            bg={!isChartViewSelected ? 'shakespeare' : 'white'}
            sx={{ ...style, ...borderTable, borderTopRightRadius: 0, borderBottomRightRadius: 0, borderRight: 0 }}
            onClick={() => setSelectedView(TABLE)}
          >
            <Text variant="s-spaced-medium" color={!isChartViewSelected ? 'white' : 'bali-hai'}>
              TABLE
            </Text>
          </Box>
          <Box
            bg={isChartViewSelected ? 'shakespeare' : 'white'}
            sx={{ ...style, ...borderChart, borderTopLeftRadius: 0, borderBottomLeftRadius: 0, borderLeft: 0 }}
            onClick={() => setSelectedView(CHART)}
          >
            <Text variant="s-spaced-medium" color={isChartViewSelected ? 'white' : 'bali-hai'}>
              GRAPH
            </Text>
          </Box>
        </FlexLayout>
      </Box>
    </FlexLayout>
  );
};

export default CashPoolTableChartPicker;
