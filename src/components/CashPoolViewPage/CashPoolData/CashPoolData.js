import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';

import { getCashPoolParticipantTrails } from '~/api';
import { FilterCashPoolDataColumnsModal } from '~/components/Modals';
import {
  CompanyMultiSelect,
  CurrencySingleSelect,
  SimpleCompanySingleSelect,
  TopCurrencyAccountSingleSelect,
} from '~/components/Shared';
import { CHART, NORDIC, NOTIONAL } from '~/enums';
import { useCompanySelectOptions } from '~/hooks';
import { errorHandler } from '~/utils/errors';
import { cashPoolSelector } from '~/reducers/cashPool.slice';
import { cashPoolDataSelector, resetCompanyIds, updateDateRange, updateField } from '~/reducers/cashPoolData.slice';
import { Card, DateRangeInput, FlexLayout, Text } from '~/ui';

import { CashPoolCharts } from './CashPoolCharts';
import CashPoolDataButtons from './CashPoolDataButtons';
import CashPoolTableChartPicker from './CashPoolTableChartPicker';
import CashPoolTables from './CashPoolTables';
import DataSingleSelect from './Selects/DataSingleSelect';

const CashPoolData = () => {
  const dispatch = useDispatch();
  const { cashPoolId } = useParams();
  const cashPool = useSelector(cashPoolSelector);
  const {
    allParticipants,
    chosenCompanyId,
    chosenCompanyIds,
    chosenCurrency,
    chosenTopCurrencyAccountId,
    dateRange,
    selectedData,
    selectedView,
    isShowingColumnsFilterModal,
  } = useSelector(cashPoolDataSelector);
  const getCompanySelectOptions = useCompanySelectOptions(allParticipants, chosenTopCurrencyAccountId, cashPool.type);
  const isChartView = selectedView === CHART;

  const onTableChartPickerChange = (newSelectedView) => {
    if (newSelectedView === selectedView) return;

    dispatch(updateField({ selectedView: newSelectedView }));

    const { companyId: firstNonLeaderId } = allParticipants.find((participant) => !participant.isLeader);
    dispatch(resetCompanyIds({ chosenCompanyId, chosenCompanyIds, firstNonLeaderId }));
  };

  const setChosenCompany = (isMultiSelect) => (companyId) => {
    if (isMultiSelect) dispatch(updateField({ chosenCompanyIds: companyId }));
    else dispatch(updateField({ chosenCompanyId: companyId }));
  };

  useEffect(() => {
    if (!dateRange.startDate || !dateRange.endDate) return;

    /** In case of SimpleCompanySingleSelect, single id is cast to an array to work with the API */
    const companyIds = chosenCompanyId ? [chosenCompanyId] : chosenCompanyIds;

    const data = { companyIds, ...dateRange, topCurrencyAccountId: chosenTopCurrencyAccountId };
    getCashPoolParticipantTrails({ cashPoolId, data })
      .then(({ trails: participantsTrails, leaderBenefit }) =>
        dispatch(updateField({ participantsTrails, leaderBenefit }))
      )
      .catch(errorHandler);
  }, [
    dispatch,
    cashPoolId,
    chosenCompanyIds,
    chosenCompanyId,
    dateRange,
    chosenTopCurrencyAccountId,
    cashPool.cashPoolUpdateTrigger,
  ]);

  if (allParticipants.length === 0) return null;

  return (
    <Card p={4}>
      <FlexLayout justifyContent="space-between">
        <Text color="deep-sapphire" variant="xl-spaced-bold">
          Data View
        </Text>
        <CashPoolDataButtons />
      </FlexLayout>
      <FlexLayout justifyContent="space-between">
        <FlexLayout space={3} justifyContent="center" alignItems="center">
          <TopCurrencyAccountSingleSelect
            variant="short"
            width="m"
            height="input-height-small"
            value={chosenTopCurrencyAccountId}
            onChange={(chosenTopCurrencyAccountId) => dispatch(updateField({ chosenTopCurrencyAccountId }))}
            isShowing={cashPool.type === NORDIC}
          />
          {isChartView ? (
            <CompanyMultiSelect
              label="Company"
              height="input-height-small"
              options={getCompanySelectOptions()}
              value={chosenCompanyIds}
              onChange={setChosenCompany(true)}
              includeSelectAll
            />
          ) : (
            <SimpleCompanySingleSelect
              label="Company"
              height="input-height-small"
              options={getCompanySelectOptions()}
              value={chosenCompanyId}
              onChange={setChosenCompany(false)}
            />
          )}
          <DateRangeInput
            dateRange={dateRange}
            onChange={(range) => dispatch(updateDateRange(range))}
            label="Date Range"
            height="input-height-small"
          />
          {cashPool.type === NOTIONAL && (
            <CurrencySingleSelect
              variant="extendedShort"
              width="s"
              height="input-height-small"
              value={chosenCurrency}
              onChange={(chosenCurrency) => dispatch(updateField({ chosenCurrency }))}
            />
          )}
          <DataSingleSelect
            isShowing={isChartView}
            value={selectedData}
            onChange={(selectedData) => dispatch(updateField({ selectedData }))}
          />
        </FlexLayout>
        <CashPoolTableChartPicker selectedView={selectedView} setSelectedView={onTableChartPickerChange} />
      </FlexLayout>
      {(chosenCompanyIds?.length > 0 || chosenCompanyId) && (
        <>
          <CashPoolCharts isShowing={isChartView} />
          <CashPoolTables isShowing={!isChartView} />
        </>
      )}
      <FilterCashPoolDataColumnsModal
        isShowing={isShowingColumnsFilterModal}
        onHide={() => dispatch(updateField({ isShowingColumnsFilterModal: false }))}
      />
    </Card>
  );
};

export default CashPoolData;
