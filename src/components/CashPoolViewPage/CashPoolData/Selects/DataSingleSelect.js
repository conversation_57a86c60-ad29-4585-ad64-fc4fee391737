import { SingleSelect } from '~/ui';

/**
 * value is the name of the database column.
 * It's used to map fetched data for the chart in getLineGraphData.
 */
const options = [
  { label: 'Balance', value: 'balance' },
  { label: 'Credit Rate', value: 'creditInterestRate' },
  { label: 'Debit Rate', value: 'debitInterestRate' },
  { label: 'Credit Rate (adj.)', value: 'adjustedCreditInterestRate' },
  { label: 'Debit Rate (adj.)', value: 'adjustedDebitInterestRate' },
  { label: 'Interest Receivable', value: 'adjustedCreditInterestReceived' },
  { label: 'Interest Payable', value: 'adjustedDebitInterestPaid' },

  { label: 'Benefit', value: 'netInterestBenefit' },
  { label: 'Leader Benefit', value: 'leaderBenefit' },
];

function DataSingleSelect({
  isShowing = true,
  error,
  label = 'Data',
  tooltip,
  value,
  width = 'm',
  height = 'input-height-small',
  onChange,
}) {
  if (!isShowing) return null;

  return (
    <SingleSelect
      error={error}
      label={label}
      options={options}
      tooltip={tooltip}
      value={value}
      width={width}
      height={height}
      onChange={onChange}
    />
  );
}

export default DataSingleSelect;
