import { ButtonActionMenu } from 'components/Shared';
import { saveAs } from 'file-saver';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';

import { exportCashPoolAsExcel } from '~/api';
import { CHART, NORDIC } from '~/enums';
import { cashPoolSelector } from '~/reducers/cashPool.slice';
import { cashPoolDataSelector, resetCashPoolData, updateField } from '~/reducers/cashPoolData.slice';
import { Button, FlexLayout } from '~/ui';
import { showErrorToast, showToast } from '~/ui/components/Toast';
import { errorHandler } from '~/utils/errors';

const CashPoolDataButtons = () => {
  const dispatch = useDispatch();
  const { chosenTopCurrencyAccountId, dateRange, selectedView, chosenCompanyIds } = useSelector(cashPoolDataSelector);
  const { type } = useSelector(cashPoolSelector);
  const { cashPoolId } = useParams();
  const isChartView = selectedView === CHART;
  const isNordic = type === NORDIC;

  const handleOnExportTableClick = async (exportType) => {
    if (isNordic && !chosenTopCurrencyAccountId) {
      return showErrorToast('Select Top Currency Account for export.');
    }

    try {
      const data = {
        endDate: dateRange.endDate,
        startDate: dateRange.startDate,
        topCurrencyAccountId: chosenTopCurrencyAccountId,
        companyIds: chosenCompanyIds,
      };
      const file = await exportCashPoolAsExcel({ cashPoolId, type, data, exportType });
      saveAs(file, file.name);
      showToast('Sheet has been successfully exported.');
    } catch (err) {
      errorHandler(err);
    }
  };

  const exportOptions = [
    { label: 'Multi-sheet', onClick: () => handleOnExportTableClick('multi-sheet') },
    { label: 'Consolidated', onClick: () => handleOnExportTableClick('consolidated') },
  ];

  return (
    <FlexLayout space={2} alignItems="flex-end">
      {!isChartView && (
        <Button
          iconLeft="columns"
          size="s"
          text="Columns"
          variant="secondary"
          onClick={() => dispatch(updateField({ isShowingColumnsFilterModal: true }))}
        />
      )}
      {isChartView && <ButtonActionMenu buttonText="Export" options={exportOptions} />}

      <Button
        iconLeft="delete"
        text="Clear all"
        size="s"
        variant="secondary"
        onClick={() => dispatch(resetCashPoolData())}
      />
    </FlexLayout>
  );
};

export default CashPoolDataButtons;
