import { useContext } from 'react';
import { useSelector } from 'react-redux';

import { UserInfoContext } from '~/context/user';
import { cashPoolDataColumnsSelector } from '~/reducers/tableColumns/cashPoolDataColumns.slice';
import { cashPoolDataSelector } from '~/reducers/cashPoolData.slice';
import { FlexLayout, Table } from '~/ui';

import { getTrailsData, getVisibleTrailsColumns } from './TrailsTable.utils';

const TrailsTable = () => {
  const { participantsTrails, leaderBenefit } = useSelector(cashPoolDataSelector);
  const visibleCashPoolDataColumns = useSelector(cashPoolDataColumnsSelector);
  const { userInfo } = useContext(UserInfoContext);
  const accountTrails = participantsTrails[0]?.accountTrails;

  return (
    <FlexLayout flexDirection="column">
      <Table
        columns={getVisibleTrailsColumns(visibleCashPoolDataColumns)}
        data={getTrailsData(accountTrails, leaderBenefit, userInfo)}
      />
    </FlexLayout>
  );
};

export default TrailsTable;
