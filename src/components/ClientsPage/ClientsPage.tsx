import { useEffect, useState } from 'react';
import { useHistory, RouteComponentProps } from 'react-router-dom';

import { createClient, getClients, deleteClient } from 'api';
import { AddClientModal, DeleteModal } from 'components/Modals';
import { <PERSON>ton, Card, FlexLayout, PageLayout, Table } from 'ui';
import { showToast } from 'ui/components/Toast';

import { errorHandler } from 'utils/errors';
import type { ClientType } from 'types';
import type { Company } from 'types/database/Company.type';

import { columns, getClientsData, renderTableActionColumn } from './ClientsPage.utils';

const ClientsPage = () => {
  const history: RouteComponentProps['history'] = useHistory();
  const [clients, setClients] = useState<ClientType[]>([]);
  const [isAddClientModalShowing, setIsAddClientModalShowing] = useState(false);
  const [isDeleteModalShowing, setIsDeleteModalShowing] = useState<false | ClientType>(false);

  const onAddClient = async (
    name: string,
    emailDomains: string,
    industry: string,
    companiesToCreate?: Array<Company>
  ) => {
    try {
      const emailDomainsArray = emailDomains.split(',').map((e) => e.trim());
      const newClient = await createClient({ name, emailDomains: emailDomainsArray, industry, companiesToCreate });
      setClients([...clients, { ...newClient, numberOfUsers: 0 }]);
      showToast('Client created successfully');
    } catch (err) {
      errorHandler(err);
    }
    setIsAddClientModalShowing(false);
  };

  const onDeleteClient = () => {
    if (!isDeleteModalShowing) return;
    const clientToDelete = isDeleteModalShowing;

    deleteClient(clientToDelete.id)
      .then(() => {
        setClients(clients.filter((c) => c.id !== clientToDelete.id));
        setIsDeleteModalShowing(false);
      })
      .catch(errorHandler);
  };

  useEffect(() => {
    getClients().then(setClients);
  }, []);

  return (
    <PageLayout
      title="Clients"
      rightTitleContent={
        <FlexLayout alignItems="center" space={6}>
          <Button
            iconLeft="add"
            text="New client"
            size="s"
            variant="secondary"
            onClick={() => setIsAddClientModalShowing(true)}
          />
        </FlexLayout>
      }
    >
      <Card pb={3} pt={6}>
        <FlexLayout flexDirection="column">
          <Table
            isSearchable
            actionColumn={(item: ClientType) => renderTableActionColumn(item, setIsDeleteModalShowing, history)}
            columns={columns}
            data={getClientsData(clients)}
          />
        </FlexLayout>
      </Card>
      <AddClientModal
        isShowing={isAddClientModalShowing}
        onHide={() => setIsAddClientModalShowing(false)}
        onAddClient={onAddClient}
        clients={clients}
      />
      <DeleteModal
        item="client"
        handleOnDeleteClick={onDeleteClient}
        handleOnHide={() => setIsDeleteModalShowing(false)}
        isShowing={!!isDeleteModalShowing}
        additionalInfo={undefined}
        dataTestId={undefined}
      />
    </PageLayout>
  );
};

export default ClientsPage;
