import _ from 'lodash';
import React from 'react';
import { RouteComponentProps } from 'react-router-dom';

import { routesEnum } from 'routes';
import { ThreeDotActionMenu } from 'components/Shared';

import type { ClientType } from 'types';

const name = 'name';
const numberOfUsers = 'numberOfUsers';
const industry = 'industry';
const emailDomains = 'emailDomains';

const clientColumn = { label: 'Client', sortBy: 'client', value: name, width: 250 };
const industryColumn = { label: 'Industry', sortBy: industry, value: industry, width: 250 };
const numberOfUsersColumn = { label: 'Number of users', sortBy: numberOfUsers, value: numberOfUsers, width: 250 };
const domainColumn = { label: 'Email domains', sortBy: emailDomains, value: emailDomains, width: 250 };

export const columns = [clientColumn, industryColumn, numberOfUsersColumn, domainColumn];

export const getClientsData = (data: ClientType[]) => {
  return data.map((client: ClientType) => {
    return {
      ..._.pick(client, [name, industry, numberOfUsers, 'id']),
      emailDomains: client.emailDomains?.join(', ') || '-',
    };
  });
};

export const renderTableActionColumn = (
  item: ClientType,
  setIsDeleteModalShowing: React.Dispatch<React.SetStateAction<false | ClientType>>,
  history: RouteComponentProps['history']
) => {
  const ADMIN_CLIENT_ID = 1;
  const options = [];

  options.push({
    label: 'See features',
    onClick: () => history.push(`${routesEnum.CLIENTS}/${item.id}/client-features`),
  });

  if (item.id !== ADMIN_CLIENT_ID) {
    options.push({
      label: 'Delete',
      onClick: () => setIsDeleteModalShowing(item),
    });
  }

  return <ThreeDotActionMenu options={options} />;
};
