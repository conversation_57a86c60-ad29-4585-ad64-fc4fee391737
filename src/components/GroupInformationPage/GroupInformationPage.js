import { saveAs } from 'file-saver';
import { useContext, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { createCompanies, exportCompaniesAsExcel, deleteCompany, getCompanyTemplate } from '~/api';
import { ThreeDotActionMenu } from '~/components/Shared';
import { UserInfoContext, CountryContext } from '~/context';
import { isAdmin, NOTIFICATION_ACTIONS } from '~/enums';
import { useCompanies } from '~/hooks';
import { addCompanies, removeCompany } from '~/reducers/companies.slice';
import { setNotification } from '~/reducers/notifications.slice';
import { routesEnum } from '~/routes';
import { Box, Button, Card, FileInput, FlexLayout, LoadingSpinner, PageLayout, Table, Text, WithTooltip } from '~/ui';
import { showToast, showErrorToast } from '~/ui/components/Toast';
import { sheetToJson } from '~/utils/documents';
import { errorHand<PERSON>, fullResponseErrorHandler } from '~/utils/errors';

import DeleteCompanyModal from './DeleteCompanyModal';
import { getCompaniesColumns, getCompaniesData, getCompaniesFromSheet } from './GroupInformationPage.utils';

function ParentCompanyLabel() {
  return (
    <FlexLayout alignItems="center" space={2}>
      <Box bg="shakespeare" sx={{ flexShrink: '0', height: '8px', width: '8px', borderRadius: 'round' }} />
      <Text color="deep-sapphire" variant="m-spaced">
        Parent Company
      </Text>
    </FlexLayout>
  );
}

function GroupInformationPage() {
  const dispatch = useDispatch();
  const history = useHistory();
  const [selectedItem, setSelectedItem] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const { userInfo } = useContext(UserInfoContext);
  const { countriesByName } = useContext(CountryContext);

  const companies = useCompanies();
  const parentCompanyId = companies.find((company) => company.id === company.parentCompanyId)?.id ?? null;
  const isBulkUploadDisabled = !parentCompanyId;

  function onItemDelete() {
    deleteCompany(selectedItem.id)
      .then(() => {
        showToast(`Company "${selectedItem.name}" has been successfully deleted.`);
        dispatch(removeCompany(selectedItem));
        setShowDeleteModal(false);
        setSelectedItem(null);
      })
      .catch(errorHandler);
  }

  async function handleOnExportTableClick() {
    try {
      setIsExporting(true);
      const file = await exportCompaniesAsExcel();
      saveAs(file, file.name);
      showToast('Sheet has been successfully exported.');
    } catch (err) {
      fullResponseErrorHandler(err);
    } finally {
      setIsExporting(false);
    }
  }

  function handleOnDownloadTemplateClick() {
    getCompanyTemplate()
      .then((res) => {
        saveAs(res, 'group_information_template.xlsx');
        showToast('Template was successfully downloaded.');
      })
      .catch(() => showErrorToast());
  }

  async function handleOnUploadTemplateClick(document) {
    try {
      setIsUploading(true);
      if (!parentCompanyId) throw new Error('Parent company not found.');

      const numberOfInstructionRows = 5;
      const jsonSheet = await sheetToJson(await document.arrayBuffer(), null, numberOfInstructionRows);

      const newCompanies = await getCompaniesFromSheet(jsonSheet, countriesByName);

      if (newCompanies.length === 0) throw new Error('No companies found in the template.');

      const createdCompaniesResponse = await createCompanies(newCompanies);
      dispatch(addCompanies(createdCompaniesResponse));

      showToast('Companies have been successfully created.');
    } catch (err) {
      errorHandler(err);
    } finally {
      setIsUploading(false);
    }
  }

  function renderActionColumn(item) {
    const options = [];

    options.push({ label: 'Audit Trail', onClick: () => history.push(`/group-information/${item.id}/audit-trail`) });

    if (isAdmin(userInfo.role)) {
      options.push({
        label: 'Delete',
        onClick: () => {
          setShowDeleteModal(true);
          setSelectedItem(item);
        },
      });
    } else {
      options.push({
        label: 'Notify admin to delete company',
        onClick: () =>
          dispatch(
            setNotification({
              action: NOTIFICATION_ACTIONS.DELETE_COMPANY,
              title: 'Notify admin to delete company',
              id: item.id,
            })
          ),
      });
    }

    return <ThreeDotActionMenu options={options} />;
  }

  const isLoading = !companies;
  if (isLoading) return <LoadingSpinner />;

  return (
    <>
      <PageLayout
        title="Group Information"
        rightTitleContent={
          <FlexLayout alignItems="center" space={6}>
            <Button
              iconLeft="download"
              size="s"
              text="Download template"
              variant="secondary"
              onClick={handleOnDownloadTemplateClick}
            />
            <WithTooltip
              label="companyBulkUpload"
              tooltip="Before uploading the template it is necessary to create and assign<br /> a parent company using the New Company button."
              disabled={isBulkUploadDisabled}
            >
              <FileInput
                accept=".xls, .xlsx"
                sx={{ alignSelf: 'center' }}
                disabled={isBulkUploadDisabled}
                onChange={handleOnUploadTemplateClick}
              >
                <Button
                  iconLeft="upload"
                  disabled={isBulkUploadDisabled}
                  loading={isUploading}
                  size="s"
                  text="Upload template"
                  variant="secondary"
                />
              </FileInput>
            </WithTooltip>
            <Button
              iconLeft="export"
              text="Export"
              size="s"
              variant="secondary"
              loading={isExporting}
              onClick={handleOnExportTableClick}
            />
            <Button
              iconLeft="add"
              text="New Company"
              size="s"
              variant="secondary"
              onClick={() => history.push(routesEnum.GROUP_INFORMATION_NEW)}
            />
          </FlexLayout>
        }
      >
        <Card pb={3} pt={6} space={0}>
          <Table
            actionColumn={renderActionColumn}
            columns={getCompaniesColumns()}
            data={getCompaniesData(companies, countriesByName)}
            isSearchable
            rightTitleContent={<ParentCompanyLabel />}
            onItemClick={({ id }) => history.push(`/group-information/${id}`)}
          />
        </Card>
      </PageLayout>
      {showDeleteModal && (
        <DeleteCompanyModal
          selectedItem={selectedItem}
          handleOnDeleteClick={onItemDelete}
          handleOnHide={() => {
            setShowDeleteModal(false);
            setSelectedItem(null);
          }}
        />
      )}
    </>
  );
}
export default GroupInformationPage;
