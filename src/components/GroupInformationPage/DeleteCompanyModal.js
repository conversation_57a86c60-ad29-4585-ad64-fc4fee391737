import { Button, Modal, Text } from '~/ui';

function DeleteCompanyModal({ selectedItem, handleOnDeleteClick, handleOnHide }) {
  return (
    <Modal
      actionButtons={<Button text="Delete" onClick={handleOnDeleteClick} />}
      title="Delete company"
      width="s"
      onHide={handleOnHide}
    >
      <Text color="deep-sapphire" variant="m-spaced">
        Are you sure you want to delete{' '}
        <b style={{ wordWrap: 'break-word', overflowWrap: 'break-word', whiteSpace: 'normal' }}>
          "{`${selectedItem.name}`}"
        </b>{' '}
        company? You won't be able to undo this action.
      </Text>
    </Modal>
  );
}

export default DeleteCompanyModal;
