import React from 'react';
import { useParams, useHistory } from 'react-router-dom';

import { Table } from '~/ui';
import { ReportsCard } from '~/components/Shared';
import { getTopCurrencyAccountsColumns, getTopCurrencyAccountsData } from './AuditTrailTopCurrencyAccountsTable.utils';

const AuditTrailTopCurrencyAccountsTable = ({ topCurrencyAccounts, isNordic }) => {
  const { cashPoolId, auditTrailId } = useParams();
  const history = useHistory();

  if (!isNordic) return null;

  return (
    <ReportsCard
      title="Cash Pool Top Currency Accounts"
      table={
        <Table
          columns={getTopCurrencyAccountsColumns()}
          data={getTopCurrencyAccountsData(topCurrencyAccounts)}
          isSearchable
          onItemClick={({ id }) =>
            history.push(`/cash-pools/${cashPoolId}/audit-trail/${auditTrailId}/top-currency-account/${id}`)
          }
        />
      }
    />
  );
};

export default AuditTrailTopCurrencyAccountsTable;
