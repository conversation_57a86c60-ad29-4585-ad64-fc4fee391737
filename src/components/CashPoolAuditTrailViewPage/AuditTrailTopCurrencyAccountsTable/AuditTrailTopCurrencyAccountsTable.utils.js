function getTopCurrencyAccountData({ topCurrencyAccount }) {
  const { id, name, currency, participants } = topCurrencyAccount;

  return {
    id,
    name,
    currency,
    numberOfParticipants: participants?.length,
  };
}

export function getTopCurrencyAccountsData(data = []) {
  return data.map((topCurrencyAccount) => getTopCurrencyAccountData({ topCurrencyAccount }));
}

export function getTopCurrencyAccountsColumns() {
  return [
    { label: 'Currency Top Account Name', sortBy: 'name', value: 'name', width: 250, wrapText: true },
    { label: 'Currency', sortBy: 'currency', value: 'currency' },
    { label: 'Number of Participants', sortBy: 'numberOfParticipants', value: 'numberOfParticipants' },
  ];
}
