import { isAdmin, NOTIFICATION_ACTIONS } from '~/enums';
import { setNotification } from '~/reducers/notifications.slice';

export function getActionOptions(userInfo, setIsDeleteModalShowing, dispatch, auditTrailId) {
  const options = [];

  if (isAdmin(userInfo.role)) {
    options.push({ label: 'Delete', onClick: () => setIsDeleteModalShowing(true) });
  } else {
    options.push({
      label: 'Notify admin to delete the audit trail',
      onClick: () =>
        dispatch(
          setNotification({
            action: NOTIFICATION_ACTIONS.DELETE_AUDIT_TRAIL,
            title: 'Notify admin to delete the audit trail',
            id: auditTrailId,
          })
        ),
    });
  }

  return options;
}
