import React, { useContext, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import {
  deleteCashPoolAuditTrail,
  getCashPoolAuditTrail,
  getCashPool,
  getAuditTrailTopCurrencyAccountAll,
} from '~/api';
import { errorHandler } from '~/utils/errors';
import { useCompanies } from '~/hooks';
import { DeleteModal } from '~/components/Modals';
import {
  ThreeDotActionMenu,
  AuditTrailParticipantsTable,
  NordicFieldsCard,
  NordicPhysicalFunctionalAnalysisCard,
  NotionalFunctionalAnalysisCard,
  NotionalFieldsCard,
  PhysicalFieldsCard,
} from '~/components/Shared';
import { UserInfoContext } from '~/context/user';
import { NOTIONAL, NORDIC } from '~/enums';
import { cashPoolSelector, resetCashPool, setCashPool } from '~/reducers/cashPool.slice';
import { showToast, showErrorToast } from '~/ui/components/Toast';
import { Card, FlexLayout, LoadingSpinner, PageLayout } from '~/ui';
import AuditTrailTopCurrencyAccountsTable from './AuditTrailTopCurrencyAccountsTable/AuditTrailTopCurrencyAccountsTable';
import { getActionOptions } from './CashPoolAuditTrailViewPage.utils';

function CashPoolAuditTrailViewPage() {
  const dispatch = useDispatch();
  const history = useHistory();
  const data = useSelector(cashPoolSelector);
  const { cashPoolId, auditTrailId } = useParams();
  const [isDeleteModalShowing, setIsDeleteModalShowing] = useState(false);
  const { userInfo } = useContext(UserInfoContext);
  const [auditTrailParticipants, setAuditTrailParticipants] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [auditTrailTopCurrencyAccounts, setAuditTrailTopCurrencyAccounts] = useState([]);
  const isNotional = data.type === NOTIONAL;
  const isNordic = data.type === NORDIC;
  useCompanies();

  useEffect(() => {
    getCashPool({ cashPoolId })
      .then((cashPool) => dispatch(setCashPool(cashPool)))
      .catch(errorHandler);
  }, [dispatch, cashPoolId]);

  useEffect(() => {
    if (isNordic) {
      getAuditTrailTopCurrencyAccountAll({ cashPoolId, auditTrailId }).then((res) => {
        setAuditTrailTopCurrencyAccounts(res);
      });
    }
  }, [cashPoolId, auditTrailId, isNordic]);

  useEffect(() => {
    getCashPoolAuditTrail({ cashPoolId, auditTrailId })
      .then((res) => {
        setAuditTrailParticipants(res.participants);
        delete res.participants;
        dispatch(setCashPool(res));
        setIsLoading(false);
      })
      .catch(errorHandler);

    return () => {
      dispatch(resetCashPool());
    };
  }, [dispatch, cashPoolId, auditTrailId]);

  function onItemDelete() {
    deleteCashPoolAuditTrail({ cashPoolId, auditTrailId })
      .then(() => {
        history.push(`/cash-pools/${cashPoolId}/audit-trail`);
        setIsDeleteModalShowing(false);
        showToast('Audit trail has been successfully deleted.');
      })
      .catch(() => showErrorToast());
  }

  if (isLoading) return <LoadingSpinner />;

  return (
    <>
      <PageLayout
        title="Audit Trail"
        rightTitleContent={
          <ThreeDotActionMenu options={getActionOptions(userInfo, setIsDeleteModalShowing, dispatch, auditTrailId)} />
        }
      >
        <FlexLayout flexDirection="column">
          <Card pb={6} pt={6}>
            <PhysicalFieldsCard isAuditTrail={true} />
            <NordicFieldsCard isAuditTrail={true} />
            <NotionalFieldsCard isAuditTrail={true} />
          </Card>
        </FlexLayout>

        <NordicPhysicalFunctionalAnalysisCard isAuditTrail={true} />
        <NotionalFunctionalAnalysisCard isAuditTrail={true} />
        {!isNordic && <AuditTrailParticipantsTable participants={auditTrailParticipants} isNotional={isNotional} />}
        <AuditTrailTopCurrencyAccountsTable topCurrencyAccounts={auditTrailTopCurrencyAccounts} isNordic={isNordic} />
      </PageLayout>
      {isDeleteModalShowing && (
        <DeleteModal
          item="audit trail"
          handleOnDeleteClick={onItemDelete}
          handleOnHide={() => setIsDeleteModalShowing(false)}
        />
      )}
    </>
  );
}

export default CashPoolAuditTrailViewPage;
