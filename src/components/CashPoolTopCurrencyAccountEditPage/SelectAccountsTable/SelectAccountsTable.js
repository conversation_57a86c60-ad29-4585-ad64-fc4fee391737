import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { UpdateCirDirModal } from '~/components/Modals';
import { ReportsCard, SelectParticipantsTable } from '~/components/Shared';
import { useCompanies } from '~/hooks';
import { cashPoolTopCurrencyAccountSelector, updateField } from '~/reducers/cashPoolTopCurrencyAccount.slice';
import { Pagination } from '~/ui';

const SelectAccountsTable = () => {
  const dispatch = useDispatch();
  const companies = useCompanies();
  const [inTableParticipants, setInTableParticipants] = useState(); // combination of company and account data
  const [isCirDirModalShowing, setIsCirDirModalShowing] = useState(); // false or set to table row values of account
  const { accountsOffset, interestType, accounts } = useSelector(cashPoolTopCurrencyAccountSelector);
  const isFixed = interestType === 'fixed';
  const isPhysical = true;
  const offset = accountsOffset || 0;
  const limit = 10;

  const onPageChange = ({ selected }) => dispatch(updateField({ accountsOffset: selected }));

  useEffect(() => {
    const inTableParticipants = [];
    for (const company of companies) {
      const companyCopy = { ...company };
      const account = accounts.find((account) => account.companyId === companyCopy.id);
      if (account) {
        companyCopy.creditInterestRate = account.creditInterestRate;
        companyCopy.debitInterestRate = account.debitInterestRate;
        companyCopy.generateInterestStatementData = account.generateInterestStatementData;
        companyCopy.value = true; // used in getCompaniesSheetData to know which companies are selected (company.value ? 'Yes' : 'No';)
      }
      inTableParticipants.push(companyCopy);
    }
    setInTableParticipants(inTableParticipants);
  }, [accounts, companies]);

  return (
    <ReportsCard
      title="Select Top Currency Account Participants"
      table={
        <>
          <SelectParticipantsTable
            inTableParticipants={inTableParticipants}
            accounts={accounts}
            setIsCirDirModalShowing={setIsCirDirModalShowing}
            isFixed={isFixed}
            isPhysical={isPhysical}
            companies={companies}
            limit={limit}
            participantsOffset={offset}
          />
          <Pagination
            canNextPage={companies.length - offset * limit > offset * limit}
            canPreviousPage={offset !== 0}
            pageCount={companies.length / limit}
            forcePage={offset}
            onPageChange={onPageChange}
            isShowing={companies.length > limit}
          />
          <UpdateCirDirModal
            onHide={() => setIsCirDirModalShowing(false)}
            participant={isCirDirModalShowing}
            interestType={interestType}
          />
        </>
      }
    />
  );
};

export default SelectAccountsTable;
