import _ from 'lodash';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import { CurrencySingleSelect, OvernightRateSingleSelect } from '~/components/Shared';
import { addTopCurrencyAccount, cashPoolSelector, updateTopCurrencyAccount } from '~/reducers/cashPool.slice';
import {
  cashPoolTopCurrencyAccountSelector,
  resetCashPoolTopCurrencyAccount,
  setCashPoolTopCurrencyAccount,
  updateField,
} from '~/reducers/cashPoolTopCurrencyAccount.slice';
import { Box, Button, Card, FlexLayout, NumberInput, PageLayout, RadioGroup, TextInput } from '~/ui';
import { showToast } from '~/ui/components/Toast';
import { errorHandler } from '~/utils/errors';

import { getRequiredTopCurrencyAccountFields, getTexts, isValid } from './CashPoolTopCurrencyAccountEditPage.utils';
import SelectAccountsTable from './SelectAccountsTable';

const CashPoolTopCurrencyAccountEditPage = () => {
  const history = useHistory();
  const dispatch = useDispatch();
  const topCurrencyAccount = useSelector(cashPoolTopCurrencyAccountSelector);
  const cashPool = useSelector(cashPoolSelector);
  const { topCurrencyAccountId } = useParams();
  const { topCurrencyAccounts } = cashPool;
  const isEdit = history.location.pathname.includes('/edit');
  const isFixed = topCurrencyAccount.interestType === 'fixed';
  const texts = getTexts(isFixed);
  /** If it isn't an integer that means it's a temp string (topCurrencyAccountTempId), so all the fields are still editable. */
  const topCurrencyAccountIsInteger = Number.isInteger(Number(topCurrencyAccountId));

  const onSaveClick = () => {
    try {
      isValid({ topCurrencyAccount, cashPool, dispatch });
      if (isEdit) {
        dispatch(updateTopCurrencyAccount(topCurrencyAccount));
        showToast('Successfully updated Top Currency Account.');
      } else {
        /**
         * topCurrencyAccountTempId is used for editing a top currency account that hasn't been saved to the db.
         * For example when creating a new Nordic cash pool, user could "create" top currency account
         * and before creating the cash pool (and actually creating the top currency account) decide to
         * edit the not yet created top currency account, that's why the temporary id is necessary. Same goes for
         * updating the cash pool.
         */
        const topCurrencyAccountTempId = Math.random().toString(36).slice(4);
        dispatch(addTopCurrencyAccount({ ...topCurrencyAccount, topCurrencyAccountTempId }));
        showToast('Successfully created Top Currency Account.');
      }
      history.goBack();
      dispatch(resetCashPoolTopCurrencyAccount());
    } catch (err) {
      errorHandler(err);
    }
  };

  const isSaveDisabled = () => {
    const requiredFields = getRequiredTopCurrencyAccountFields(topCurrencyAccount);
    return !_.every(requiredFields, (field) => ![null, '', NaN].includes(field));
  };

  useEffect(() => {
    dispatch(resetCashPoolTopCurrencyAccount());
    if (isEdit) {
      if (topCurrencyAccounts.length === 0) return history.goBack();
      const topCurrencyAccount = topCurrencyAccounts.find((tca) =>
        [tca.id?.toString(), tca.topCurrencyAccountTempId].includes(topCurrencyAccountId)
      );
      dispatch(setCashPoolTopCurrencyAccount(topCurrencyAccount));
    }

    return () => dispatch(resetCashPoolTopCurrencyAccount());
  }, [dispatch, history, isEdit, topCurrencyAccountId, topCurrencyAccounts]);

  return (
    <PageLayout title={isEdit ? 'Edit Top Account' : 'New Top Account'}>
      <Card p={6}>
        <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
          <TextInput
            label="Currency Top Account Name"
            width="fullWidth"
            value={topCurrencyAccount.name}
            onChange={(name) => dispatch(updateField({ name }))}
          />
          <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
            <CurrencySingleSelect
              tooltip="Select the currency of the currency top account."
              width="fullWidth"
              disabled={isEdit && topCurrencyAccountIsInteger}
              variant="extended"
              value={topCurrencyAccount.currency}
              onChange={(currency) => dispatch(updateField({ currency }))}
            />
          </Box>
        </Box>
        <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
          <RadioGroup
            label="Interest Type"
            tooltip="Select the interest type applied to the top currency account by the bank when calculating interest."
            options={[
              { label: 'Fixed', value: 'fixed' },
              { label: 'Float', value: 'float' },
            ]}
            disabled={isEdit && topCurrencyAccountIsInteger}
            width="fullWidth"
            value={topCurrencyAccount.interestType}
            onChange={(interestType) => dispatch(updateField({ interestType, overnightRate: null }))}
          />
          {!isFixed && (
            <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
              <OvernightRateSingleSelect
                tooltip="Select the overnight rate applied to the top currency account by the bank in calculating interest."
                value={topCurrencyAccount.overnightRate}
                onChange={(overnightRate) => dispatch(updateField({ overnightRate }))}
              />
            </Box>
          )}
        </Box>
        <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(4, 1fr)' }}>
          <NumberInput
            inputType="float"
            allowNegatives={!isFixed}
            label={texts.labels.cir}
            tooltip={texts.tooltips.cir}
            width="fullWidth"
            unit={isFixed ? '%' : 'basis points'}
            value={topCurrencyAccount.creditInterestRate}
            error={topCurrencyAccount?.errors?.creditInterestRate}
            onChange={(creditInterestRate) => dispatch(updateField({ creditInterestRate }))}
          />
          <NumberInput
            inputType="float"
            allowNegatives={!isFixed}
            label={texts.labels.dir}
            tooltip={texts.tooltips.dir}
            width="fullWidth"
            unit={isFixed ? '%' : 'basis points'}
            value={topCurrencyAccount.debitInterestRate}
            onChange={(debitInterestRate) => dispatch(updateField({ debitInterestRate }))}
          />
        </Box>
      </Card>
      <SelectAccountsTable />
      <FlexLayout alignItems="flex-end" flexGrow="1" justifyContent="space-between">
        <Button variant="gray" text="Cancel" onClick={history.goBack} />
        <Button text="Save" iconRight="checkmark" disabled={isSaveDisabled()} onClick={onSaveClick} />
      </FlexLayout>
    </PageLayout>
  );
};

export default CashPoolTopCurrencyAccountEditPage;
