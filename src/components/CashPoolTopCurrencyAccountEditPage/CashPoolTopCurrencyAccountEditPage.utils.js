import _ from 'lodash';

import { isTopCurrencyAccountValid, updateAccounts, updateField } from '~/reducers/cashPoolTopCurrencyAccount.slice';

export const getRequiredTopCurrencyAccountFields = (topCurrencyAccount) =>
  _.pick(topCurrencyAccount, [
    'name',
    'currency',
    'creditInterestRate',
    'debitInterestRate',
    'interestType',
    topCurrencyAccount.interestType === 'float' && 'overnightRate',
  ]);

export const isValid = ({ topCurrencyAccount, cashPool, dispatch }) => {
  const isFixed = topCurrencyAccount.interestType === 'fixed';
  const word = isFixed ? 'rate' : 'spread';

  const errors = isTopCurrencyAccountValid({ topCurrencyAccount, cashPool, isTopCurrencyAccount: true });
  if (Object.keys(_.omit(errors, 'accounts')).length) {
    dispatch(updateField({ errors, showErrors: true }));
    throw new Error('Some fields are not valid.');
  }
  if (errors.accounts) {
    for (const account of errors.accounts) {
      dispatch(updateAccounts({ ...account }));
    }
    throw new Error(`One or more credit or debit interest ${word}s are not compliant. Check the participants table.`);
  }
};

export const getTexts = (isFixed) => {
  if (isFixed) {
    return {
      tooltips: {
        cir: 'Input the credit interest rate of the currency top account with the bank. This rate must be positive.',
        dir: 'Input the debit interest rate of the cash pool with the bank. This rate must be positive.',
      },
      labels: {
        cir: 'Credit Interest Rate',
        dir: 'Debit Interest Rate',
      },
    };
  }

  return {
    tooltips: {
      cir:
        'Input the credit interest spread of the cash pool with the bank.<br /> A negative value will be treated as a discount from the overnight rate,<br /> while a positive value will be treated as a premium to the overnight rate.',
      dir:
        'Input the debit interest spread of the cash pool with the bank.<br /> A negative value will be treated as a discount from the overnight rate,<br /> while a positive value will be treated as a premium to the overnight rate.',
    },
    labels: {
      cir: 'Credit Interest Spread',
      dir: 'Debit Interest Spread',
    },
  };
};
