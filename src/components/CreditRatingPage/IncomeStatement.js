import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { creditRating, updateAttribute, updateAttributeAndEvaluate } from '~/reducers/creditRating.slice';
import { Box, Card, FlexLayout, NumberInput, Text } from '~/ui';
import InputGroup from '~/ui/components/InputGroup/InputGroup';

import { tooltips } from './CreditRatingPage.utils';
import ExpandButton from './ExpandButton';
import LinkButton from './LinkButton';

function IncomeStatement() {
  const [isCollapsed, setIsCollapsed] = useState(true);
  const { errors, attributes, isTemplateUploaded } = useSelector(creditRating);
  const dispatch = useDispatch();

  useEffect(() => {
    if (isTemplateUploaded) setIsCollapsed(false);
  }, [isTemplateUploaded]);

  return (
    <Card
      py={6}
      title={
        <FlexLayout alignItems="center" justifyContent="space-between" space={6}>
          <FlexLayout space={4}>
            <Box>Income statement</Box>
            <ExpandButton isCollapsed={isCollapsed} setIsCollapsed={setIsCollapsed} />
          </FlexLayout>
          <Text color="bali-hai" variant="s-spaced-italic">
            Enter values in whole currency units <br /> Enter costs as positive values
          </Text>
        </FlexLayout>
      }
    >
      <FlexLayout flexDirection="column" space={6} px={4}>
        <InputGroup
          showTopInputOnly={isCollapsed}
          inputs={[
            {
              allowNegatives: false,
              error: errors?.operatingRevenueTurnover,
              label: 'Operating revenue/turnover*',
              level: 0,
              tooltip: tooltips.operatingRevenueTurnover,
              value: attributes?.operatingRevenueTurnover,
              onChange: (operatingRevenueTurnover) =>
                dispatch(updateAttributeAndEvaluate({ operatingRevenueTurnover })),
            },
            {
              allowNegatives: false,
              label: 'Sales**',
              level: 1,
              value: attributes?.sales,
              onChange: (sales) => dispatch(updateAttributeAndEvaluate({ sales })),
            },
          ]}
          variant="twoGroup"
        />
        <NumberInput
          allowNegatives={false}
          label="Cost of good sold"
          inputType="float"
          value={attributes?.costOfGoodSold}
          isShowing={!isCollapsed}
          width="l"
          onChange={(costOfGoodSold) => dispatch(updateAttributeAndEvaluate({ costOfGoodSold }))}
        />
        <NumberInput
          label="Gross profit**"
          inputType="float"
          tooltip={tooltips.grossProfit}
          value={attributes?.grossProfit}
          width="l"
          rightSideComponent={
            <LinkButton attribute="grossProfit" isLinked={!attributes?.overriddenStatus.grossProfit} />
          }
          isLocked={!attributes?.overriddenStatus.grossProfit}
          onChange={(grossProfit) => dispatch(updateAttribute({ grossProfit }))}
        />
        <NumberInput
          allowNegatives={false}
          label="Material costs"
          inputType="float"
          value={attributes?.materialCosts}
          isShowing={!isCollapsed}
          width="l"
          onChange={(materialCosts) => dispatch(updateAttributeAndEvaluate({ materialCosts }))}
        />
        <NumberInput
          allowNegatives={false}
          label="Cost of employees"
          inputType="float"
          value={attributes?.costOfEmployees}
          isShowing={!isCollapsed}
          width="l"
          onChange={(costOfEmployees) => dispatch(updateAttributeAndEvaluate({ costOfEmployees }))}
        />
        <NumberInput
          allowNegatives={false}
          label="Other operating expenses"
          inputType="float"
          value={attributes?.otherOperatingExpenses}
          isShowing={!isCollapsed}
          width="l"
          onChange={(otherOperatingExpenses) => dispatch(updateAttributeAndEvaluate({ otherOperatingExpenses }))}
        />
        <NumberInput
          allowNegatives={false}
          label="Depreciation"
          inputType="float"
          value={attributes?.depreciation}
          isShowing={!isCollapsed}
          width="l"
          onChange={(depreciation) => dispatch(updateAttributeAndEvaluate({ depreciation }))}
        />
        <NumberInput
          error={errors?.EBIT}
          label="EBIT*"
          inputType="float"
          tooltip={tooltips.EBIT}
          value={attributes?.EBIT}
          width="l"
          rightSideComponent={<LinkButton attribute="EBIT" isLinked={!attributes?.overriddenStatus.EBIT} />}
          isLocked={!attributes?.overriddenStatus.EBIT}
          onChange={(EBIT) => dispatch(updateAttribute({ EBIT, checkValid: true }))}
        />
        <NumberInput
          error={errors?.EBITDA}
          label="EBITDA*"
          inputType="float"
          tooltip={tooltips.EBITDA}
          value={attributes?.EBITDA}
          width="l"
          rightSideComponent={<LinkButton attribute="EBITDA" isLinked={!attributes?.overriddenStatus.EBITDA} />}
          isLocked={!attributes?.overriddenStatus.EBITDA}
          onChange={(EBITDA) => dispatch(updateAttributeAndEvaluate({ EBITDA }))}
        />
        <InputGroup
          showTopInputOnly={isCollapsed}
          isLinked={!attributes?.overriddenStatus.financialPL}
          inputs={[
            {
              error: errors?.financialPL,
              label: 'Financial P/L**',
              level: 0,
              tooltip: tooltips.financialPL,
              value: attributes?.financialPL,
              rightSideComponent: (
                <LinkButton attribute="financialPL" isLinked={!attributes?.overriddenStatus.financialPL} />
              ),
              isLocked: !attributes?.overriddenStatus.financialPL,
              onChange: (financialPL) => dispatch(updateAttribute({ financialPL, checkValid: true })),
            },
            {
              allowNegatives: false,
              label: 'Financial revenue**',
              level: 1,
              value: attributes?.financialRevenue,
              onChange: (financialRevenue) => dispatch(updateAttributeAndEvaluate({ financialRevenue })),
            },
            {
              renderInputGroup: () => (
                <InputGroup
                  key="OtherCurrentAssetsCashAndCashEquivalent"
                  inputs={[
                    {
                      allowNegatives: false,
                      label: 'Financial expenses**',
                      level: 1,
                      tooltip: tooltips.financialExpenses,
                      value: attributes?.financialExpenses,
                      onChange: (financialExpenses) => dispatch(updateAttributeAndEvaluate({ financialExpenses })),
                    },
                    {
                      allowNegatives: false,
                      label: 'Interest paid**',
                      error: errors?.interestPaid,
                      level: 2,
                      tooltip: tooltips.interestPaid,
                      value: attributes?.interestPaid,
                      onChange: (value) => dispatch(updateAttribute({ interestPaid: value, checkValid: true })),
                    },
                  ]}
                  variant="twoGroup"
                />
              ),
            },
            // {
            //   allowNegatives: false,
            //   label: 'Financial expenses**',
            //   level: 1,
            //   tooltip: tooltips.financialExpenses,
            //   value: attributes?.financialExpenses,
            //   onChange: (financialExpenses) => dispatch(updateAttributeAndEvaluate({ financialExpenses })),
            // },
          ]}
          variant="threeGroup"
        />
        <NumberInput
          error={errors?.PLBeforeTax}
          label="P/L before tax*"
          inputType="float"
          tooltip={tooltips.PLBeforeTax}
          value={attributes?.PLBeforeTax}
          width="l"
          rightSideComponent={
            <LinkButton attribute="PLBeforeTax" isLinked={!attributes?.overriddenStatus.PLBeforeTax} />
          }
          isLocked={!attributes?.overriddenStatus.PLBeforeTax}
          onChange={(PLBeforeTax) => dispatch(updateAttributeAndEvaluate({ PLBeforeTax }))}
        />
        <NumberInput
          label="Taxation"
          inputType="float"
          value={attributes?.taxation}
          isShowing={!isCollapsed}
          width="l"
          onChange={(taxation) => dispatch(updateAttributeAndEvaluate({ taxation }))}
        />
        <NumberInput
          error={errors?.PLAfterTax}
          label="P/L after tax"
          inputType="float"
          tooltip={tooltips.PLAfterTax}
          value={attributes?.PLAfterTax}
          isShowing={!isCollapsed}
          width="l"
          rightSideComponent={<LinkButton attribute="PLAfterTax" isLinked={!attributes?.overriddenStatus.PLAfterTax} />}
          isLocked={!attributes?.overriddenStatus.PLAfterTax}
          onChange={(PLAfterTax) => dispatch(updateAttributeAndEvaluate({ PLAfterTax }))}
        />
        <InputGroup
          isShowing={!isCollapsed}
          isLinked={!attributes?.overriddenStatus.extrAndOtherPL}
          inputs={[
            {
              label: 'Extraordinary and other P/L',
              level: 0,
              tooltip: tooltips.extrAndOtherPL,
              value: attributes?.extrAndOtherPL,
              rightSideComponent: (
                <LinkButton attribute="extrAndOtherPL" isLinked={!attributes?.overriddenStatus.extrAndOtherPL} />
              ),
              isLocked: !attributes?.overriddenStatus.extrAndOtherPL,
              onChange: (extrAndOtherPL) => dispatch(updateAttributeAndEvaluate({ extrAndOtherPL })),
            },
            {
              allowNegatives: false,
              label: 'Extraordinary and other revenue',
              level: 1,
              value: attributes?.extrAndOtherRevenue,
              onChange: (extrAndOtherRevenue) => dispatch(updateAttributeAndEvaluate({ extrAndOtherRevenue })),
            },
            {
              allowNegatives: false,
              label: 'Extraordinary and other expenses',
              level: 1,
              value: attributes?.extrAndOtherExpenses,
              onChange: (extrAndOtherExpenses) => dispatch(updateAttributeAndEvaluate({ extrAndOtherExpenses })),
            },
          ]}
          variant="threeGroup"
        />
        <NumberInput
          error={errors?.PLForPeriod}
          label="P/L for period*"
          inputType="float"
          tooltip={tooltips.PLForPeriod}
          value={attributes?.PLForPeriod}
          width="l"
          rightSideComponent={
            <LinkButton attribute="PLForPeriod" isLinked={!attributes?.overriddenStatus.PLForPeriod} />
          }
          isLocked={!attributes?.overriddenStatus.PLForPeriod}
          onChange={(value) => dispatch(updateAttribute({ PLForPeriod: value, checkValid: true }))}
        />
      </FlexLayout>
      <FlexLayout flexDirection="column" flexGrow="1" justifyContent="flex-end">
        <Text color="bali-hai" variant="s-spaced-italic">
          *Required field
        </Text>
        <Text color="bali-hai" variant="s-spaced-italic">
          **Important field but not required
        </Text>
      </FlexLayout>
    </Card>
  );
}

export default IncomeStatement;
