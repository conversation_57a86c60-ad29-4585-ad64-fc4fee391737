import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { creditRating, updateAttribute, updateAttributeAndEvaluate } from '~/reducers/creditRating.slice';
import { Box, Card, FlexLayout, NumberInput, Text } from '~/ui';
import InputGroup from '~/ui/components/InputGroup/InputGroup';

import ExpandButton from './ExpandButton';
import LinkButton from './LinkButton';
import { tooltips } from './CreditRatingPage.utils';

function BalanceSheet() {
  const [isCollapsed, setIsCollapsed] = useState(true);
  const { errors, attributes, isTemplateUploaded } = useSelector(creditRating);
  const dispatch = useDispatch();

  useEffect(() => {
    if (isTemplateUploaded) setIsCollapsed(false);
  }, [isTemplateUploaded]);

  return (
    <Card
      py={6}
      title={
        <FlexLayout alignItems="center" justifyContent="space-between" space={6}>
          <FlexLayout space={4}>
            <Box>Balance sheet</Box>
            <ExpandButton isCollapsed={isCollapsed} setIsCollapsed={setIsCollapsed} />
          </FlexLayout>
          <Text color="bali-hai" variant="s-spaced-italic">
            Enter values in whole currency units
          </Text>
        </FlexLayout>
      }
    >
      <FlexLayout flexDirection="column" space={6} px={4}>
        <InputGroup
          showTopInputOnly={isCollapsed}
          isLinked={!attributes?.overriddenStatus.fixedAssets}
          inputs={[
            {
              error: errors?.fixedAssets,
              label: 'Fixed assets*',
              level: 0,
              tooltip: tooltips.fixedAssets,
              value: attributes?.fixedAssets,
              rightSideComponent: (
                <LinkButton attribute="fixedAssets" isLinked={!attributes?.overriddenStatus.fixedAssets} />
              ),
              isLocked: !attributes?.overriddenStatus.fixedAssets,
              onChange: (value) => dispatch(updateAttributeAndEvaluate({ fixedAssets: value })),
            },
            {
              allowNegatives: false,
              label: 'Intangible fixed assets',
              level: 1,
              value: attributes?.intangibleFixedAssets,
              onChange: (value) => dispatch(updateAttributeAndEvaluate({ intangibleFixedAssets: value })),
            },
            {
              allowNegatives: false,
              label: 'Tangible fixed assets',
              level: 1,
              value: attributes?.tangibleFixedAssets,
              onChange: (value) => dispatch(updateAttributeAndEvaluate({ tangibleFixedAssets: value })),
            },
            {
              allowNegatives: false,
              label: 'Other fixed assets',
              level: 1,
              value: attributes?.otherFixedAssets,
              onChange: (value) => dispatch(updateAttributeAndEvaluate({ otherFixedAssets: value })),
            },
          ]}
          variant="fourGroup"
        />
        <InputGroup
          showTopInputOnly={isCollapsed}
          isLinked={!attributes?.overriddenStatus.currentAssets}
          inputs={[
            {
              error: errors?.currentAssets,
              label: 'Current assets*',
              level: 0,
              tooltip: tooltips.currentAssets,
              value: attributes?.currentAssets,
              rightSideComponent: (
                <LinkButton attribute="currentAssets" isLinked={!attributes?.overriddenStatus.currentAssets} />
              ),
              isLocked: !attributes?.overriddenStatus.currentAssets,
              onChange: (value) => dispatch(updateAttributeAndEvaluate({ currentAssets: value })),
            },
            {
              allowNegatives: false,
              error: errors?.stocks,
              label: 'Stocks**',
              level: 1,
              value: attributes?.stocks,
              onChange: (value) => dispatch(updateAttributeAndEvaluate({ stocks: value })),
            },
            {
              allowNegatives: false,
              error: errors?.debtors,
              label: 'Debtors**',
              level: 1,
              value: attributes?.debtors,
              onChange: (value) => dispatch(updateAttributeAndEvaluate({ debtors: value })),
            },
            {
              renderInputGroup: () => (
                <InputGroup
                  key="OtherCurrentAssetsCashAndCashEquivalent"
                  inputs={[
                    {
                      allowNegatives: false,
                      label: 'Other current assets',
                      level: 1,
                      tooltip: tooltips.otherCurrentAssets,
                      value: attributes?.otherCurrentAssets,
                      onChange: (value) => dispatch(updateAttributeAndEvaluate({ otherCurrentAssets: value })),
                    },
                    {
                      allowNegatives: false,
                      error: errors?.cashAndCashEquivalent,
                      label: 'Cash and cash equivalent',
                      level: 2,
                      tooltip: tooltips.cashAndCashEquivalent,
                      value: attributes?.cashAndCashEquivalent,
                      onChange: (value) =>
                        dispatch(updateAttribute({ cashAndCashEquivalent: value, checkValid: true })),
                    },
                  ]}
                  variant="twoGroup"
                />
              ),
            },
          ]}
          variant="fourGroup"
        />
        <NumberInput
          error={errors?.totalAssets}
          label="Total assets*"
          inputType="float"
          tooltip={tooltips.totalAssets}
          value={attributes?.totalAssets}
          width="l"
          rightSideComponent={
            <LinkButton attribute="totalAssets" isLinked={!attributes?.overriddenStatus.totalAssets} />
          }
          isLocked={!attributes?.overriddenStatus.totalAssets}
          onChange={(value) => dispatch(updateAttribute({ totalAssets: value, checkValid: true }))}
        />
        <InputGroup
          showTopInputOnly={isCollapsed}
          isLinked={!attributes?.overriddenStatus.shareholdersFunds}
          inputs={[
            {
              error: errors?.shareholdersFunds,
              label: 'Shareholder funds*',
              level: 0,
              tooltip: tooltips.shareholdersFunds,
              value: attributes?.shareholdersFunds,
              rightSideComponent: (
                <LinkButton attribute="shareholdersFunds" isLinked={!attributes?.overriddenStatus.shareholdersFunds} />
              ),
              isLocked: !attributes?.overriddenStatus.shareholdersFunds,
              onChange: (value) => dispatch(updateAttribute({ shareholdersFunds: value, checkValid: true })),
            },
            {
              label: 'Capital',
              level: 1,
              value: attributes?.capital,
              onChange: (value) => dispatch(updateAttributeAndEvaluate({ capital: value })),
            },
            {
              renderInputGroup: () => (
                <InputGroup
                  key="OtherShareholdersFundsTreasuryShares"
                  inputs={[
                    {
                      label: 'Other shareholders funds',
                      level: 1,
                      value: attributes?.otherShareholdersFunds,
                      onChange: (value) => dispatch(updateAttributeAndEvaluate({ otherShareholdersFunds: value })),
                    },
                    {
                      error: errors?.treasuryShares,
                      label: 'Treasury shares**',
                      level: 2,
                      tooltip: tooltips.treasuryShares,
                      value: attributes?.treasuryShares,
                      onChange: (value) => dispatch(updateAttribute({ treasuryShares: value, checkValid: true })),
                    },
                  ]}
                  variant="twoGroup"
                />
              ),
            },
          ]}
          variant="threeGroup"
        />
        <InputGroup
          showTopInputOnly={isCollapsed}
          isLinked={!attributes?.overriddenStatus.nonCurrentLiabilities}
          inputs={[
            {
              error: errors?.nonCurrentLiabilities,
              label: 'Non-current liabilities*',
              level: 0,
              tooltip: tooltips.nonCurrentLiabilities,
              value: attributes?.nonCurrentLiabilities,
              rightSideComponent: (
                <LinkButton
                  attribute="nonCurrentLiabilities"
                  isLinked={!attributes?.overriddenStatus.nonCurrentLiabilities}
                />
              ),
              isLocked: !attributes?.overriddenStatus.nonCurrentLiabilities,
              onChange: (value) => dispatch(updateAttributeAndEvaluate({ nonCurrentLiabilities: value })),
            },
            {
              allowNegatives: false,
              error: errors?.longTermDebt,
              label: 'Long-term debt**',
              level: 1,
              value: attributes?.longTermDebt,
              onChange: (value) => dispatch(updateAttributeAndEvaluate({ longTermDebt: value })),
            },
            {
              renderInputGroup: () => (
                <InputGroup
                  key="OtherShareholdersFundsTreasuryShares"
                  inputs={[
                    {
                      allowNegatives: false,
                      label: 'Other non-current liabilities',
                      level: 1,
                      value: attributes?.otherNonCurrentLiabilities,
                      onChange: (value) => dispatch(updateAttributeAndEvaluate({ otherNonCurrentLiabilities: value })),
                    },
                    {
                      allowNegatives: false,
                      error: errors?.provisions,
                      label: 'Provisions',
                      level: 2,
                      tooltip: tooltips.provisions,
                      value: attributes?.provisions,
                      onChange: (value) => dispatch(updateAttribute({ provisions: value, checkValid: true })),
                    },
                  ]}
                  variant="twoGroup"
                />
              ),
            },
          ]}
          variant="threeGroup"
        />
        <InputGroup
          showTopInputOnly={isCollapsed}
          isLinked={!attributes?.overriddenStatus.currentLiabilities}
          inputs={[
            {
              error: errors?.currentLiabilities,
              label: 'Current liabilities*',
              level: 0,
              tooltip: tooltips.currentLiabilities,
              value: attributes?.currentLiabilities,
              rightSideComponent: (
                <LinkButton
                  attribute="currentLiabilities"
                  isLinked={!attributes?.overriddenStatus.currentLiabilities}
                />
              ),
              isLocked: !attributes?.overriddenStatus.currentLiabilities,
              onChange: (value) => dispatch(updateAttributeAndEvaluate({ currentLiabilities: value })),
            },
            {
              allowNegatives: false,
              error: errors?.loans,
              label: 'Loans**',
              level: 1,
              value: attributes?.loans,
              onChange: (value) => dispatch(updateAttributeAndEvaluate({ loans: value })),
            },
            {
              allowNegatives: false,
              error: errors?.creditors,
              label: 'Creditors**',
              level: 1,
              value: attributes?.creditors,
              onChange: (value) => dispatch(updateAttributeAndEvaluate({ creditors: value })),
            },
            {
              allowNegatives: false,
              label: 'Other current liabilities',
              level: 1,
              value: attributes?.otherCurrentLiabilities,
              onChange: (value) => dispatch(updateAttributeAndEvaluate({ otherCurrentLiabilities: value })),
            },
          ]}
          variant="fourGroup"
        />
        <NumberInput
          error={errors?.totalShareFundsAndLiabilities}
          label="Total shareholder funds and liabilities*"
          inputType="float"
          tooltip={tooltips.totalShareFundsAndLiabilities}
          value={attributes?.totalShareFundsAndLiabilities}
          width="l"
          rightSideComponent={
            <LinkButton
              attribute="totalShareFundsAndLiabilities"
              isLinked={!attributes?.overriddenStatus.totalShareFundsAndLiabilities}
            />
          }
          isLocked={!attributes?.overriddenStatus.totalShareFundsAndLiabilities}
          onChange={(value) => dispatch(updateAttribute({ totalShareFundsAndLiabilities: value, checkValid: true }))}
        />
      </FlexLayout>
      <FlexLayout flexDirection="column" flexGrow="1" justifyContent="flex-end">
        <Text color="bali-hai" variant="s-spaced-italic">
          *Required field
        </Text>
        <Text color="bali-hai" variant="s-spaced-italic">
          **Important field but not required
        </Text>
      </FlexLayout>
    </Card>
  );
}

export default BalanceSheet;
