import { Text } from 'ui';

type ExpandButtonPropsType = {
  isCollapsed: boolean;
  setIsCollapsed: React.Dispatch<React.SetStateAction<boolean>>;
};

const ExpandButton = ({ isCollapsed, setIsCollapsed }: ExpandButtonPropsType) => {
  return (
    <Text
      px={2}
      sx={{
        border: 'border',
        borderRadius: 'm',
        borderColor: 'deep-sapphire',
        lineHeight: 'inherit',
        height: 'fit-content',
      }}
      variant="xs-spaced-medium"
      color="deep-sapphire"
      onClick={() => setIsCollapsed(!isCollapsed)}
    >
      {isCollapsed ? 'Expand' : 'Collapse'}
    </Text>
  );
};

export default ExpandButton;
