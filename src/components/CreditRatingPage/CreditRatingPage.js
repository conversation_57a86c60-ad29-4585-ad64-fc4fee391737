import _ from 'lodash';
import { useContext, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { createCreditRatingFile, postCreditRating } from '~/api';
import { REPORT_TYPE, reportEnum } from '~/enums';
import { useCompanies } from '~/hooks';
import {
  creditRating,
  isCreditRatingFormValid,
  resetCreditRating,
  setCreditRatingData,
  setIsPristine,
  transform,
  updateField,
} from '~/reducers/creditRating.slice';
import { Button, FlexLayout, Text } from '~/ui';
import { showErrorToast, showToast } from '~/ui/components/Toast';
import { WithTooltip } from '~/ui/hocs';
import { errorHandler } from '~/utils/errors';

import { UserInfoContext } from 'context';
import { isBefore, parse } from 'date-fns';
import { Modal } from 'ui';
import CreditRatingForm from './CreditRatingForm';

const NOTIFICATION_DATE = new Date(parse('2025-10-08', 'yyyy-MM-dd', new Date()));

function CreditRatingPage() {
  const dispatch = useDispatch();
  const data = useSelector(creditRating);
  const history = useHistory();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedTemplateFile, setUploadedTemplateFile] = useState(null);
  useCompanies(); // refresh company list
  const user = useContext(UserInfoContext);
  const [isNotificationModalShowing, setIsNotificationModalShowing] = useState(false);

  useEffect(() => {
    dispatch(setCreditRatingData());

    return () => {
      dispatch(resetCreditRating());
    };
  }, [dispatch]);

  useEffect(() => {
    if (
      isBefore(new Date(user.userInfo.createdAt), NOTIFICATION_DATE) &&
      !localStorage.getItem('credit_rating_notification_dismissed')
    ) {
      setIsNotificationModalShowing(true);
    }
  }, [user.userInfo.createdAt]);

  async function handleOnSubmitClick() {
    const errors = isCreditRatingFormValid(data);

    if (Object.keys(errors).length) {
      showErrorToast('One or more fields are invalid.');
      dispatch(updateField({ errors: errors, showErrors: true }));
      return;
    }
    setIsSubmitting(true);

    try {
      const requestData = transform(data);
      const createdCreditRating = await postCreditRating(requestData);
      showToast('Credit rating was successfully created.');
      dispatch(setIsPristine());
      if (uploadedTemplateFile) {
        await createCreditRatingFile({
          reportId: createdCreditRating.id,
          file: uploadedTemplateFile,
          label: 'Other',
          status: 'Final',
        });
      }
      history.push(`/analyses/${createdCreditRating.id}?${REPORT_TYPE}=${reportEnum.CREDIT_RATING}`);
    } catch (error) {
      errorHandler(error);
    } finally {
      setIsSubmitting(false);
    }
  }

  const isSubmitDisabled = (data?.showErrors && Object.keys(data?.errors).length !== 0) || _.isEmpty(data.company);

  const handleOnNotificationModalHide = () => {
    localStorage.setItem('credit_rating_notification_dismissed', true);
    setIsNotificationModalShowing(false);
  };

  return (
    <>
      <CreditRatingForm
        actionButtons={
          <FlexLayout alignItems="flex-end" flexGrow="1" justifyContent="flex-end">
            <WithTooltip
              tooltip="Company needs to be picked and all errors<br /> need to be resolved before creating an analysis."
              label="Create analysis"
              disabled={isSubmitDisabled}
            >
              <Button
                disabled={isSubmitDisabled}
                loading={isSubmitting}
                iconRight="arrowRight"
                text="Create analysis"
                onClick={isSubmitting ? null : handleOnSubmitClick}
              />
            </WithTooltip>
          </FlexLayout>
        }
        setUploadedTemplateFile={setUploadedTemplateFile}
      />
      <Modal
        isShowing={isNotificationModalShowing}
        actionButtons={<Button text="Continue" size="l" onClick={handleOnNotificationModalHide} />}
        title="Credit Rating Input Added"
        onHide={() => {}}
        showCancelButton={false}
      >
        <Text color="deep-sapphire" variant="m-spaced" sx={{ lineHeight: '21px' }}>
          We’ve added a new Interest Paid field to the credit rating input and Excel template to help you capture more
          complete information. You’ll see this field the next time you create or edit a credit rating. Any existing
          ratings remain unchanged. The old template will still work you can continue using it if preferred.
        </Text>
      </Modal>
    </>
  );
}

export default CreditRatingPage;
