import { useDispatch } from 'react-redux';

import { Icon, RightSideComponent } from 'ui';
import { toggleAttributeOverriddenStatus } from 'reducers/creditRating.slice';

type LinkButtonPropsType = {
  attribute: string;
  isLinked: boolean;
};

const LinkButton = ({ attribute, isLinked }: LinkButtonPropsType) => {
  const dispatch = useDispatch();
  return (
    <RightSideComponent>
      <Icon
        icon={isLinked ? 'link' : 'unlink'}
        size="m"
        onClick={() => dispatch(toggleAttributeOverriddenStatus({ attribute: attribute }))}
      />
    </RightSideComponent>
  );
};

export default LinkButton;
