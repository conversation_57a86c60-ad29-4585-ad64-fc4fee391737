import { saveAs } from 'file-saver';
import { useContext, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { createCreditRatingFile, getTemplate, postCreditRatings } from '~/api';
import { CompanySingleSelect, ThreeDotActionMenu } from '~/components/Shared';
import { UserInfoContext } from '~/context/user';
import { useCompanies, useUnsavedChangesWarning } from '~/hooks';
import {
  clearCreditRating,
  creditRating,
  evaluateCreditRatingData,
  isCreditRatingFormValid,
  setCreditRatingAndEvaluate,
  toggleCompanyPseudonym,
  transform,
  updateAttribute,
  updateField,
} from '~/reducers/creditRating.slice';
import {
  Box,
  Button,
  Card,
  DateInput,
  FileInput,
  FlexLayout,
  Modal,
  NumberInput,
  PageLayout,
  RadioGroup,
  Switch,
} from '~/ui';
import { showToast } from '~/ui/components/Toast';
import { sheetToJson, transformToJSObject } from '~/utils/documents';
import { errorHandler, fullResponseErrorHandler } from '~/utils/errors';

import BalanceSheet from './BalanceSheet';
import CreditRatingDrafts from './CreditRatingDrafts';
import { getCreditRatingDataFromSheet, tooltips } from './CreditRatingPage.utils';
import IncomeStatement from './IncomeStatement';
import {
  CurrencySingleSelect,
  MonthsSingleSelect,
  NaceSectorGroupSingleSelect,
  NaceSectorSingleSelect,
} from './SingleSelects';

function CreditRatingForm({ actionButtons, setUploadedTemplateFile }) {
  const dispatch = useDispatch();
  const history = useHistory();
  const data = useSelector(creditRating);
  const companies = useCompanies(false);
  const [isUploading, setIsUploading] = useState(false);
  const [showClearModal, setShowClearModal] = useState(false);
  const [isConfirmMultipleUploadModalShowing, setIsConfirmMultipleUploadModalShowing] = useState(false);
  const [areMultipleCreditRatingsUploading, setAreMultipleCreditRatingsUploading] = useState(false);
  const [bulkFormData, setBulkFormData] = useState([]);
  const { userInfo } = useContext(UserInfoContext);
  const [isAnonymized, setIsAnonymized] = useState(userInfo.client.isCreditRatingAnonymized);
  const [Prompt] = useUnsavedChangesWarning({ isDirty: data.isDirty });

  // field freeze functionality 2025-30-09 - this feature can be easily re-enabled in the future if we implement per-analysis pricing.
  // const isEdit = history.location.pathname.includes('/edit');

  async function handleOnDownloadClick() {
    try {
      saveAs(await getTemplate(), 'credit_rating_template.xlsx');
      showToast('Template was successfully downloaded.');
    } catch (error) {
      fullResponseErrorHandler(error);
    }
  }

  async function onCreateMultipleCreditRatings() {
    try {
      setAreMultipleCreditRatingsUploading(true);
      const createdCreditRatings = await postCreditRatings(bulkFormData.map(({ formData }) => formData));
      await Promise.all(
        createdCreditRatings.map(({ id }, i) =>
          createCreditRatingFile({ reportId: id, file: bulkFormData[i].document, label: 'Other', status: 'Final' })
        )
      );

      showToast('Credit ratings created.');
    } catch (error) {
      errorHandler(error);
    } finally {
      setIsConfirmMultipleUploadModalShowing(false);
      setAreMultipleCreditRatingsUploading(false);
    }
  }

  async function handleOnUploadClick(documents) {
    try {
      setIsUploading(true);

      const isSingleDocumentUpload = documents.length === 1;
      const bulkFormData = [];
      for (const document of documents) {
        const sheet = transformToJSObject(await sheetToJson(new Uint8Array(await document.arrayBuffer())));

        const formData = getCreditRatingDataFromSheet(sheet, companies, userInfo);
        isCreditRatingFormValid(formData, { shouldThrowError: true, documentName: document.name });

        if (isSingleDocumentUpload) {
          setUploadedTemplateFile(document);
          dispatch(setCreditRatingAndEvaluate(formData));
          showToast('Sheet was successfully uploaded.');
          return;
        }

        bulkFormData.push({ formData: transform(evaluateCreditRatingData(formData)), document });
      }
      setBulkFormData(bulkFormData);
      setIsConfirmMultipleUploadModalShowing(true);
    } catch (error) {
      errorHandler(error);
    } finally {
      setIsUploading(false);
    }
  }

  function onClearClick() {
    dispatch(clearCreditRating());
    setShowClearModal(false);
  }

  const handleAnonymizeToggle = () => {
    setIsAnonymized(!isAnonymized);
    dispatch(toggleCompanyPseudonym(!isAnonymized));
  };

  useEffect(() => {
    if (data.company && !!data.company?.pseudonym !== isAnonymized) {
      dispatch(toggleCompanyPseudonym(isAnonymized));
    }
  }, [dispatch, data?.company, isAnonymized, userInfo.client.isCreditRatingAnonymized]);

  return (
    <>
      <PageLayout
        rightTitleContent={
          <FlexLayout alignItems="center" space={6}>
            <CreditRatingDrafts />
            <Button
              iconLeft="download"
              size="s"
              text="Download template"
              variant="secondary"
              onClick={handleOnDownloadClick}
            />
            <FileInput accept=".xls,.xlsx,.xlsm" multiple sx={{ alignSelf: 'center' }} onChange={handleOnUploadClick}>
              <Button iconLeft="upload" loading={isUploading} size="s" text="Upload template" variant="secondary" />
            </FileInput>
            <ThreeDotActionMenu options={[{ label: 'Clear', onClick: () => setShowClearModal(true) }]} />
          </FlexLayout>
        }
        title="Credit Rating"
      >
        <Card py={6}>
          <FlexLayout flexDirection="column" space={6}>
            <Box
              sx={{
                display: 'grid',
                gridGap: 8,
                gridTemplateColumns: 'repeat(5, 1fr)',
                gridTemplateAreas: '"company naceSectorGroup naceSectorGroup naceSector"',
              }}
            >
              <CompanySingleSelect
                error={data?.errors?.company}
                label="Company*"
                properties={['id', 'name', 'country']}
                sx={{ gridArea: 'company' }}
                tooltip={tooltips.company}
                // disabled={isEdit}
                value={data?.company?.id}
                width="fullWidth"
                onChange={(value) => {
                  const pseudonym = isAnonymized ? `Company#${value.id}` : undefined;
                  dispatch(updateField({ company: { ...value, pseudonym }, checkValid: true }));
                }}
              />
              <NaceSectorGroupSingleSelect
                sx={{ gridArea: 'naceSectorGroup' }}
                tooltip={tooltips.naceSectorGroup}
                // disabled={isEdit}
                value={data?.attributes?.naceSectorGroup}
                width="fullWidth"
                onChange={(value) => dispatch(updateAttribute({ naceSectorGroup: value, naceSector: null }))}
              />
              <NaceSectorSingleSelect
                error={data?.errors?.naceSector}
                naceSectorGroup={data?.attributes?.naceSectorGroup}
                sx={{ gridArea: 'naceSector' }}
                tooltip={tooltips.naceSector}
                // disabled={isEdit}
                value={data?.attributes?.naceSector}
                width="fullWidth"
                onChange={(value) => dispatch(updateAttribute({ naceSector: value, checkValid: true }))}
              />
              <Switch
                label="Anonymize company"
                disabled={!data?.company?.id}
                isActive={isAnonymized}
                onChange={handleAnonymizeToggle}
              />
            </Box>
          </FlexLayout>
        </Card>
        <Card py={6}>
          <FlexLayout flexDirection="column" space={6}>
            <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(4, 1fr)' }}>
              <DateInput
                error={data?.errors?.closingDate}
                label="Closing Date*"
                tooltip={tooltips.closingDate}
                // disabled={isEdit}
                value={data?.closingDate}
                width="fullWidth"
                onChange={(value) => dispatch(updateField({ closingDate: value, checkValid: true }))}
              />
              <RadioGroup
                label="Financial Statement*"
                options={[
                  { label: 'Consolidated', value: 'Consolidated' },
                  { label: 'Unconsolidated', value: 'Unconsolidated' },
                ]}
                spaced
                tooltip={tooltips.option}
                // disabled={isEdit}
                value={data?.attributes?.option}
                width="fullWidth"
                onChange={(value) => dispatch(updateAttribute({ option: value }))}
              />
            </Box>
            <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(4, 1fr)' }}>
              <MonthsSingleSelect
                error={data?.errors?.months}
                tooltip={tooltips.months}
                // disabled={isEdit}
                value={data?.attributes?.months}
                width="fullWidth"
                onChange={(value) => dispatch(updateAttribute({ months: value, checkValid: true }))}
              />
              <CurrencySingleSelect
                tooltip={tooltips.currency}
                // disabled={isEdit}
                value={data?.attributes?.currency}
                width="fullWidth"
                onChange={(value) => dispatch(updateAttribute({ currency: value }))}
              />
              <NumberInput
                inputType="float"
                label="Exchange Rate"
                tooltip={tooltips.exchangeRate}
                // disabled={isEdit}
                value={data?.attributes?.exchangeRate}
                width="fullWidth"
                onChange={(value) => dispatch(updateAttribute({ exchangeRate: value }))}
              />
            </Box>
          </FlexLayout>
        </Card>
        <Box sx={{ display: 'grid', gridGap: 6, gridTemplateColumns: 'repeat(2, 1fr)' }}>
          <IncomeStatement />
          <BalanceSheet />
        </Box>
        {actionButtons}
        <Modal
          isShowing={showClearModal}
          actionButtons={<Button text="Clear" onClick={onClearClick} />}
          title="Are you sure you want to clear the form?"
          width="s"
          onHide={() => setShowClearModal(false)}
        />
        <Modal
          isShowing={isConfirmMultipleUploadModalShowing}
          actionButtons={
            <Button text="Create" loading={areMultipleCreditRatingsUploading} onClick={onCreateMultipleCreditRatings} />
          }
          title={`Are you sure you want to create ${bulkFormData.length} credit ratings? It will take up to three minutes to finish.`}
          width="s"
          onHide={() => setIsConfirmMultipleUploadModalShowing(false)}
        />
      </PageLayout>
      {Prompt}
    </>
  );
}

export default CreditRatingForm;
