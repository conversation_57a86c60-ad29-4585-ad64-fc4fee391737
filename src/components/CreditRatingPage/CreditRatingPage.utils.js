import format from 'date-fns/format';

import { DATE_FNS_FORMATS } from '~/enums/dates';
import { createCompanyValue, getCompanyByName } from '~/utils/companies';

import { currencies, months, naceSectors } from './SingleSelects';

const formToSheetMapper = {
  intangibleFixedAssets: 'Intangible fixed assets',
  tangibleFixedAssets: 'Tangible fixed assets',
  otherFixedAssets: 'Other fixed assets',
  stocks: 'Stocks',
  debtors: 'Debtors',
  otherCurrentAssets: 'Other current assets',
  cashAndCashEquivalent: 'Cash and cash equivalent',
  longTermDebt: 'Long-term debt',
  otherNonCurrentLiabilities: 'Other non-current liabilities',
  provisions: 'Provisions',
  loans: 'Loans',
  creditors: 'Creditors',
  otherCurrentLiabilities: 'Other current liabilities',
  operatingRevenueTurnover: 'Operating revenue/turnover',
  sales: 'Sales',
  materialCosts: 'Material costs',
  costOfEmployees: 'Cost of employees',
  depreciation: 'Depreciation',
  financialRevenue: 'Financial revenue',
  financialExpenses: ['Financial expenses', 'Financial expenses  (incl. interest paid)'],
  interestPaid: 'Interest paid',
  taxation: 'Taxation',
  extrAndOtherRevenue: 'Extraordinary and other revenue',
  extrAndOtherExpenses: 'Extraordinary and other expenses',
  costOfGoodSold: 'Costs of good sold',
  otherOperatingExpenses: 'Other operating expenses',
};

// These values can be negative + Taxation
export const formToSheetOverridableValuesMapper = {
  fixedAssets: 'Fixed assets',
  currentAssets: 'Current assets',
  totalAssets: 'Total assets',
  shareholdersFunds: 'Shareholder funds',
  capital: 'Capital',
  otherShareholdersFunds: 'Other shareholders funds',
  treasuryShares: 'Treasury shares',
  nonCurrentLiabilities: 'Non-current liabilities',
  currentLiabilities: 'Current liabilities',
  totalShareFundsAndLiabilities: 'Total shareholder funds & liabilities',
  EBIT: 'EBIT',
  EBITDA: 'EBITDA',
  financialPL: 'Financial P/L',
  PLBeforeTax: 'P/L before tax',
  PLAfterTax: 'P/L after tax',
  extrAndOtherPL: 'Extraordinary and other P/L',
  PLForPeriod: 'P/L for period',
  grossProfit: 'Gross profit',
};

export function getCreditRatingDataFromSheet(sheet, companies, userInfo) {
  const data = {};
  const attributes = { overriddenStatus: {} };
  const company = getCompanyByName(companies, sheet['Company name']);
  if (Object.keys(company).length !== 0) {
    data.company = createCompanyValue(company, ['id', 'name', 'country']);
  }
  data.closingDate = sheet['Closing date'] ? `${format(new Date(sheet['Closing date']), DATE_FNS_FORMATS.ISO)}` : null;
  attributes.months = months.find((month) => month === sheet['Number of months']) || null;
  attributes.naceSector =
    naceSectors
      .map((naceSectorGroups) => naceSectorGroups.options)
      .flat()
      .find((naceSector) => parseFloat(naceSector.value) === parseFloat(sheet['Industry sector (NACE rev. 2 code)']))
      ?.value || null;
  attributes.option = sheet['Unconsolidated or Consolidated'] || 'Consolidated';
  attributes.currency =
    currencies
      .map((currencyGroup) => currencyGroup.options)
      .flat()
      .find((currency) => currency.value === sheet['Currency'])?.value || null;
  attributes.exchangeRate = sheet['Exchange rate'] || null;

  // Positive values
  const keys = Object.keys(formToSheetMapper);

  for (let i = 0, len = keys.length; i < len; i++) {
    const mapping = formToSheetMapper[keys[i]];
    let value = null;

    if (Array.isArray(mapping)) {
      for (let j = 0; j < mapping.length; j++) {
        if (sheet[mapping[j]] != null) {
          value = sheet[mapping[j]];
          break;
        }
      }
    } else {
      value = sheet[mapping];
    }

    attributes[keys[i]] = value != null ? Math.abs(value) : null;
  }

  // Negative values
  const overridableKeys = Object.keys(formToSheetOverridableValuesMapper);
  for (let i = 0, len = overridableKeys.length; i < len; i++) {
    const value = sheet[formToSheetOverridableValuesMapper[overridableKeys[i]]] ?? null;
    attributes[overridableKeys[i]] = value;
    attributes.overriddenStatus[overridableKeys[i]] = true;
  }
  checkIfAttributesInSheetWereOverridden(attributes);
  attributes.taxation = sheet['Taxation'] ?? null;

  data.isTemplateUploaded = true;

  data.attributes = attributes;

  return data;
}

const checkIfAttributesInSheetWereOverridden = (attributes) => {
  if (
    attributes.fixedAssets ===
    attributes.intangibleFixedAssets + attributes.tangibleFixedAssets + attributes.otherFixedAssets
  ) {
    attributes.overriddenStatus.fixedAssets = false;
  }
  if (attributes.currentAssets === attributes.stocks + attributes.debtors + attributes.otherCurrentAssets) {
    attributes.overriddenStatus.currentAssets = false;
  }
  if (attributes.totalAssets === attributes.fixedAssets + attributes.currentAssets) {
    attributes.overriddenStatus.totalAssets = false;
  }
  if (attributes.shareholdersFunds === attributes.capital + attributes.otherShareholdersFunds) {
    attributes.overriddenStatus.shareholdersFunds = false;
  }
  if (attributes.nonCurrentLiabilities === attributes.longTermDebt + attributes.otherNonCurrentLiabilities) {
    attributes.overriddenStatus.nonCurrentLiabilities = false;
  }
  if (attributes.currentLiabilities === attributes.loans + attributes.creditors + attributes.otherCurrentLiabilities) {
    attributes.overriddenStatus.currentLiabilities = false;
  }
  if (
    attributes.totalShareFundsAndLiabilities ===
    attributes.nonCurrentLiabilities + attributes.currentLiabilities + attributes.shareholdersFunds
  ) {
    attributes.overriddenStatus.totalShareFundsAndLiabilities = false;
  }
  if (attributes.grossProfit === attributes.operatingRevenueTurnover - attributes.costOfGoodSold) {
    attributes.overriddenStatus.grossProfit = false;
  }
  if (
    attributes.EBIT ===
    attributes.grossProfit -
      attributes.materialCosts -
      attributes.costOfEmployees -
      attributes.otherOperatingExpenses -
      attributes.depreciation
  ) {
    attributes.overriddenStatus.EBIT = false;
  }
  if (attributes.EBITDA === attributes.EBIT + attributes.depreciation) {
    attributes.overriddenStatus.EBITDA = false;
  }
  if (attributes.financialPL === attributes.financialRevenue - attributes.financialExpenses) {
    attributes.overriddenStatus.financialPL = false;
  }
  if (attributes.PLBeforeTax === attributes.EBIT + attributes.financialPL) {
    attributes.overriddenStatus.PLBeforeTax = false;
  }
  if (attributes.PLAfterTax === attributes.PLBeforeTax - attributes.taxation) {
    attributes.overriddenStatus.PLAfterTax = false;
  }
  if (attributes.extrAndOtherPL === attributes.extrAndOtherRevenue - attributes.extrAndOtherExpenses) {
    attributes.overriddenStatus.extrAndOtherPL = false;
  }
  if (attributes.PLForPeriod === attributes.PLAfterTax + attributes.extrAndOtherPL) {
    attributes.overriddenStatus.PLForPeriod = false;
  }
};

export const tooltips = {
  company: 'Select the company that is being credit rated.',
  naceSectorGroup:
    "Select the sector group that most closely matches that of the company's operations.\n" +
    'This narrows the choices in the following Sector selection field.',
  naceSector:
    "Select the sector that most closely matches that of the company's operations.\n" +
    "The credit rating model takes sector choice into account when calculating the company's credit rating.",
  closingDate: "Select the closing date of the company's financials that are entered below.",
  option: "Select whether the company's financials entered below are on a consolidated or unconsolidated basis.",
  months: "Select the number of months that the company's financials entered below represent.",
  currency: "Select the currency in which the company's financials entered below are denominated.",
  exchangeRate:
    'Enter the exchange rate of the selected currency against the EUR<br /> (i.e., how many EUR make up one unit of the selected currency)',
  operatingRevenueTurnover: 'Operating revenue/turnover = Sales + Financial revenue',
  grossProfit: 'Gross profit = Operating revenue - Cost of goods sold',
  EBIT: 'EBIT = Gross profit - Material costs - Cost of employees - Other operating expenses - Depreciation',
  EBITDA: 'EBITDA = EBIT + Depreciation',
  financialPL: 'Financial P/L = Financial revenue - Financial expenses',
  financialExpenses: 'Interest paid should be included in this figure',
  interestPaid: 'Interest paid must be less than or equal to financial expenses',
  PLBeforeTax: 'P/L before tax = EBIT + Financial P/L',
  PLAfterTax: 'P/L after tax = P/L before tax - Taxation',
  extrAndOtherPL: 'Extraordinary and other P/L = Extraordinary and other revenue - Extraordinary and other expenses',
  PLForPeriod: 'P/L for period = P/L after tax + Extraordinary and other P/L',
  fixedAssets: 'Fixed assets = Intangible fixed assets + Tangible fixed assets + Other fixed assets',
  currentAssets: 'Current assets = Stocks + Debtors + Other current assets',
  otherCurrentAssets: 'Cash & cash equivalents should be included in this figure',
  cashAndCashEquivalent: 'Cash & cash equivalents must be less than other current assets',
  totalAssets:
    'Total assets = Fixed assets + Current assets<br/>' +
    'Total assets must be equal to total shareholder funds & liabilities',
  shareholdersFunds: 'Shareholder funds = Capital + Other shareholders funds',
  treasuryShares: 'Treasury shares must be less than other shareholder funds',
  nonCurrentLiabilities: 'Non-current liabilities = Long term debt + Other non-current liabilities',
  provisions: 'Provisions must be less than other non-current liabilities',
  currentLiabilities: 'Current liabilities = Loans + Creditors + Other current liabilities',
  totalShareFundsAndLiabilities:
    'Total shareholder funds & liabilities = Shareholder funds + Non-current liabilities + Current liabilities<br/>' +
    'Total shareholder funds & liabilities must be equal to total assets',
};
