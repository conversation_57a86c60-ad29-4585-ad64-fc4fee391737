import React from 'react';

import { SingleSelect } from '~/ui';
import { getOptionsFromArray } from '~/utils/arrays';

import { months } from './constants';

const options = getOptionsFromArray(months);

function MonthsSingleSelect({ error, label = 'Months*', tooltip, value, disabled, width = 'm', onChange }) {
  return (
    <SingleSelect
      error={error}
      label={label}
      options={options}
      tooltip={tooltip}
      disabled={disabled}
      value={value}
      width={width}
      onChange={onChange}
    />
  );
}

export default MonthsSingleSelect;
