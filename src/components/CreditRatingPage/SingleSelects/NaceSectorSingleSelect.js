import React from 'react';

import { SingleSelect } from '~/ui';

import { naceSectors } from './constants';

function NaceSectorSingleSelect({
  error,
  label = 'Sector*',
  sx,
  tooltip,
  disabled,
  naceSectorGroup,
  value,
  width = 'm',
  onChange,
}) {
  let filteredOptions;
  const hasGroupedOptions = !naceSectorGroup || naceSectorGroup === 'ALL';
  if (hasGroupedOptions) {
    filteredOptions = naceSectors;
  } else {
    filteredOptions = naceSectors.find((option) => option.label === naceSectorGroup).options;
  }

  return (
    <SingleSelect
      error={error}
      hasGroupedOptions={hasGroupedOptions}
      label={label}
      options={filteredOptions}
      sx={sx}
      tooltip={tooltip}
      disabled={disabled}
      value={value}
      width={width}
      onChange={onChange}
    />
  );
}

export default NaceSectorSingleSelect;
