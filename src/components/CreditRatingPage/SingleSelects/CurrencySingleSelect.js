import React from 'react';

import { SingleSelect } from '~/ui';

import { currencies } from './constants';

function CurrencySingleSelect({ disabled = false, label = 'Currency', tooltip, value, width = 'm', onChange }) {
  return (
    <SingleSelect
      disabled={disabled}
      hasGroupedOptions
      label={label}
      options={currencies}
      tooltip={tooltip}
      value={value}
      width={width}
      onChange={onChange}
    />
  );
}

export default CurrencySingleSelect;
