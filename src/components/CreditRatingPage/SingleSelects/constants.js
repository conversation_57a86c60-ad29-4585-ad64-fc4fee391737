export const currencies = [
  {
    label: 'Global Core',
    options: [
      {
        label: 'USD',
        value: 'USD',
      },
      {
        label: 'EUR',
        value: 'EUR',
      },
      {
        label: 'GBP',
        value: 'GBP',
      },
      {
        label: 'CHF',
        value: 'CHF',
      },
      {
        label: 'AUD',
        value: 'AUD',
      },
      {
        label: 'JPY',
        value: 'JPY',
      },
      {
        label: 'CAD',
        value: 'CAD',
      },
    ],
  },
  {
    label: 'Asia',
    options: [
      {
        label: 'CNY',
        value: 'CNY',
      },
      {
        label: 'HKD',
        value: 'HKD',
      },
      {
        label: 'INR',
        value: 'INR',
      },
      {
        label: 'IDR',
        value: 'IDR',
      },
      {
        label: 'KRW',
        value: 'KRW',
      },
      {
        label: 'MYR',
        value: 'MYR',
      },
      {
        label: 'PHP',
        value: 'PHP',
      },
      {
        label: 'SGD',
        value: 'SGD',
      },
      {
        label: 'THB',
        value: 'THB',
      },
    ],
  },
  {
    label: 'W. Europe',
    options: [
      {
        label: 'DKK',
        value: 'DKK',
      },
      {
        label: 'ISK',
        value: 'ISK',
      },
      {
        label: 'NOK',
        value: 'NOK',
      },
      {
        label: 'SEK',
        value: 'SEK',
      },
    ],
  },
  {
    label: 'E. Europe',
    options: [
      {
        label: 'BGN',
        value: 'BGN',
      },
      {
        label: 'CZK',
        value: 'CZK',
      },
      {
        label: 'HRK',
        value: 'HRK',
      },
      {
        label: 'HUF',
        value: 'HUF',
      },
      {
        label: 'RON',
        value: 'RON',
      },
      {
        label: 'PLN',
        value: 'PLN',
      },
      {
        label: 'RUB',
        value: 'RUB',
      },
    ],
  },
  {
    label: 'Oceania',
    options: [
      {
        label: 'NZD',
        value: 'NZD',
      },
    ],
  },
  {
    label: 'Latin America',
    options: [
      {
        label: 'BRL',
        value: 'BRL',
      },
      {
        label: 'MXN',
        value: 'MXN',
      },
    ],
  },
  {
    label: 'Middle East & Africa',
    options: [
      {
        label: 'AED',
        value: 'AED',
      },
      {
        label: 'EGP',
        value: 'EGP',
      },
      {
        label: 'ILS',
        value: 'ILS',
      },
      {
        label: 'JOD',
        value: 'JOD',
      },
      {
        label: 'KWD',
        value: 'KWD',
      },
      {
        label: 'TRY',
        value: 'TRY',
      },
      {
        label: 'SAR',
        value: 'SAR',
      },
      {
        label: 'ZAR',
        value: 'ZAR',
      },
    ],
  },
];

export const months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];

export const naceSectors = [
  {
    label: 'A - AGRICULTURE, FORESTRY AND FISHING',
    options: [
      {
        label: '1 Crop and animal production, hunting and related service activities',
        value: '1',
      },
      {
        label: '01.1 Growing of non-perennial crops',
        value: '01.1',
        indentLevel: 1,
      },
      {
        label: '01.11 Growing of cereals (except rice), leguminous crops and oil seeds',
        value: '01.11',
        indentLevel: 2,
      },
      {
        label: '01.12 Growing of rice',
        value: '01.12',
        indentLevel: 2,
      },
      {
        label: '01.13 Growing of vegetables and melons, roots and tubers',
        value: '01.13',
        indentLevel: 2,
      },
      {
        label: '01.14 Growing of sugar cane',
        value: '01.14',
        indentLevel: 2,
      },
      {
        label: '01.15 Growing of tobacco',
        value: '01.15',
        indentLevel: 2,
      },
      {
        label: '01.16 Growing of fibre crops',
        value: '01.16',
        indentLevel: 2,
      },
      {
        label: '01.19 Growing of other non-perennial crops',
        value: '01.19',
        indentLevel: 2,
      },
      {
        label: '01.2 Growing of perennial crops',
        value: '01.2',
        indentLevel: 1,
      },
      {
        label: '01.21 Growing of grapes',
        value: '01.21',
        indentLevel: 2,
      },
      {
        label: '01.22 Growing of tropical and subtropical fruits',
        value: '01.22',
        indentLevel: 2,
      },
      {
        label: '01.23 Growing of citrus fruits',
        value: '01.23',
        indentLevel: 2,
      },
      {
        label: '01.24 Growing of pome fruits and stone fruits',
        value: '01.24',
        indentLevel: 2,
      },
      {
        label: '01.25 Growing of other tree and bush fruits and nuts',
        value: '01.25',
        indentLevel: 2,
      },
      {
        label: '01.26 Growing of oleaginous fruits',
        value: '01.26',
        indentLevel: 2,
      },
      {
        label: '01.27 Growing of beverage crops',
        value: '01.27',
        indentLevel: 2,
      },
      {
        label: '01.28 Growing of spices, aromatic, drug and pharmaceutical crops',
        value: '01.28',
        indentLevel: 2,
      },
      {
        label: '01.29 Growing of other perennial crops',
        value: '01.29',
        indentLevel: 2,
      },
      {
        label: '01.3 Plant propagation',
        value: '01.3',
        indentLevel: 1,
      },
      {
        label: '01.30 Plant propagation',
        value: '01.30',
        indentLevel: 2,
      },
      {
        label: '01.4 Animal production',
        value: '01.4',
        indentLevel: 1,
      },
      {
        label: '01.41 Raising of dairy cattle',
        value: '01.41',
        indentLevel: 2,
      },
      {
        label: '01.42 Raising of other cattle and buffaloes',
        value: '01.42',
        indentLevel: 2,
      },
      {
        label: '01.43 Raising of horses and other equines',
        value: '01.43',
        indentLevel: 2,
      },
      {
        label: '01.44 Raising of camels and camelids',
        value: '01.44',
        indentLevel: 2,
      },
      {
        label: '01.45 Raising of sheep and goats',
        value: '01.45',
        indentLevel: 2,
      },
      {
        label: '01.46 Raising of swine/pigs',
        value: '01.46',
        indentLevel: 2,
      },
      {
        label: '01.47 Raising of poultry',
        value: '01.47',
        indentLevel: 2,
      },
      {
        label: '01.49 Raising of other animals',
        value: '01.49',
        indentLevel: 2,
      },
      {
        label: '01.5 Mixed farming',
        value: '01.5',
        indentLevel: 1,
      },
      {
        label: '01.50 Mixed farming',
        value: '01.50',
        indentLevel: 2,
      },
      {
        label: '01.6 Support activities to agriculture and post-harvest crop activities',
        value: '01.6',
        indentLevel: 1,
      },
      {
        label: '01.61 Support activities for crop production',
        value: '01.61',
        indentLevel: 2,
      },
      {
        label: '01.62 Support activities for animal production',
        value: '01.62',
        indentLevel: 2,
      },
      {
        label: '01.63 Post-harvest crop activities',
        value: '01.63',
        indentLevel: 2,
      },
      {
        label: '01.64 Seed processing for propagation',
        value: '01.64',
        indentLevel: 2,
      },
      {
        label: '01.7 Hunting, trapping and related service activities',
        value: '01.7',
        indentLevel: 1,
      },
      {
        label: '01.70 Hunting, trapping and related service activities',
        value: '01.70',
        indentLevel: 2,
      },
      {
        label: '2 Forestry and logging',
        value: '2',
      },
      {
        label: '02.1 Silviculture and other forestry activities',
        value: '02.1',
        indentLevel: 1,
      },
      {
        label: '02.10 Silviculture and other forestry activities',
        value: '02.10',
        indentLevel: 2,
      },
      {
        label: '02.2 Logging',
        value: '02.2',
        indentLevel: 1,
      },
      {
        label: '02.20 Logging',
        value: '02.20',
        indentLevel: 2,
      },
      {
        label: '02.3 Gathering of wild growing non-wood products',
        value: '02.3',
        indentLevel: 1,
      },
      {
        label: '02.30 Gathering of wild growing non-wood products',
        value: '02.30',
        indentLevel: 2,
      },
      {
        label: '02.4 Support services to forestry',
        value: '02.4',
        indentLevel: 1,
      },
      {
        label: '02.40 Support services to forestry',
        value: '02.40',
        indentLevel: 2,
      },
      {
        label: '3 Fishing and aquaculture',
        value: '3',
      },
      {
        label: '03.1 Fishing',
        value: '03.1',
        indentLevel: 1,
      },
      {
        label: '03.11 Marine fishing',
        value: '03.11',
        indentLevel: 2,
      },
      {
        label: '03.12 Freshwater fishing',
        value: '03.12',
        indentLevel: 2,
      },
      {
        label: '03.2 Aquaculture',
        value: '03.2',
        indentLevel: 1,
      },
      {
        label: '03.21 Marine aquaculture',
        value: '03.21',
        indentLevel: 2,
      },
      {
        label: '03.22 Freshwater aquaculture',
        value: '03.22',
        indentLevel: 2,
      },
    ],
  },
  {
    label: 'B - MINING AND QUARRYING',
    options: [
      {
        label: '5 Mining of coal and lignite',
        value: '5',
      },
      {
        label: '05.1 Mining of hard coal',
        value: '05.1',
        indentLevel: 1,
      },
      {
        label: '05.10 Mining of hard coal',
        value: '05.10',
        indentLevel: 2,
      },
      {
        label: '05.2 Mining of lignite',
        value: '05.2',
        indentLevel: 1,
      },
      {
        label: '05.20 Mining of lignite',
        value: '05.20',
        indentLevel: 2,
      },
      {
        label: '6 Extraction of crude petroleum and natural gas',
        value: '6',
      },
      {
        label: '06.1 Extraction of crude petroleum',
        value: '06.1',
        indentLevel: 1,
      },
      {
        label: '06.10 Extraction of crude petroleum',
        value: '06.10',
        indentLevel: 2,
      },
      {
        label: '06.2 Extraction of natural gas',
        value: '06.2',
        indentLevel: 1,
      },
      {
        label: '06.20 Extraction of natural gas',
        value: '06.20',
        indentLevel: 2,
      },
      {
        label: '7 Mining of metal ores',
        value: '7',
      },
      {
        label: '07.1 Mining of iron ores',
        value: '07.1',
        indentLevel: 1,
      },
      {
        label: '07.10 Mining of iron ores',
        value: '07.10',
        indentLevel: 2,
      },
      {
        label: '07.2 Mining of non-ferrous metal ores',
        value: '07.2',
        indentLevel: 1,
      },
      {
        label: '07.21 Mining of uranium and thorium ores',
        value: '07.21',
        indentLevel: 2,
      },
      {
        label: '07.29 Mining of other non-ferrous metal ores',
        value: '07.29',
        indentLevel: 2,
      },
      {
        label: '8 Other mining and quarrying',
        value: '8',
      },
      {
        label: '08.1 Quarrying of stone, sand and clay',
        value: '08.1',
        indentLevel: 1,
      },
      {
        label: '08.11 Quarrying of ornamental and building stone, limestone, gypsum, chalk and slate',
        value: '08.11',
        indentLevel: 2,
      },
      {
        label: '08.12 Operation of gravel and sand pits; mining of clays and kaolin',
        value: '08.12',
        indentLevel: 2,
      },
      {
        label: '08.9 Mining and quarrying n.e.c.',
        value: '08.9',
        indentLevel: 1,
      },
      {
        label: '08.91 Mining of chemical and fertiliser minerals',
        value: '08.91',
        indentLevel: 2,
      },
      {
        label: '08.92 Extraction of peat',
        value: '08.92',
        indentLevel: 2,
      },
      {
        label: '08.93 Extraction of salt',
        value: '08.93',
        indentLevel: 2,
      },
      {
        label: '08.99 Other mining and quarrying n.e.c.',
        value: '08.99',
        indentLevel: 2,
      },
      {
        label: '9 Mining support service activities',
        value: '9',
      },
      {
        label: '09.1 Support activities for petroleum and natural gas extraction',
        value: '09.1',
        indentLevel: 1,
      },
      {
        label: '09.10 Support activities for petroleum and natural gas extraction',
        value: '09.10',
        indentLevel: 2,
      },
      {
        label: '09.9 Support activities for other mining and quarrying',
        value: '09.9',
        indentLevel: 1,
      },
      {
        label: '09.90 Support activities for other mining and quarrying',
        value: '09.90',
        indentLevel: 2,
      },
    ],
  },
  {
    label: 'C - MANUFACTURING',
    options: [
      {
        label: '10 Manufacture of food products',
        value: '10',
      },
      {
        label: '10.1 Processing and preserving of meat and production of meat products',
        value: '10.1',
        indentLevel: 1,
      },
      {
        label: '10.11 Processing and preserving of meat',
        value: '10.11',
        indentLevel: 2,
      },
      {
        label: '10.12 Processing and preserving of poultry meat',
        value: '10.12',
        indentLevel: 2,
      },
      {
        label: '10.13 Production of meat and poultry meat products',
        value: '10.13',
        indentLevel: 2,
      },
      {
        label: '10.2 Processing and preserving of fish, crustaceans and molluscs',
        value: '10.2',
        indentLevel: 1,
      },
      {
        label: '10.20 Processing and preserving of fish, crustaceans and molluscs',
        value: '10.20',
        indentLevel: 2,
      },
      {
        label: '10.3 Processing and preserving of fruit and vegetables',
        value: '10.3',
        indentLevel: 1,
      },
      {
        label: '10.31 Processing and preserving of potatoes',
        value: '10.31',
        indentLevel: 2,
      },
      {
        label: '10.32 Manufacture of fruit and vegetable juice',
        value: '10.32',
        indentLevel: 2,
      },
      {
        label: '10.39 Other processing and preserving of fruit and vegetables',
        value: '10.39',
        indentLevel: 2,
      },
      {
        label: '10.4 Manufacture of vegetable and animal oils and fats',
        value: '10.4',
        indentLevel: 1,
      },
      {
        label: '10.41 Manufacture of oils and fats',
        value: '10.41',
        indentLevel: 2,
      },
      {
        label: '10.42 Manufacture of margarine and similar edible fats',
        value: '10.42',
        indentLevel: 2,
      },
      {
        label: '10.5 Manufacture of dairy products',
        value: '10.5',
        indentLevel: 1,
      },
      {
        label: '10.51 Operation of dairies and cheese making',
        value: '10.51',
        indentLevel: 2,
      },
      {
        label: '10.52 Manufacture of ice cream',
        value: '10.52',
        indentLevel: 2,
      },
      {
        label: '10.6 Manufacture of grain mill products, starches and starch products',
        value: '10.6',
        indentLevel: 1,
      },
      {
        label: '10.61 Manufacture of grain mill products',
        value: '10.61',
        indentLevel: 2,
      },
      {
        label: '10.62 Manufacture of starches and starch products',
        value: '10.62',
        indentLevel: 2,
      },
      {
        label: '10.7 Manufacture of bakery and farinaceous products',
        value: '10.7',
        indentLevel: 1,
      },
      {
        label: '10.71 Manufacture of bread; manufacture of fresh pastry goods and cakes',
        value: '10.71',
        indentLevel: 2,
      },
      {
        label: '10.72 Manufacture of rusks and biscuits; manufacture of preserved pastry goods and cakes',
        value: '10.72',
        indentLevel: 2,
      },
      {
        label: '10.73 Manufacture of macaroni, noodles, couscous and similar farinaceous products',
        value: '10.73',
        indentLevel: 2,
      },
      {
        label: '10.8 Manufacture of other food products',
        value: '10.8',
        indentLevel: 1,
      },
      {
        label: '10.81 Manufacture of sugar',
        value: '10.81',
        indentLevel: 2,
      },
      {
        label: '10.82 Manufacture of cocoa, chocolate and sugar confectionery',
        value: '10.82',
        indentLevel: 2,
      },
      {
        label: '10.83 Processing of tea and coffee',
        value: '10.83',
        indentLevel: 2,
      },
      {
        label: '10.84 Manufacture of condiments and seasonings',
        value: '10.84',
        indentLevel: 2,
      },
      {
        label: '10.85 Manufacture of prepared meals and dishes',
        value: '10.85',
        indentLevel: 2,
      },
      {
        label: '10.86 Manufacture of homogenised food preparations and dietetic food',
        value: '10.86',
        indentLevel: 2,
      },
      {
        label: '10.89 Manufacture of other food products n.e.c.',
        value: '10.89',
        indentLevel: 2,
      },
      {
        label: '10.9 Manufacture of prepared animal feeds',
        value: '10.9',
        indentLevel: 1,
      },
      {
        label: '10.91 Manufacture of prepared feeds for farm animals',
        value: '10.91',
        indentLevel: 2,
      },
      {
        label: '10.92 Manufacture of prepared pet foods',
        value: '10.92',
        indentLevel: 2,
      },
      {
        label: '11 Manufacture of beverages',
        value: '11',
      },
      {
        label: '11.0 Manufacture of beverages',
        value: '11.0',
        indentLevel: 1,
      },
      {
        label: '11.01 Distilling, rectifying and blending of spirits',
        value: '11.01',
        indentLevel: 2,
      },
      {
        label: '11.02 Manufacture of wine from grape',
        value: '11.02',
        indentLevel: 2,
      },
      {
        label: '11.03 Manufacture of cider and other fruit wines',
        value: '11.03',
        indentLevel: 2,
      },
      {
        label: '11.04 Manufacture of other non-distilled fermented beverages',
        value: '11.04',
        indentLevel: 2,
      },
      {
        label: '11.05 Manufacture of beer',
        value: '11.05',
        indentLevel: 2,
      },
      {
        label: '11.06 Manufacture of malt',
        value: '11.06',
        indentLevel: 2,
      },
      {
        label: '11.07 Manufacture of soft drinks; production of mineral waters and other bottled waters',
        value: '11.07',
        indentLevel: 2,
      },
      {
        label: '12 Manufacture of tobacco products',
        value: '12',
      },
      {
        label: '12.0 Manufacture of tobacco products',
        value: '12.0',
        indentLevel: 1,
      },
      {
        label: '12.00 Manufacture of tobacco products',
        value: '12.00',
        indentLevel: 2,
      },
      {
        label: '13 Manufacture of textiles',
        value: '13',
      },
      {
        label: '13.1 Preparation and spinning of textile fibres',
        value: '13.1',
        indentLevel: 1,
      },
      {
        label: '13.10 Preparation and spinning of textile fibres',
        value: '13.10',
        indentLevel: 2,
      },
      {
        label: '13.2 Weaving of textiles',
        value: '13.2',
        indentLevel: 1,
      },
      {
        label: '13.20 Weaving of textiles',
        value: '13.20',
        indentLevel: 2,
      },
      {
        label: '13.3 Finishing of textiles',
        value: '13.3',
        indentLevel: 1,
      },
      {
        label: '13.30 Finishing of textiles',
        value: '13.30',
        indentLevel: 2,
      },
      {
        label: '13.9 Manufacture of other textiles',
        value: '13.9',
        indentLevel: 1,
      },
      {
        label: '13.91 Manufacture of knitted and crocheted fabrics',
        value: '13.91',
        indentLevel: 2,
      },
      {
        label: '13.92 Manufacture of made-up textile articles, except apparel',
        value: '13.92',
        indentLevel: 2,
      },
      {
        label: '13.93 Manufacture of carpets and rugs',
        value: '13.93',
        indentLevel: 2,
      },
      {
        label: '13.94 Manufacture of cordage, rope, twine and netting',
        value: '13.94',
        indentLevel: 2,
      },
      {
        label: '13.95 Manufacture of non-wovens and articles made from non-wovens, except apparel',
        value: '13.95',
        indentLevel: 2,
      },
      {
        label: '13.96 Manufacture of other technical and industrial textiles',
        value: '13.96',
        indentLevel: 2,
      },
      {
        label: '13.99 Manufacture of other textiles n.e.c.',
        value: '13.99',
        indentLevel: 2,
      },
      {
        label: '14 Manufacture of wearing apparel',
        value: '14',
      },
      {
        label: '14.1 Manufacture of wearing apparel, except fur apparel',
        value: '14.1',
        indentLevel: 1,
      },
      {
        label: '14.11 Manufacture of leather clothes',
        value: '14.11',
        indentLevel: 2,
      },
      {
        label: '14.12 Manufacture of workwear',
        value: '14.12',
        indentLevel: 2,
      },
      {
        label: '14.13 Manufacture of other outerwear',
        value: '14.13',
        indentLevel: 2,
      },
      {
        label: '14.14 Manufacture of underwear',
        value: '14.14',
        indentLevel: 2,
      },
      {
        label: '14.19 Manufacture of other wearing apparel and accessories',
        value: '14.19',
        indentLevel: 2,
      },
      {
        label: '14.2 Manufacture of articles of fur',
        value: '14.2',
        indentLevel: 1,
      },
      {
        label: '14.20 Manufacture of articles of fur',
        value: '14.20',
        indentLevel: 2,
      },
      {
        label: '14.3 Manufacture of knitted and crocheted apparel',
        value: '14.3',
        indentLevel: 1,
      },
      {
        label: '14.31 Manufacture of knitted and crocheted hosiery',
        value: '14.31',
        indentLevel: 2,
      },
      {
        label: '14.39 Manufacture of other knitted and crocheted apparel',
        value: '14.39',
        indentLevel: 2,
      },
      {
        label: '15 Manufacture of leather and related products',
        value: '15',
      },
      {
        label:
          '15.1 Tanning and dressing of leather; manufacture of luggage, handbags, saddlery and harness; dressing and dyeing of fur',
        value: '15.1',
        indentLevel: 1,
      },
      {
        label: '15.11 Tanning and dressing of leather; dressing and dyeing of fur',
        value: '15.11',
        indentLevel: 2,
      },
      {
        label: '15.12 Manufacture of luggage, handbags and the like, saddlery and harness',
        value: '15.12',
        indentLevel: 2,
      },
      {
        label: '15.2 Manufacture of footwear',
        value: '15.2',
        indentLevel: 1,
      },
      {
        label: '15.20 Manufacture of footwear',
        value: '15.20',
        indentLevel: 2,
      },
      {
        label:
          '16 Manufacture of wood and of products of wood and cork, except furniture; manufacture of articles of straw and plaiting materials',
        value: '16',
      },
      {
        label: '16.1 Sawmilling and planing of wood',
        value: '16.1',
        indentLevel: 1,
      },
      {
        label: '16.10 Sawmilling and planing of wood',
        value: '16.10',
        indentLevel: 2,
      },
      {
        label: '16.2 Manufacture of products of wood, cork, straw and plaiting materials',
        value: '16.2',
        indentLevel: 1,
      },
      {
        label: '16.21 Manufacture of veneer sheets and wood-based panels',
        value: '16.21',
        indentLevel: 2,
      },
      {
        label: '16.22 Manufacture of assembled parquet floors',
        value: '16.22',
        indentLevel: 2,
      },
      {
        label: "16.23 Manufacture of other builders' carpentry and joinery",
        value: '16.23',
        indentLevel: 2,
      },
      {
        label: '16.24 Manufacture of wooden containers',
        value: '16.24',
        indentLevel: 2,
      },
      {
        label:
          '16.29 Manufacture of other products of wood; manufacture of articles of cork, straw and plaiting materials',
        value: '16.29',
        indentLevel: 2,
      },
      {
        label: '17 Manufacture of paper and paper products',
        value: '17',
      },
      {
        label: '17.1 Manufacture of pulp, paper and paperboard',
        value: '17.1',
        indentLevel: 1,
      },
      {
        label: '17.11 Manufacture of pulp',
        value: '17.11',
        indentLevel: 2,
      },
      {
        label: '17.12 Manufacture of paper and paperboard',
        value: '17.12',
        indentLevel: 2,
      },
      {
        label: '17.2 Manufacture of articles of paper and paperboard',
        value: '17.2',
        indentLevel: 1,
      },
      {
        label: '17.21 Manufacture of corrugated paper and paperboard and of containers of paper and paperboard',
        value: '17.21',
        indentLevel: 2,
      },
      {
        label: '17.22 Manufacture of household and sanitary goods and of toilet requisites',
        value: '17.22',
        indentLevel: 2,
      },
      {
        label: '17.23 Manufacture of paper stationery',
        value: '17.23',
        indentLevel: 2,
      },
      {
        label: '17.24 Manufacture of wallpaper',
        value: '17.24',
        indentLevel: 2,
      },
      {
        label: '17.29 Manufacture of other articles of paper and paperboard',
        value: '17.29',
        indentLevel: 2,
      },
      {
        label: '18 Printing and reproduction of recorded media',
        value: '18',
      },
      {
        label: '18.1 Printing and service activities related to printing',
        value: '18.1',
        indentLevel: 1,
      },
      {
        label: '18.11 Printing of newspapers',
        value: '18.11',
        indentLevel: 2,
      },
      {
        label: '18.12 Other printing',
        value: '18.12',
        indentLevel: 2,
      },
      {
        label: '18.13 Pre-press and pre-media services',
        value: '18.13',
        indentLevel: 2,
      },
      {
        label: '18.14 Binding and related services',
        value: '18.14',
        indentLevel: 2,
      },
      {
        label: '18.2 Reproduction of recorded media',
        value: '18.2',
        indentLevel: 1,
      },
      {
        label: '18.20 Reproduction of recorded media',
        value: '18.20',
        indentLevel: 2,
      },
      {
        label: '19 Manufacture of coke and refined petroleum products',
        value: '19',
      },
      {
        label: '19.1 Manufacture of coke oven products',
        value: '19.1',
        indentLevel: 1,
      },
      {
        label: '19.10 Manufacture of coke oven products',
        value: '19.10',
        indentLevel: 2,
      },
      {
        label: '19.2 Manufacture of refined petroleum products',
        value: '19.2',
        indentLevel: 1,
      },
      {
        label: '19.20 Manufacture of refined petroleum products',
        value: '19.20',
        indentLevel: 2,
      },
      {
        label: '20 Manufacture of chemicals and chemical products',
        value: '20',
      },
      {
        label:
          '20.1 Manufacture of basic chemicals, fertilisers and nitrogen compounds, plastics and synthetic rubber in primary forms',
        value: '20.1',
        indentLevel: 1,
      },
      {
        label: '20.11 Manufacture of industrial gases',
        value: '20.11',
        indentLevel: 2,
      },
      {
        label: '20.12 Manufacture of dyes and pigments',
        value: '20.12',
        indentLevel: 2,
      },
      {
        label: '20.13 Manufacture of other inorganic basic chemicals',
        value: '20.13',
        indentLevel: 2,
      },
      {
        label: '20.14 Manufacture of other organic basic chemicals',
        value: '20.14',
        indentLevel: 2,
      },
      {
        label: '20.15 Manufacture of fertilisers and nitrogen compounds',
        value: '20.15',
        indentLevel: 2,
      },
      {
        label: '20.16 Manufacture of plastics in primary forms',
        value: '20.16',
        indentLevel: 2,
      },
      {
        label: '20.17 Manufacture of synthetic rubber in primary forms',
        value: '20.17',
        indentLevel: 2,
      },
      {
        label: '20.2 Manufacture of pesticides and other agrochemical products',
        value: '20.2',
        indentLevel: 1,
      },
      {
        label: '20.20 Manufacture of pesticides and other agrochemical products',
        value: '20.20',
        indentLevel: 2,
      },
      {
        label: '20.3 Manufacture of paints, varnishes and similar coatings, printing ink and mastics',
        value: '20.3',
        indentLevel: 1,
      },
      {
        label: '20.30 Manufacture of paints, varnishes and similar coatings, printing ink and mastics',
        value: '20.30',
        indentLevel: 2,
      },
      {
        label:
          '20.4 Manufacture of soap and detergents, cleaning and polishing preparations, perfumes and toilet preparations',
        value: '20.4',
        indentLevel: 1,
      },
      {
        label: '20.41 Manufacture of soap and detergents, cleaning and polishing preparations',
        value: '20.41',
        indentLevel: 2,
      },
      {
        label: '20.42 Manufacture of perfumes and toilet preparations',
        value: '20.42',
        indentLevel: 2,
      },
      {
        label: '20.5 Manufacture of other chemical products',
        value: '20.5',
        indentLevel: 1,
      },
      {
        label: '20.51 Manufacture of explosives',
        value: '20.51',
        indentLevel: 2,
      },
      {
        label: '20.52 Manufacture of glues',
        value: '20.52',
        indentLevel: 2,
      },
      {
        label: '20.53 Manufacture of essential oils',
        value: '20.53',
        indentLevel: 2,
      },
      {
        label: '20.59 Manufacture of other chemical products n.e.c.',
        value: '20.59',
        indentLevel: 2,
      },
      {
        label: '20.6 Manufacture of man-made fibres',
        value: '20.6',
        indentLevel: 1,
      },
      {
        label: '20.60 Manufacture of man-made fibres',
        value: '20.60',
        indentLevel: 2,
      },
      {
        label: '21 Manufacture of basic pharmaceutical products and pharmaceutical preparations',
        value: '21',
      },
      {
        label: '21.1 Manufacture of basic pharmaceutical products',
        value: '21.1',
        indentLevel: 1,
      },
      {
        label: '21.10 Manufacture of basic pharmaceutical products',
        value: '21.10',
        indentLevel: 2,
      },
      {
        label: '21.2 Manufacture of pharmaceutical preparations',
        value: '21.2',
        indentLevel: 1,
      },
      {
        label: '21.20 Manufacture of pharmaceutical preparations',
        value: '21.20',
        indentLevel: 2,
      },
      {
        label: '22 Manufacture of rubber and plastic products',
        value: '22',
      },
      {
        label: '22.1 Manufacture of rubber products',
        value: '22.1',
        indentLevel: 1,
      },
      {
        label: '22.11 Manufacture of rubber tyres and tubes; retreading and rebuilding of rubber tyres',
        value: '22.11',
        indentLevel: 2,
      },
      {
        label: '22.19 Manufacture of other rubber products',
        value: '22.19',
        indentLevel: 2,
      },
      {
        label: '22.2 Manufacture of plastic products',
        value: '22.2',
        indentLevel: 1,
      },
      {
        label: '22.21 Manufacture of plastic plates, sheets, tubes and profiles',
        value: '22.21',
        indentLevel: 2,
      },
      {
        label: '22.22 Manufacture of plastic packing goods',
        value: '22.22',
        indentLevel: 2,
      },
      {
        label: "22.23 Manufacture of builders' ware of plastic",
        value: '22.23',
        indentLevel: 2,
      },
      {
        label: '22.29 Manufacture of other plastic products',
        value: '22.29',
        indentLevel: 2,
      },
      {
        label: '23 Manufacture of other non-metallic mineral products',
        value: '23',
      },
      {
        label: '23.1 Manufacture of glass and glass products',
        value: '23.1',
        indentLevel: 1,
      },
      {
        label: '23.11 Manufacture of flat glass',
        value: '23.11',
        indentLevel: 2,
      },
      {
        label: '23.12 Shaping and processing of flat glass',
        value: '23.12',
        indentLevel: 2,
      },
      {
        label: '23.13 Manufacture of hollow glass',
        value: '23.13',
        indentLevel: 2,
      },
      {
        label: '23.14 Manufacture of glass fibres',
        value: '23.14',
        indentLevel: 2,
      },
      {
        label: '23.19 Manufacture and processing of other glass, including technical glassware',
        value: '23.19',
        indentLevel: 2,
      },
      {
        label: '23.2 Manufacture of refractory products',
        value: '23.2',
        indentLevel: 1,
      },
      {
        label: '23.20 Manufacture of refractory products',
        value: '23.20',
        indentLevel: 2,
      },
      {
        label: '23.3 Manufacture of clay building materials',
        value: '23.3',
        indentLevel: 1,
      },
      {
        label: '23.31 Manufacture of ceramic tiles and flags',
        value: '23.31',
        indentLevel: 2,
      },
      {
        label: '23.32 Manufacture of bricks, tiles and construction products, in baked clay',
        value: '23.32',
        indentLevel: 2,
      },
      {
        label: '23.4 Manufacture of other porcelain and ceramic products',
        value: '23.4',
        indentLevel: 1,
      },
      {
        label: '23.41 Manufacture of ceramic household and ornamental articles',
        value: '23.41',
        indentLevel: 2,
      },
      {
        label: '23.42 Manufacture of ceramic sanitary fixtures',
        value: '23.42',
        indentLevel: 2,
      },
      {
        label: '23.43 Manufacture of ceramic insulators and insulating fittings',
        value: '23.43',
        indentLevel: 2,
      },
      {
        label: '23.44 Manufacture of other technical ceramic products',
        value: '23.44',
        indentLevel: 2,
      },
      {
        label: '23.49 Manufacture of other ceramic products',
        value: '23.49',
        indentLevel: 2,
      },
      {
        label: '23.5 Manufacture of cement, lime and plaster',
        value: '23.5',
        indentLevel: 1,
      },
      {
        label: '23.51 Manufacture of cement',
        value: '23.51',
        indentLevel: 2,
      },
      {
        label: '23.52 Manufacture of lime and plaster',
        value: '23.52',
        indentLevel: 2,
      },
      {
        label: '23.6 Manufacture of articles of concrete, cement and plaster',
        value: '23.6',
        indentLevel: 1,
      },
      {
        label: '23.61 Manufacture of concrete products for construction purposes',
        value: '23.61',
        indentLevel: 2,
      },
      {
        label: '23.62 Manufacture of plaster products for construction purposes',
        value: '23.62',
        indentLevel: 2,
      },
      {
        label: '23.63 Manufacture of ready-mixed concrete',
        value: '23.63',
        indentLevel: 2,
      },
      {
        label: '23.64 Manufacture of mortars',
        value: '23.64',
        indentLevel: 2,
      },
      {
        label: '23.65 Manufacture of fibre cement',
        value: '23.65',
        indentLevel: 2,
      },
      {
        label: '23.69 Manufacture of other articles of concrete, plaster and cement',
        value: '23.69',
        indentLevel: 2,
      },
      {
        label: '23.7 Cutting, shaping and finishing of stone',
        value: '23.7',
        indentLevel: 1,
      },
      {
        label: '23.70 Cutting, shaping and finishing of stone',
        value: '23.70',
        indentLevel: 2,
      },
      {
        label: '23.9 Manufacture of abrasive products and non-metallic mineral products n.e.c.',
        value: '23.9',
        indentLevel: 1,
      },
      {
        label: '23.91 Production of abrasive products',
        value: '23.91',
        indentLevel: 2,
      },
      {
        label: '23.99 Manufacture of other non-metallic mineral products n.e.c.',
        value: '23.99',
        indentLevel: 2,
      },
      {
        label: '24 Manufacture of basic metals',
        value: '24',
      },
      {
        label: '24.1 Manufacture of basic iron and steel and of ferro-alloys',
        value: '24.1',
        indentLevel: 1,
      },
      {
        label: '24.10 Manufacture of basic iron and steel and of ferro-alloys',
        value: '24.10',
        indentLevel: 2,
      },
      {
        label: '24.2 Manufacture of tubes, pipes, hollow profiles and related fittings, of steel',
        value: '24.2',
        indentLevel: 1,
      },
      {
        label: '24.20 Manufacture of tubes, pipes, hollow profiles and related fittings, of steel',
        value: '24.20',
        indentLevel: 2,
      },
      {
        label: '24.3 Manufacture of other products of first processing of steel',
        value: '24.3',
        indentLevel: 1,
      },
      {
        label: '24.31 Cold drawing of bars',
        value: '24.31',
        indentLevel: 2,
      },
      {
        label: '24.32 Cold rolling of narrow strip',
        value: '24.32',
        indentLevel: 2,
      },
      {
        label: '24.33 Cold forming or folding',
        value: '24.33',
        indentLevel: 2,
      },
      {
        label: '24.34 Cold drawing of wire',
        value: '24.34',
        indentLevel: 2,
      },
      {
        label: '24.4 Manufacture of basic precious and other non-ferrous metals',
        value: '24.4',
        indentLevel: 1,
      },
      {
        label: '24.41 Precious metals production',
        value: '24.41',
        indentLevel: 2,
      },
      {
        label: '24.42 Aluminium production',
        value: '24.42',
        indentLevel: 2,
      },
      {
        label: '24.43 Lead, zinc and tin production',
        value: '24.43',
        indentLevel: 2,
      },
      {
        label: '24.44 Copper production',
        value: '24.44',
        indentLevel: 2,
      },
      {
        label: '24.45 Other non-ferrous metal production',
        value: '24.45',
        indentLevel: 2,
      },
      {
        label: '24.46 Processing of nuclear fuel',
        value: '24.46',
        indentLevel: 2,
      },
      {
        label: '24.5 Casting of metals',
        value: '24.5',
        indentLevel: 1,
      },
      {
        label: '24.51 Casting of iron',
        value: '24.51',
        indentLevel: 2,
      },
      {
        label: '24.52 Casting of steel',
        value: '24.52',
        indentLevel: 2,
      },
      {
        label: '24.53 Casting of light metals',
        value: '24.53',
        indentLevel: 2,
      },
      {
        label: '24.54 Casting of other non-ferrous metals',
        value: '24.54',
        indentLevel: 2,
      },
      {
        label: '25 Manufacture of fabricated metal products, except machinery and equipment',
        value: '25',
      },
      {
        label: '25.1 Manufacture of structural metal products',
        value: '25.1',
        indentLevel: 1,
      },
      {
        label: '25.11 Manufacture of metal structures and parts of structures',
        value: '25.11',
        indentLevel: 2,
      },
      {
        label: '25.12 Manufacture of doors and windows of metal',
        value: '25.12',
        indentLevel: 2,
      },
      {
        label: '25.2 Manufacture of tanks, reservoirs and containers of metal',
        value: '25.2',
        indentLevel: 1,
      },
      {
        label: '25.21 Manufacture of central heating radiators and boilers',
        value: '25.21',
        indentLevel: 2,
      },
      {
        label: '25.29 Manufacture of other tanks, reservoirs and containers of metal',
        value: '25.29',
        indentLevel: 2,
      },
      {
        label: '25.3 Manufacture of steam generators, except central heating hot water boilers',
        value: '25.3',
        indentLevel: 1,
      },
      {
        label: '25.30 Manufacture of steam generators, except central heating hot water boilers',
        value: '25.30',
        indentLevel: 2,
      },
      {
        label: '25.4 Manufacture of weapons and ammunition',
        value: '25.4',
        indentLevel: 1,
      },
      {
        label: '25.40 Manufacture of weapons and ammunition',
        value: '25.40',
        indentLevel: 2,
      },
      {
        label: '25.5 Forging, pressing, stamping and roll-forming of metal; powder metallurgy',
        value: '25.5',
        indentLevel: 1,
      },
      {
        label: '25.50 Forging, pressing, stamping and roll-forming of metal; powder metallurgy',
        value: '25.50',
        indentLevel: 2,
      },
      {
        label: '25.6 Treatment and coating of metals; machining',
        value: '25.6',
        indentLevel: 1,
      },
      {
        label: '25.61 Treatment and coating of metals',
        value: '25.61',
        indentLevel: 2,
      },
      {
        label: '25.62 Machining',
        value: '25.62',
        indentLevel: 2,
      },
      {
        label: '25.7 Manufacture of cutlery, tools and general hardware',
        value: '25.7',
        indentLevel: 1,
      },
      {
        label: '25.71 Manufacture of cutlery',
        value: '25.71',
        indentLevel: 2,
      },
      {
        label: '25.72 Manufacture of locks and hinges',
        value: '25.72',
        indentLevel: 2,
      },
      {
        label: '25.73 Manufacture of tools',
        value: '25.73',
        indentLevel: 2,
      },
      {
        label: '25.9 Manufacture of other fabricated metal products',
        value: '25.9',
        indentLevel: 1,
      },
      {
        label: '25.91 Manufacture of steel drums and similar containers',
        value: '25.91',
        indentLevel: 2,
      },
      {
        label: '25.92 Manufacture of light metal packaging',
        value: '25.92',
        indentLevel: 2,
      },
      {
        label: '25.93 Manufacture of wire products, chain and springs',
        value: '25.93',
        indentLevel: 2,
      },
      {
        label: '25.94 Manufacture of fasteners and screw machine products',
        value: '25.94',
        indentLevel: 2,
      },
      {
        label: '25.99 Manufacture of other fabricated metal products n.e.c.',
        value: '25.99',
        indentLevel: 2,
      },
      {
        label: '26 Manufacture of computer, electronic and optical products',
        value: '26',
      },
      {
        label: '26.1 Manufacture of electronic components and boards',
        value: '26.1',
        indentLevel: 1,
      },
      {
        label: '26.11 Manufacture of electronic components',
        value: '26.11',
        indentLevel: 2,
      },
      {
        label: '26.12 Manufacture of loaded electronic boards',
        value: '26.12',
        indentLevel: 2,
      },
      {
        label: '26.2 Manufacture of computers and peripheral equipment',
        value: '26.2',
        indentLevel: 1,
      },
      {
        label: '26.20 Manufacture of computers and peripheral equipment',
        value: '26.20',
        indentLevel: 2,
      },
      {
        label: '26.3 Manufacture of communication equipment',
        value: '26.3',
        indentLevel: 1,
      },
      {
        label: '26.30 Manufacture of communication equipment',
        value: '26.30',
        indentLevel: 2,
      },
      {
        label: '26.4 Manufacture of consumer electronics',
        value: '26.4',
        indentLevel: 1,
      },
      {
        label: '26.40 Manufacture of consumer electronics',
        value: '26.40',
        indentLevel: 2,
      },
      {
        label:
          '26.5 Manufacture of instruments and appliances for measuring, testing and navigation; watches and clocks',
        value: '26.5',
        indentLevel: 1,
      },
      {
        label: '26.51 Manufacture of instruments and appliances for measuring, testing and navigation',
        value: '26.51',
        indentLevel: 2,
      },
      {
        label: '26.52 Manufacture of watches and clocks',
        value: '26.52',
        indentLevel: 2,
      },
      {
        label: '26.6 Manufacture of irradiation, electromedical and electrotherapeutic equipment',
        value: '26.6',
        indentLevel: 1,
      },
      {
        label: '26.60 Manufacture of irradiation, electromedical and electrotherapeutic equipment',
        value: '26.60',
        indentLevel: 2,
      },
      {
        label: '26.7 Manufacture of optical instruments and photographic equipment',
        value: '26.7',
        indentLevel: 1,
      },
      {
        label: '26.70 Manufacture of optical instruments and photographic equipment',
        value: '26.70',
        indentLevel: 2,
      },
      {
        label: '26.8 Manufacture of magnetic and optical media',
        value: '26.8',
        indentLevel: 1,
      },
      {
        label: '26.80 Manufacture of magnetic and optical media',
        value: '26.80',
        indentLevel: 2,
      },
      {
        label: '27 Manufacture of electrical equipment',
        value: '27',
      },
      {
        label:
          '27.1 Manufacture of electric motors, generators, transformers and electricity distribution and control apparatus',
        value: '27.1',
        indentLevel: 1,
      },
      {
        label: '27.11 Manufacture of electric motors, generators and transformers',
        value: '27.11',
        indentLevel: 2,
      },
      {
        label: '27.12 Manufacture of electricity distribution and control apparatus',
        value: '27.12',
        indentLevel: 2,
      },
      {
        label: '27.2 Manufacture of batteries and accumulators',
        value: '27.2',
        indentLevel: 1,
      },
      {
        label: '27.20 Manufacture of batteries and accumulators',
        value: '27.20',
        indentLevel: 2,
      },
      {
        label: '27.3 Manufacture of wiring and wiring devices',
        value: '27.3',
        indentLevel: 1,
      },
      {
        label: '27.31 Manufacture of fibre optic cables',
        value: '27.31',
        indentLevel: 2,
      },
      {
        label: '27.32 Manufacture of other electronic and electric wires and cables',
        value: '27.32',
        indentLevel: 2,
      },
      {
        label: '27.33 Manufacture of wiring devices',
        value: '27.33',
        indentLevel: 2,
      },
      {
        label: '27.4 Manufacture of electric lighting equipment',
        value: '27.4',
        indentLevel: 1,
      },
      {
        label: '27.40 Manufacture of electric lighting equipment',
        value: '27.40',
        indentLevel: 2,
      },
      {
        label: '27.5 Manufacture of domestic appliances',
        value: '27.5',
        indentLevel: 1,
      },
      {
        label: '27.51 Manufacture of electric domestic appliances',
        value: '27.51',
        indentLevel: 2,
      },
      {
        label: '27.52 Manufacture of non-electric domestic appliances',
        value: '27.52',
        indentLevel: 2,
      },
      {
        label: '27.9 Manufacture of other electrical equipment',
        value: '27.9',
        indentLevel: 1,
      },
      {
        label: '27.90 Manufacture of other electrical equipment',
        value: '27.90',
        indentLevel: 2,
      },
      {
        label: '28 Manufacture of machinery and equipment n.e.c.',
        value: '28',
      },
      {
        label: '28.1 Manufacture of general-purpose machinery',
        value: '28.1',
        indentLevel: 1,
      },
      {
        label: '28.11 Manufacture of engines and turbines, except aircraft, vehicle and cycle engines',
        value: '28.11',
        indentLevel: 2,
      },
      {
        label: '28.12 Manufacture of fluid power equipment',
        value: '28.12',
        indentLevel: 2,
      },
      {
        label: '28.13 Manufacture of other pumps and compressors',
        value: '28.13',
        indentLevel: 2,
      },
      {
        label: '28.14 Manufacture of other taps and valves',
        value: '28.14',
        indentLevel: 2,
      },
      {
        label: '28.15 Manufacture of bearings, gears, gearing and driving elements',
        value: '28.15',
        indentLevel: 2,
      },
      {
        label: '28.2 Manufacture of other general-purpose machinery',
        value: '28.2',
        indentLevel: 1,
      },
      {
        label: '28.21 Manufacture of ovens, furnaces and furnace burners',
        value: '28.21',
        indentLevel: 2,
      },
      {
        label: '28.22 Manufacture of lifting and handling equipment',
        value: '28.22',
        indentLevel: 2,
      },
      {
        label: '28.23 Manufacture of office machinery and equipment (except computers and peripheral equipment)',
        value: '28.23',
        indentLevel: 2,
      },
      {
        label: '28.24 Manufacture of power-driven hand tools',
        value: '28.24',
        indentLevel: 2,
      },
      {
        label: '28.25 Manufacture of non-domestic cooling and ventilation equipment',
        value: '28.25',
        indentLevel: 2,
      },
      {
        label: '28.29 Manufacture of other general-purpose machinery n.e.c.',
        value: '28.29',
        indentLevel: 2,
      },
      {
        label: '28.3 Manufacture of agricultural and forestry machinery',
        value: '28.3',
        indentLevel: 1,
      },
      {
        label: '28.30 Manufacture of agricultural and forestry machinery',
        value: '28.30',
        indentLevel: 2,
      },
      {
        label: '28.4 Manufacture of metal forming machinery and machine tools',
        value: '28.4',
        indentLevel: 1,
      },
      {
        label: '28.41 Manufacture of metal forming machinery',
        value: '28.41',
        indentLevel: 2,
      },
      {
        label: '28.49 Manufacture of other machine tools',
        value: '28.49',
        indentLevel: 2,
      },
      {
        label: '28.9 Manufacture of other special-purpose machinery',
        value: '28.9',
        indentLevel: 1,
      },
      {
        label: '28.91 Manufacture of machinery for metallurgy',
        value: '28.91',
        indentLevel: 2,
      },
      {
        label: '28.92 Manufacture of machinery for mining, quarrying and construction',
        value: '28.92',
        indentLevel: 2,
      },
      {
        label: '28.93 Manufacture of machinery for food, beverage and tobacco processing',
        value: '28.93',
        indentLevel: 2,
      },
      {
        label: '28.94 Manufacture of machinery for textile, apparel and leather production',
        value: '28.94',
        indentLevel: 2,
      },
      {
        label: '28.95 Manufacture of machinery for paper and paperboard production',
        value: '28.95',
        indentLevel: 2,
      },
      {
        label: '28.96 Manufacture of plastics and rubber machinery',
        value: '28.96',
        indentLevel: 2,
      },
      {
        label: '28.99 Manufacture of other special-purpose machinery n.e.c.',
        value: '28.99',
        indentLevel: 2,
      },
      {
        label: '29 Manufacture of motor vehicles, trailers and semi-trailers',
        value: '29',
      },
      {
        label: '29.1 Manufacture of motor vehicles',
        value: '29.1',
        indentLevel: 1,
      },
      {
        label: '29.10 Manufacture of motor vehicles',
        value: '29.10',
        indentLevel: 2,
      },
      {
        label: '29.2 Manufacture of bodies (coachwork) for motor vehicles; manufacture of trailers and semi-trailers',
        value: '29.2',
        indentLevel: 1,
      },
      {
        label: '29.20 Manufacture of bodies (coachwork) for motor vehicles; manufacture of trailers and semi-trailers',
        value: '29.20',
        indentLevel: 2,
      },
      {
        label: '29.3 Manufacture of parts and accessories for motor vehicles',
        value: '29.3',
        indentLevel: 1,
      },
      {
        label: '29.31 Manufacture of electrical and electronic equipment for motor vehicles',
        value: '29.31',
        indentLevel: 2,
      },
      {
        label: '29.32 Manufacture of other parts and accessories for motor vehicles',
        value: '29.32',
        indentLevel: 2,
      },
      {
        label: '30 Manufacture of other transport equipment',
        value: '30',
      },
      {
        label: '30.1 Building of ships and boats',
        value: '30.1',
        indentLevel: 1,
      },
      {
        label: '30.11 Building of ships and floating structures',
        value: '30.11',
        indentLevel: 2,
      },
      {
        label: '30.12 Building of pleasure and sporting boats',
        value: '30.12',
        indentLevel: 2,
      },
      {
        label: '30.2 Manufacture of railway locomotives and rolling stock',
        value: '30.2',
        indentLevel: 1,
      },
      {
        label: '30.20 Manufacture of railway locomotives and rolling stock',
        value: '30.20',
        indentLevel: 2,
      },
      {
        label: '30.3 Manufacture of air and spacecraft and related machinery',
        value: '30.3',
        indentLevel: 1,
      },
      {
        label: '30.30 Manufacture of air and spacecraft and related machinery',
        value: '30.30',
        indentLevel: 2,
      },
      {
        label: '30.4 Manufacture of military fighting vehicles',
        value: '30.4',
        indentLevel: 1,
      },
      {
        label: '30.40 Manufacture of military fighting vehicles',
        value: '30.40',
        indentLevel: 2,
      },
      {
        label: '30.9 Manufacture of transport equipment n.e.c.',
        value: '30.9',
        indentLevel: 1,
      },
      {
        label: '30.91 Manufacture of motorcycles',
        value: '30.91',
        indentLevel: 2,
      },
      {
        label: '30.92 Manufacture of bicycles and invalid carriages',
        value: '30.92',
        indentLevel: 2,
      },
      {
        label: '30.99 Manufacture of other transport equipment n.e.c.',
        value: '30.99',
        indentLevel: 2,
      },
      {
        label: '31 Manufacture of furniture',
        value: '31',
      },
      {
        label: '31.0 Manufacture of furniture',
        value: '31.0',
        indentLevel: 1,
      },
      {
        label: '31.01 Manufacture of office and shop furniture',
        value: '31.01',
        indentLevel: 2,
      },
      {
        label: '31.02 Manufacture of kitchen furniture',
        value: '31.02',
        indentLevel: 2,
      },
      {
        label: '31.03 Manufacture of mattresses',
        value: '31.03',
        indentLevel: 2,
      },
      {
        label: '31.09 Manufacture of other furniture',
        value: '31.09',
        indentLevel: 2,
      },
      {
        label: '32 Other manufacturing',
        value: '32',
      },
      {
        label: '32.1 Manufacture of jewellery, bijouterie and related articles',
        value: '32.1',
        indentLevel: 1,
      },
      {
        label: '32.11 Striking of coins',
        value: '32.11',
        indentLevel: 2,
      },
      {
        label: '32.12 Manufacture of jewellery and related articles',
        value: '32.12',
        indentLevel: 2,
      },
      {
        label: '32.13 Manufacture of imitation jewellery and related articles',
        value: '32.13',
        indentLevel: 2,
      },
      {
        label: '32.2 Manufacture of musical instruments',
        value: '32.2',
        indentLevel: 1,
      },
      {
        label: '32.20 Manufacture of musical instruments',
        value: '32.20',
        indentLevel: 2,
      },
      {
        label: '32.3 Manufacture of sports goods',
        value: '32.3',
        indentLevel: 1,
      },
      {
        label: '32.30 Manufacture of sports goods',
        value: '32.30',
        indentLevel: 2,
      },
      {
        label: '32.4 Manufacture of games and toys',
        value: '32.4',
        indentLevel: 1,
      },
      {
        label: '32.40 Manufacture of games and toys',
        value: '32.40',
        indentLevel: 2,
      },
      {
        label: '32.5 Manufacture of medical and dental instruments and supplies',
        value: '32.5',
        indentLevel: 1,
      },
      {
        label: '32.50 Manufacture of medical and dental instruments and supplies',
        value: '32.50',
        indentLevel: 2,
      },
      {
        label: '32.9 Manufacturing n.e.c.',
        value: '32.9',
        indentLevel: 1,
      },
      {
        label: '32.91 Manufacture of brooms and brushes',
        value: '32.91',
        indentLevel: 2,
      },
      {
        label: '32.99 Other manufacturing n.e.c.',
        value: '32.99',
        indentLevel: 2,
      },
      {
        label: '33 Repair and installation of machinery and equipment',
        value: '33',
      },
      {
        label: '33.1 Repair of fabricated metal products, machinery and equipment',
        value: '33.1',
        indentLevel: 1,
      },
      {
        label: '33.11 Repair of fabricated metal products',
        value: '33.11',
        indentLevel: 2,
      },
      {
        label: '33.12 Repair of machinery',
        value: '33.12',
        indentLevel: 2,
      },
      {
        label: '33.13 Repair of electronic and optical equipment',
        value: '33.13',
        indentLevel: 2,
      },
      {
        label: '33.14 Repair of electrical equipment',
        value: '33.14',
        indentLevel: 2,
      },
      {
        label: '33.15 Repair and maintenance of ships and boats',
        value: '33.15',
        indentLevel: 2,
      },
      {
        label: '33.16 Repair and maintenance of aircraft and spacecraft',
        value: '33.16',
        indentLevel: 2,
      },
      {
        label: '33.17 Repair and maintenance of other transport equipment',
        value: '33.17',
        indentLevel: 2,
      },
      {
        label: '33.19 Repair of other equipment',
        value: '33.19',
        indentLevel: 2,
      },
      {
        label: '33.2 Installation of industrial machinery and equipment',
        value: '33.2',
        indentLevel: 1,
      },
      {
        label: '33.20 Installation of industrial machinery and equipment',
        value: '33.20',
        indentLevel: 2,
      },
    ],
  },
  {
    label: 'D - ELECTRICITY, GAS, STEAM AND AIR CONDITIONING SUPPLY',
    options: [
      {
        label: '35 Electricity, gas, steam and air conditioning supply',
        value: '35',
      },
      {
        label: '35.1 Electric power generation, transmission and distribution',
        value: '35.1',
        indentLevel: 1,
      },
      {
        label: '35.11 Production of electricity',
        value: '35.11',
        indentLevel: 2,
      },
      {
        label: '35.12 Transmission of electricity',
        value: '35.12',
        indentLevel: 2,
      },
      {
        label: '35.13 Distribution of electricity',
        value: '35.13',
        indentLevel: 2,
      },
      {
        label: '35.14 Trade of electricity',
        value: '35.14',
        indentLevel: 2,
      },
      {
        label: '35.2 Manufacture of gas; distribution of gaseous fuels through mains',
        value: '35.2',
        indentLevel: 1,
      },
      {
        label: '35.21 Manufacture of gas',
        value: '35.21',
        indentLevel: 2,
      },
      {
        label: '35.22 Distribution of gaseous fuels through mains',
        value: '35.22',
        indentLevel: 2,
      },
      {
        label: '35.23 Trade of gas through mains',
        value: '35.23',
        indentLevel: 2,
      },
      {
        label: '35.3 Steam and air conditioning supply',
        value: '35.3',
        indentLevel: 1,
      },
      {
        label: '35.30 Steam and air conditioning supply',
        value: '35.30',
        indentLevel: 2,
      },
    ],
  },
  {
    label: 'E - WATER SUPPLY; SEWERAGE, WASTE MANAGEMENT AND REMEDIATION ACTIVITIES',
    options: [
      {
        label: '36 Water collection, treatment and supply',
        value: '36',
      },
      {
        label: '36.0 Water collection, treatment and supply',
        value: '36.0',
        indentLevel: 1,
      },
      {
        label: '36.00 Water collection, treatment and supply',
        value: '36.00',
        indentLevel: 2,
      },
      {
        label: '37 Sewerage',
        value: '37',
      },
      {
        label: '37.0 Sewerage',
        value: '37.0',
        indentLevel: 1,
      },
      {
        label: '37.00 Sewerage',
        value: '37.00',
        indentLevel: 2,
      },
      {
        label: '38 Waste collection, treatment and disposal activities; materials recovery',
        value: '38',
      },
      {
        label: '38.1 Waste collection',
        value: '38.1',
        indentLevel: 1,
      },
      {
        label: '38.11 Collection of non-hazardous waste',
        value: '38.11',
        indentLevel: 2,
      },
      {
        label: '38.12 Collection of hazardous waste',
        value: '38.12',
        indentLevel: 2,
      },
      {
        label: '38.2 Waste treatment and disposal',
        value: '38.2',
        indentLevel: 1,
      },
      {
        label: '38.21 Treatment and disposal of non-hazardous waste',
        value: '38.21',
        indentLevel: 2,
      },
      {
        label: '38.22 Treatment and disposal of hazardous waste',
        value: '38.22',
        indentLevel: 2,
      },
      {
        label: '38.3 Materials recovery',
        value: '38.3',
        indentLevel: 1,
      },
      {
        label: '38.31 Dismantling of wrecks',
        value: '38.31',
        indentLevel: 2,
      },
      {
        label: '38.32 Recovery of sorted materials',
        value: '38.32',
        indentLevel: 2,
      },
      {
        label: '39 Remediation activities and other waste management services',
        value: '39',
      },
      {
        label: '39.0 Remediation activities and other waste management services',
        value: '39.0',
        indentLevel: 1,
      },
      {
        label: '39.00 Remediation activities and other waste management services',
        value: '39.00',
        indentLevel: 2,
      },
    ],
  },
  {
    label: 'F - CONSTRUCTION',
    options: [
      {
        label: '41 Construction of buildings',
        value: '41',
      },
      {
        label: '41.1 Development of building projects',
        value: '41.1',
        indentLevel: 1,
      },
      {
        label: '41.10 Development of building projects',
        value: '41.10',
        indentLevel: 2,
      },
      {
        label: '41.2 Construction of residential and non-residential buildings',
        value: '41.2',
        indentLevel: 1,
      },
      {
        label: '41.20 Construction of residential and non-residential buildings',
        value: '41.20',
        indentLevel: 2,
      },
      {
        label: '42 Civil engineering',
        value: '42',
      },
      {
        label: '42.1 Construction of roads and railways',
        value: '42.1',
        indentLevel: 1,
      },
      {
        label: '42.11 Construction of roads and motorways',
        value: '42.11',
        indentLevel: 2,
      },
      {
        label: '42.12 Construction of railways and underground railways',
        value: '42.12',
        indentLevel: 2,
      },
      {
        label: '42.13 Construction of bridges and tunnels',
        value: '42.13',
        indentLevel: 2,
      },
      {
        label: '42.2 Construction of utility projects',
        value: '42.2',
        indentLevel: 1,
      },
      {
        label: '42.21 Construction of utility projects for fluids',
        value: '42.21',
        indentLevel: 2,
      },
      {
        label: '42.22 Construction of utility projects for electricity and telecommunications',
        value: '42.22',
        indentLevel: 2,
      },
      {
        label: '42.9 Construction of other civil engineering projects',
        value: '42.9',
        indentLevel: 1,
      },
      {
        label: '42.91 Construction of water projects',
        value: '42.91',
        indentLevel: 2,
      },
      {
        label: '42.99 Construction of other civil engineering projects n.e.c.',
        value: '42.99',
        indentLevel: 2,
      },
      {
        label: '43 Specialised construction activities',
        value: '43',
      },
      {
        label: '43.1 Demolition and site preparation',
        value: '43.1',
        indentLevel: 1,
      },
      {
        label: '43.11 Demolition',
        value: '43.11',
        indentLevel: 2,
      },
      {
        label: '43.12 Site preparation',
        value: '43.12',
        indentLevel: 2,
      },
      {
        label: '43.13 Test drilling and boring',
        value: '43.13',
        indentLevel: 2,
      },
      {
        label: '43.2 Electrical, plumbing and other construction installation activities',
        value: '43.2',
        indentLevel: 1,
      },
      {
        label: '43.21 Electrical installation',
        value: '43.21',
        indentLevel: 2,
      },
      {
        label: '43.22 Plumbing, heat and air-conditioning installation',
        value: '43.22',
        indentLevel: 2,
      },
      {
        label: '43.29 Other construction installation',
        value: '43.29',
        indentLevel: 2,
      },
      {
        label: '43.3 Building completion and finishing',
        value: '43.3',
        indentLevel: 1,
      },
      {
        label: '43.31 Plastering',
        value: '43.31',
        indentLevel: 2,
      },
      {
        label: '43.32 Joinery installation',
        value: '43.32',
        indentLevel: 2,
      },
      {
        label: '43.33 Floor and wall covering',
        value: '43.33',
        indentLevel: 2,
      },
      {
        label: '43.34 Painting and glazing',
        value: '43.34',
        indentLevel: 2,
      },
      {
        label: '43.39 Other building completion and finishing',
        value: '43.39',
        indentLevel: 2,
      },
      {
        label: '43.9 Other specialised construction activities',
        value: '43.9',
        indentLevel: 1,
      },
      {
        label: '43.91 Roofing activities',
        value: '43.91',
        indentLevel: 2,
      },
      {
        label: '43.99 Other specialised construction activities n.e.c.',
        value: '43.99',
        indentLevel: 2,
      },
    ],
  },
  {
    label: 'G - WHOLESALE AND RETAIL TRADE; REPAIR OF MOTOR VEHICLES AND MOTORCYCLES',
    options: [
      {
        label: '45 Wholesale and retail trade and repair of motor vehicles and motorcycles',
        value: '45',
      },
      {
        label: '45.1 Sale of motor vehicles',
        value: '45.1',
        indentLevel: 1,
      },
      {
        label: '45.11 Sale of cars and light motor vehicles',
        value: '45.11',
        indentLevel: 2,
      },
      {
        label: '45.19 Sale of other motor vehicles',
        value: '45.19',
        indentLevel: 2,
      },
      {
        label: '45.2 Maintenance and repair of motor vehicles',
        value: '45.2',
        indentLevel: 1,
      },
      {
        label: '45.20 Maintenance and repair of motor vehicles',
        value: '45.20',
        indentLevel: 2,
      },
      {
        label: '45.3 Sale of motor vehicle parts and accessories',
        value: '45.3',
        indentLevel: 1,
      },
      {
        label: '45.31 Wholesale trade of motor vehicle parts and accessories',
        value: '45.31',
        indentLevel: 2,
      },
      {
        label: '45.32 Retail trade of motor vehicle parts and accessories',
        value: '45.32',
        indentLevel: 2,
      },
      {
        label: '45.4 Sale, maintenance and repair of motorcycles and related parts and accessories',
        value: '45.4',
        indentLevel: 1,
      },
      {
        label: '45.40 Sale, maintenance and repair of motorcycles and related parts and accessories',
        value: '45.40',
        indentLevel: 2,
      },
      {
        label: '46 Wholesale trade, except of motor vehicles and motorcycles',
        value: '46',
      },
      {
        label: '46.1 Wholesale on a fee or contract basis',
        value: '46.1',
        indentLevel: 1,
      },
      {
        label:
          '46.11 Agents involved in the sale of agricultural raw materials, live animals, textile raw materials and semi-finished goods',
        value: '46.11',
        indentLevel: 2,
      },
      {
        label: '46.12 Agents involved in the sale of fuels, ores, metals and industrial chemicals',
        value: '46.12',
        indentLevel: 2,
      },
      {
        label: '46.13 Agents involved in the sale of timber and building materials',
        value: '46.13',
        indentLevel: 2,
      },
      {
        label: '46.14 Agents involved in the sale of machinery, industrial equipment, ships and aircraft',
        value: '46.14',
        indentLevel: 2,
      },
      {
        label: '46.15 Agents involved in the sale of furniture, household goods, hardware and ironmongery',
        value: '46.15',
        indentLevel: 2,
      },
      {
        label: '46.16 Agents involved in the sale of textiles, clothing, fur, footwear and leather goods',
        value: '46.16',
        indentLevel: 2,
      },
      {
        label: '46.17 Agents involved in the sale of food, beverages and tobacco',
        value: '46.17',
        indentLevel: 2,
      },
      {
        label: '46.18 Agents specialised in the sale of other particular products',
        value: '46.18',
        indentLevel: 2,
      },
      {
        label: '46.19 Agents involved in the sale of a variety of goods',
        value: '46.19',
        indentLevel: 2,
      },
      {
        label: '46.2 Wholesale of agricultural raw materials and live animals',
        value: '46.2',
        indentLevel: 1,
      },
      {
        label: '46.21 Wholesale of grain, unmanufactured tobacco, seeds and animal feeds',
        value: '46.21',
        indentLevel: 2,
      },
      {
        label: '46.22 Wholesale of flowers and plants',
        value: '46.22',
        indentLevel: 2,
      },
      {
        label: '46.23 Wholesale of live animals',
        value: '46.23',
        indentLevel: 2,
      },
      {
        label: '46.24 Wholesale of hides, skins and leather',
        value: '46.24',
        indentLevel: 2,
      },
      {
        label: '46.3 Wholesale of food, beverages and tobacco',
        value: '46.3',
        indentLevel: 1,
      },
      {
        label: '46.31 Wholesale of fruit and vegetables',
        value: '46.31',
        indentLevel: 2,
      },
      {
        label: '46.32 Wholesale of meat and meat products',
        value: '46.32',
        indentLevel: 2,
      },
      {
        label: '46.33 Wholesale of dairy products, eggs and edible oils and fats',
        value: '46.33',
        indentLevel: 2,
      },
      {
        label: '46.34 Wholesale of beverages',
        value: '46.34',
        indentLevel: 2,
      },
      {
        label: '46.35 Wholesale of tobacco products',
        value: '46.35',
        indentLevel: 2,
      },
      {
        label: '46.36 Wholesale of sugar and chocolate and sugar confectionery',
        value: '46.36',
        indentLevel: 2,
      },
      {
        label: '46.37 Wholesale of coffee, tea, cocoa and spices',
        value: '46.37',
        indentLevel: 2,
      },
      {
        label: '46.38 Wholesale of other food, including fish, crustaceans and molluscs',
        value: '46.38',
        indentLevel: 2,
      },
      {
        label: '46.39 Non-specialised wholesale of food, beverages and tobacco',
        value: '46.39',
        indentLevel: 2,
      },
      {
        label: '46.4 Wholesale of household goods',
        value: '46.4',
        indentLevel: 1,
      },
      {
        label: '46.41 Wholesale of textiles',
        value: '46.41',
        indentLevel: 2,
      },
      {
        label: '46.42 Wholesale of clothing and footwear',
        value: '46.42',
        indentLevel: 2,
      },
      {
        label: '46.43 Wholesale of electrical household appliances',
        value: '46.43',
        indentLevel: 2,
      },
      {
        label: '46.44 Wholesale of china and glassware and cleaning materials',
        value: '46.44',
        indentLevel: 2,
      },
      {
        label: '46.45 Wholesale of perfume and cosmetics',
        value: '46.45',
        indentLevel: 2,
      },
      {
        label: '46.46 Wholesale of pharmaceutical goods',
        value: '46.46',
        indentLevel: 2,
      },
      {
        label: '46.47 Wholesale of furniture, carpets and lighting equipment',
        value: '46.47',
        indentLevel: 2,
      },
      {
        label: '46.48 Wholesale of watches and jewellery',
        value: '46.48',
        indentLevel: 2,
      },
      {
        label: '46.49 Wholesale of other household goods',
        value: '46.49',
        indentLevel: 2,
      },
      {
        label: '46.5 Wholesale of information and communication equipment',
        value: '46.5',
        indentLevel: 1,
      },
      {
        label: '46.51 Wholesale of computers, computer peripheral equipment and software',
        value: '46.51',
        indentLevel: 2,
      },
      {
        label: '46.52 Wholesale of electronic and telecommunications equipment and parts',
        value: '46.52',
        indentLevel: 2,
      },
      {
        label: '46.6 Wholesale of other machinery, equipment and supplies',
        value: '46.6',
        indentLevel: 1,
      },
      {
        label: '46.61 Wholesale of agricultural machinery, equipment and supplies',
        value: '46.61',
        indentLevel: 2,
      },
      {
        label: '46.62 Wholesale of machine tools',
        value: '46.62',
        indentLevel: 2,
      },
      {
        label: '46.63 Wholesale of mining, construction and civil engineering machinery',
        value: '46.63',
        indentLevel: 2,
      },
      {
        label: '46.64 Wholesale of machinery for the textile industry and of sewing and knitting machines',
        value: '46.64',
        indentLevel: 2,
      },
      {
        label: '46.65 Wholesale of office furniture',
        value: '46.65',
        indentLevel: 2,
      },
      {
        label: '46.66 Wholesale of other office machinery and equipment',
        value: '46.66',
        indentLevel: 2,
      },
      {
        label: '46.69 Wholesale of other machinery and equipment',
        value: '46.69',
        indentLevel: 2,
      },
      {
        label: '46.7 Other specialised wholesale',
        value: '46.7',
        indentLevel: 1,
      },
      {
        label: '46.71 Wholesale of solid, liquid and gaseous fuels and related products',
        value: '46.71',
        indentLevel: 2,
      },
      {
        label: '46.72 Wholesale of metals and metal ores',
        value: '46.72',
        indentLevel: 2,
      },
      {
        label: '46.73 Wholesale of wood, construction materials and sanitary equipment',
        value: '46.73',
        indentLevel: 2,
      },
      {
        label: '46.74 Wholesale of hardware, plumbing and heating equipment and supplies',
        value: '46.74',
        indentLevel: 2,
      },
      {
        label: '46.75 Wholesale of chemical products',
        value: '46.75',
        indentLevel: 2,
      },
      {
        label: '46.76 Wholesale of other intermediate products',
        value: '46.76',
        indentLevel: 2,
      },
      {
        label: '46.77 Wholesale of waste and scrap',
        value: '46.77',
        indentLevel: 2,
      },
      {
        label: '46.9 Non-specialised wholesale trade',
        value: '46.9',
        indentLevel: 1,
      },
      {
        label: '46.90 Non-specialised wholesale trade',
        value: '46.90',
        indentLevel: 2,
      },
      {
        label: '47 Retail trade, except of motor vehicles and motorcycles',
        value: '47',
      },
      {
        label: '47.1 Retail sale in non-specialised stores',
        value: '47.1',
        indentLevel: 1,
      },
      {
        label: '47.11 Retail sale in non-specialised stores with food, beverages or tobacco predominating',
        value: '47.11',
        indentLevel: 2,
      },
      {
        label: '47.19 Other retail sale in non-specialised stores',
        value: '47.19',
        indentLevel: 2,
      },
      {
        label: '47.2 Retail sale of food, beverages and tobacco in specialised stores',
        value: '47.2',
        indentLevel: 1,
      },
      {
        label: '47.21 Retail sale of fruit and vegetables in specialised stores',
        value: '47.21',
        indentLevel: 2,
      },
      {
        label: '47.22 Retail sale of meat and meat products in specialised stores',
        value: '47.22',
        indentLevel: 2,
      },
      {
        label: '47.23 Retail sale of fish, crustaceans and molluscs in specialised stores',
        value: '47.23',
        indentLevel: 2,
      },
      {
        label: '47.24 Retail sale of bread, cakes, flour confectionery and sugar confectionery in specialised stores',
        value: '47.24',
        indentLevel: 2,
      },
      {
        label: '47.25 Retail sale of beverages in specialised stores',
        value: '47.25',
        indentLevel: 2,
      },
      {
        label: '47.26 Retail sale of tobacco products in specialised stores',
        value: '47.26',
        indentLevel: 2,
      },
      {
        label: '47.29 Other retail sale of food in specialised stores',
        value: '47.29',
        indentLevel: 2,
      },
      {
        label: '47.3 Retail sale of automotive fuel in specialised stores',
        value: '47.3',
        indentLevel: 1,
      },
      {
        label: '47.30 Retail sale of automotive fuel in specialised stores',
        value: '47.30',
        indentLevel: 2,
      },
      {
        label: '47.4 Retail sale of information and communication equipment in specialised stores',
        value: '47.4',
        indentLevel: 1,
      },
      {
        label: '47.41 Retail sale of computers, peripheral units and software in specialised stores',
        value: '47.41',
        indentLevel: 2,
      },
      {
        label: '47.42 Retail sale of telecommunications equipment in specialised stores',
        value: '47.42',
        indentLevel: 2,
      },
      {
        label: '47.43 Retail sale of audio and video equipment in specialised stores',
        value: '47.43',
        indentLevel: 2,
      },
      {
        label: '47.5 Retail sale of other household equipment in specialised stores',
        value: '47.5',
        indentLevel: 1,
      },
      {
        label: '47.51 Retail sale of textiles in specialised stores',
        value: '47.51',
        indentLevel: 2,
      },
      {
        label: '47.52 Retail sale of hardware, paints and glass in specialised stores',
        value: '47.52',
        indentLevel: 2,
      },
      {
        label: '47.53 Retail sale of carpets, rugs, wall and floor coverings in specialised stores',
        value: '47.53',
        indentLevel: 2,
      },
      {
        label: '47.54 Retail sale of electrical household appliances in specialised stores',
        value: '47.54',
        indentLevel: 2,
      },
      {
        label: '47.59 Retail sale of furniture, lighting equipment and other household articles in specialised stores',
        value: '47.59',
        indentLevel: 2,
      },
      {
        label: '47.6 Retail sale of cultural and recreation goods in specialised stores',
        value: '47.6',
        indentLevel: 1,
      },
      {
        label: '47.61 Retail sale of books in specialised stores',
        value: '47.61',
        indentLevel: 2,
      },
      {
        label: '47.62 Retail sale of newspapers and stationery in specialised stores',
        value: '47.62',
        indentLevel: 2,
      },
      {
        label: '47.63 Retail sale of music and video recordings in specialised stores',
        value: '47.63',
        indentLevel: 2,
      },
      {
        label: '47.64 Retail sale of sporting equipment in specialised stores',
        value: '47.64',
        indentLevel: 2,
      },
      {
        label: '47.65 Retail sale of games and toys in specialised stores',
        value: '47.65',
        indentLevel: 2,
      },
      {
        label: '47.7 Retail sale of other goods in specialised stores',
        value: '47.7',
        indentLevel: 1,
      },
      {
        label: '47.71 Retail sale of clothing in specialised stores',
        value: '47.71',
        indentLevel: 2,
      },
      {
        label: '47.72 Retail sale of footwear and leather goods in specialised stores',
        value: '47.72',
        indentLevel: 2,
      },
      {
        label: '47.73 Dispensing chemist in specialised stores',
        value: '47.73',
        indentLevel: 2,
      },
      {
        label: '47.74 Retail sale of medical and orthopaedic goods in specialised stores',
        value: '47.74',
        indentLevel: 2,
      },
      {
        label: '47.75 Retail sale of cosmetic and toilet articles in specialised stores',
        value: '47.75',
        indentLevel: 2,
      },
      {
        label:
          '47.76 Retail sale of flowers, plants, seeds, fertilisers, pet animals and pet food in specialised stores',
        value: '47.76',
        indentLevel: 2,
      },
      {
        label: '47.77 Retail sale of watches and jewellery in specialised stores',
        value: '47.77',
        indentLevel: 2,
      },
      {
        label: '47.78 Other retail sale of new goods in specialised stores',
        value: '47.78',
        indentLevel: 2,
      },
      {
        label: '47.79 Retail sale of second-hand goods in stores',
        value: '47.79',
        indentLevel: 2,
      },
      {
        label: '47.8 Retail sale via stalls and markets',
        value: '47.8',
        indentLevel: 1,
      },
      {
        label: '47.81 Retail sale via stalls and markets of food, beverages and tobacco products',
        value: '47.81',
        indentLevel: 2,
      },
      {
        label: '47.82 Retail sale via stalls and markets of textiles, clothing and footwear',
        value: '47.82',
        indentLevel: 2,
      },
      {
        label: '47.89 Retail sale via stalls and markets of other goods',
        value: '47.89',
        indentLevel: 2,
      },
      {
        label: '47.9 Retail trade not in stores, stalls or markets',
        value: '47.9',
        indentLevel: 1,
      },
      {
        label: '47.91 Retail sale via mail order houses or via Internet',
        value: '47.91',
        indentLevel: 2,
      },
      {
        label: '47.99 Other retail sale not in stores, stalls or markets',
        value: '47.99',
        indentLevel: 2,
      },
    ],
  },
  {
    label: 'H - TRANSPORTATION AND STORAGE',
    options: [
      {
        label: '49 Land transport and transport via pipelines',
        value: '49',
      },
      {
        label: '49.1 Passenger rail transport, interurban',
        value: '49.1',
        indentLevel: 1,
      },
      {
        label: '49.10 Passenger rail transport, interurban',
        value: '49.10',
        indentLevel: 2,
      },
      {
        label: '49.2 Freight rail transport',
        value: '49.2',
        indentLevel: 1,
      },
      {
        label: '49.20 Freight rail transport',
        value: '49.20',
        indentLevel: 2,
      },
      {
        label: '49.3 Other passenger land transport',
        value: '49.3',
        indentLevel: 1,
      },
      {
        label: '49.31 Urban and suburban passenger land transport',
        value: '49.31',
        indentLevel: 2,
      },
      {
        label: '49.32 Taxi operation',
        value: '49.32',
        indentLevel: 2,
      },
      {
        label: '49.39 Other passenger land transport n.e.c.',
        value: '49.39',
        indentLevel: 2,
      },
      {
        label: '49.4 Freight transport by road and removal services',
        value: '49.4',
        indentLevel: 1,
      },
      {
        label: '49.41 Freight transport by road',
        value: '49.41',
        indentLevel: 2,
      },
      {
        label: '49.42 Removal services',
        value: '49.42',
        indentLevel: 2,
      },
      {
        label: '49.5 Transport via pipeline',
        value: '49.5',
        indentLevel: 1,
      },
      {
        label: '49.50 Transport via pipeline',
        value: '49.50',
        indentLevel: 2,
      },
      {
        label: '50 Water transport',
        value: '50',
      },
      {
        label: '50.1 Sea and coastal passenger water transport',
        value: '50.1',
        indentLevel: 1,
      },
      {
        label: '50.10 Sea and coastal passenger water transport',
        value: '50.10',
        indentLevel: 2,
      },
      {
        label: '50.2 Sea and coastal freight water transport',
        value: '50.2',
        indentLevel: 1,
      },
      {
        label: '50.20 Sea and coastal freight water transport',
        value: '50.20',
        indentLevel: 2,
      },
      {
        label: '50.3 Inland passenger water transport',
        value: '50.3',
        indentLevel: 1,
      },
      {
        label: '50.30 Inland passenger water transport',
        value: '50.30',
        indentLevel: 2,
      },
      {
        label: '50.4 Inland freight water transport',
        value: '50.4',
        indentLevel: 1,
      },
      {
        label: '50.40 Inland freight water transport',
        value: '50.40',
        indentLevel: 2,
      },
      {
        label: '51 Air transport',
        value: '51',
      },
      {
        label: '51.1 Passenger air transport',
        value: '51.1',
        indentLevel: 1,
      },
      {
        label: '51.10 Passenger air transport',
        value: '51.10',
        indentLevel: 2,
      },
      {
        label: '51.2 Freight air transport and space transport',
        value: '51.2',
        indentLevel: 1,
      },
      {
        label: '51.21 Freight air transport',
        value: '51.21',
        indentLevel: 2,
      },
      {
        label: '51.22 Space transport',
        value: '51.22',
        indentLevel: 2,
      },
      {
        label: '52 Warehousing and support activities for transportation',
        value: '52',
      },
      {
        label: '52.1 Warehousing and storage',
        value: '52.1',
        indentLevel: 1,
      },
      {
        label: '52.10 Warehousing and storage',
        value: '52.10',
        indentLevel: 2,
      },
      {
        label: '52.2 Support activities for transportation',
        value: '52.2',
        indentLevel: 1,
      },
      {
        label: '52.21 Service activities incidental to land transportation',
        value: '52.21',
        indentLevel: 2,
      },
      {
        label: '52.22 Service activities incidental to water transportation',
        value: '52.22',
        indentLevel: 2,
      },
      {
        label: '52.23 Service activities incidental to air transportation',
        value: '52.23',
        indentLevel: 2,
      },
      {
        label: '52.24 Cargo handling',
        value: '52.24',
        indentLevel: 2,
      },
      {
        label: '52.29 Other transportation support activities',
        value: '52.29',
        indentLevel: 2,
      },
      {
        label: '53 Postal and courier activities',
        value: '53',
      },
      {
        label: '53.1 Postal activities under universal service obligation',
        value: '53.1',
        indentLevel: 1,
      },
      {
        label: '53.10 Postal activities under universal service obligation',
        value: '53.10',
        indentLevel: 2,
      },
      {
        label: '53.2 Other postal and courier activities',
        value: '53.2',
        indentLevel: 1,
      },
      {
        label: '53.20 Other postal and courier activities',
        value: '53.20',
        indentLevel: 2,
      },
    ],
  },
  {
    label: 'I - ACCOMMODATION AND FOOD SERVICE ACTIVITIES',
    options: [
      {
        label: '55 Accommodation',
        value: '55',
      },
      {
        label: '55.1 Hotels and similar accommodation',
        value: '55.1',
        indentLevel: 1,
      },
      {
        label: '55.10 Hotels and similar accommodation',
        value: '55.10',
        indentLevel: 2,
      },
      {
        label: '55.2 Holiday and other short-stay accommodation',
        value: '55.2',
        indentLevel: 1,
      },
      {
        label: '55.20 Holiday and other short-stay accommodation',
        value: '55.20',
        indentLevel: 2,
      },
      {
        label: '55.3 Camping grounds, recreational vehicle parks and trailer parks',
        value: '55.3',
        indentLevel: 1,
      },
      {
        label: '55.30 Camping grounds, recreational vehicle parks and trailer parks',
        value: '55.30',
        indentLevel: 2,
      },
      {
        label: '55.9 Other accommodation',
        value: '55.9',
        indentLevel: 1,
      },
      {
        label: '55.90 Other accommodation',
        value: '55.90',
        indentLevel: 2,
      },
      {
        label: '56 Food and beverage service activities',
        value: '56',
      },
      {
        label: '56.1 Restaurants and mobile food service activities',
        value: '56.1',
        indentLevel: 1,
      },
      {
        label: '56.10 Restaurants and mobile food service activities',
        value: '56.10',
        indentLevel: 2,
      },
      {
        label: '56.2 Event catering and other food service activities',
        value: '56.2',
        indentLevel: 1,
      },
      {
        label: '56.21 Event catering activities',
        value: '56.21',
        indentLevel: 2,
      },
      {
        label: '56.29 Other food service activities',
        value: '56.29',
        indentLevel: 2,
      },
      {
        label: '56.3 Beverage serving activities',
        value: '56.3',
        indentLevel: 1,
      },
      {
        label: '56.30 Beverage serving activities',
        value: '56.30',
        indentLevel: 2,
      },
    ],
  },
  {
    label: 'J - INFORMATION AND COMMUNICATION',
    options: [
      {
        label: '58 Publishing activities',
        value: '58',
      },
      {
        label: '58.1 Publishing of books, periodicals and other publishing activities',
        value: '58.1',
        indentLevel: 1,
      },
      {
        label: '58.11 Book publishing',
        value: '58.11',
        indentLevel: 2,
      },
      {
        label: '58.12 Publishing of directories and mailing lists',
        value: '58.12',
        indentLevel: 2,
      },
      {
        label: '58.13 Publishing of newspapers',
        value: '58.13',
        indentLevel: 2,
      },
      {
        label: '58.14 Publishing of journals and periodicals',
        value: '58.14',
        indentLevel: 2,
      },
      {
        label: '58.19 Other publishing activities',
        value: '58.19',
        indentLevel: 2,
      },
      {
        label: '58.2 Software publishing',
        value: '58.2',
        indentLevel: 1,
      },
      {
        label: '58.21 Publishing of computer games',
        value: '58.21',
        indentLevel: 2,
      },
      {
        label: '58.29 Other software publishing',
        value: '58.29',
        indentLevel: 2,
      },
      {
        label:
          '59 Motion picture, video and television programme production, sound recording and music publishing activities',
        value: '59',
      },
      {
        label: '59.1 Motion picture, video and television programme activities',
        value: '59.1',
        indentLevel: 1,
      },
      {
        label: '59.11 Motion picture, video and television programme production activities',
        value: '59.11',
        indentLevel: 2,
      },
      {
        label: '59.12 Motion picture, video and television programme post-production activities',
        value: '59.12',
        indentLevel: 2,
      },
      {
        label: '59.13 Motion picture, video and television programme distribution activities',
        value: '59.13',
        indentLevel: 2,
      },
      {
        label: '59.14 Motion picture projection activities',
        value: '59.14',
        indentLevel: 2,
      },
      {
        label: '59.2 Sound recording and music publishing activities',
        value: '59.2',
        indentLevel: 1,
      },
      {
        label: '59.20 Sound recording and music publishing activities',
        value: '59.20',
        indentLevel: 2,
      },
      {
        label: '60 Programming and broadcasting activities',
        value: '60',
      },
      {
        label: '60.1 Radio broadcasting',
        value: '60.1',
        indentLevel: 1,
      },
      {
        label: '60.10 Radio broadcasting',
        value: '60.10',
        indentLevel: 2,
      },
      {
        label: '60.2 Television programming and broadcasting activities',
        value: '60.2',
        indentLevel: 1,
      },
      {
        label: '60.20 Television programming and broadcasting activities',
        value: '60.20',
        indentLevel: 2,
      },
      {
        label: '61 Telecommunications',
        value: '61',
      },
      {
        label: '61.1 Wired telecommunications activities',
        value: '61.1',
        indentLevel: 1,
      },
      {
        label: '61.10 Wired telecommunications activities',
        value: '61.10',
        indentLevel: 2,
      },
      {
        label: '61.2 Wireless telecommunications activities',
        value: '61.2',
        indentLevel: 1,
      },
      {
        label: '61.20 Wireless telecommunications activities',
        value: '61.20',
        indentLevel: 2,
      },
      {
        label: '61.3 Satellite telecommunications activities',
        value: '61.3',
        indentLevel: 1,
      },
      {
        label: '61.30 Satellite telecommunications activities',
        value: '61.30',
        indentLevel: 2,
      },
      {
        label: '61.9 Other telecommunications activities',
        value: '61.9',
        indentLevel: 1,
      },
      {
        label: '61.90 Other telecommunications activities',
        value: '61.90',
        indentLevel: 2,
      },
      {
        label: '62 Computer programming, consultancy and related activities',
        value: '62',
      },
      {
        label: '62.0 Computer programming, consultancy and related activities',
        value: '62.0',
        indentLevel: 1,
      },
      {
        label: '62.01 Computer programming activities',
        value: '62.01',
        indentLevel: 2,
      },
      {
        label: '62.02 Computer consultancy activities',
        value: '62.02',
        indentLevel: 2,
      },
      {
        label: '62.03 Computer facilities management activities',
        value: '62.03',
        indentLevel: 2,
      },
      {
        label: '62.09 Other information technology and computer service activities',
        value: '62.09',
        indentLevel: 2,
      },
      {
        label: '63 Information service activities',
        value: '63',
      },
      {
        label: '63.1 Data processing, hosting and related activities; web portals',
        value: '63.1',
        indentLevel: 1,
      },
      {
        label: '63.11 Data processing, hosting and related activities',
        value: '63.11',
        indentLevel: 2,
      },
      {
        label: '63.12 Web portals',
        value: '63.12',
        indentLevel: 2,
      },
      {
        label: '63.9 Other information service activities',
        value: '63.9',
        indentLevel: 1,
      },
      {
        label: '63.91 News agency activities',
        value: '63.91',
        indentLevel: 2,
      },
      {
        label: '63.99 Other information service activities n.e.c.',
        value: '63.99',
        indentLevel: 2,
      },
    ],
  },
  {
    label: 'K - FINANCIAL AND INSURANCE ACTIVITIES',
    options: [
      {
        label: '64 Financial service activities, except insurance and pension funding',
        value: '64',
      },
      {
        label: '64.1 Monetary intermediation',
        value: '64.1',
        indentLevel: 1,
      },
      {
        label: '64.11 Central banking',
        value: '64.11',
        indentLevel: 2,
      },
      {
        label: '64.19 Other monetary intermediation',
        value: '64.19',
        indentLevel: 2,
      },
      {
        label: '64.2 Activities of holding companies',
        value: '64.2',
        indentLevel: 1,
      },
      {
        label: '64.20 Activities of holding companies',
        value: '64.20',
        indentLevel: 2,
      },
      {
        label: '64.3 Trusts, funds and similar financial entities',
        value: '64.3',
        indentLevel: 1,
      },
      {
        label: '64.30 Trusts, funds and similar financial entities',
        value: '64.30',
        indentLevel: 2,
      },
      {
        label: '64.9 Other financial service activities, except insurance and pension funding',
        value: '64.9',
        indentLevel: 1,
      },
      {
        label: '64.91 Financial leasing',
        value: '64.91',
        indentLevel: 2,
      },
      {
        label: '64.92 Other credit granting',
        value: '64.92',
        indentLevel: 2,
      },
      {
        label: '64.99 Other financial service activities, except insurance and pension funding n.e.c.',
        value: '64.99',
        indentLevel: 2,
      },
      {
        label: '65 Insurance, reinsurance and pension funding, except compulsory social security',
        value: '65',
      },
      {
        label: '65.1 Insurance',
        value: '65.1',
        indentLevel: 1,
      },
      {
        label: '65.11 Life insurance',
        value: '65.11',
        indentLevel: 2,
      },
      {
        label: '65.12 Non-life insurance',
        value: '65.12',
        indentLevel: 2,
      },
      {
        label: '65.2 Reinsurance',
        value: '65.2',
        indentLevel: 1,
      },
      {
        label: '65.20 Reinsurance',
        value: '65.20',
        indentLevel: 2,
      },
      {
        label: '65.3 Pension funding',
        value: '65.3',
        indentLevel: 1,
      },
      {
        label: '65.30 Pension funding',
        value: '65.30',
        indentLevel: 2,
      },
      {
        label: '66 Activities auxiliary to financial services and insurance activities',
        value: '66',
      },
      {
        label: '66.1 Activities auxiliary to financial services, except insurance and pension funding',
        value: '66.1',
        indentLevel: 1,
      },
      {
        label: '66.11 Administration of financial markets',
        value: '66.11',
        indentLevel: 2,
      },
      {
        label: '66.12 Security and commodity contracts brokerage',
        value: '66.12',
        indentLevel: 2,
      },
      {
        label: '66.19 Other activities auxiliary to financial services, except insurance and pension funding',
        value: '66.19',
        indentLevel: 2,
      },
      {
        label: '66.2 Activities auxiliary to insurance and pension funding',
        value: '66.2',
        indentLevel: 1,
      },
      {
        label: '66.21 Risk and damage evaluation',
        value: '66.21',
        indentLevel: 2,
      },
      {
        label: '66.22 Activities of insurance agents and brokers',
        value: '66.22',
        indentLevel: 2,
      },
      {
        label: '66.29 Other activities auxiliary to insurance and pension funding',
        value: '66.29',
        indentLevel: 2,
      },
      {
        label: '66.3 Fund management activities',
        value: '66.3',
        indentLevel: 1,
      },
      {
        label: '66.30 Fund management activities',
        value: '66.30',
        indentLevel: 2,
      },
    ],
  },
  {
    label: 'L - REAL ESTATE ACTIVITIES',
    options: [
      {
        label: '68 Real estate activities',
        value: '68',
      },
      {
        label: '68.1 Buying and selling of own real estate',
        value: '68.1',
        indentLevel: 1,
      },
      {
        label: '68.10 Buying and selling of own real estate',
        value: '68.10',
        indentLevel: 2,
      },
      {
        label: '68.2 Rental and operating of own or leased real estate',
        value: '68.2',
        indentLevel: 1,
      },
      {
        label: '68.20 Rental and operating of own or leased real estate',
        value: '68.20',
        indentLevel: 2,
      },
      {
        label: '68.3 Real estate activities on a fee or contract basis',
        value: '68.3',
        indentLevel: 1,
      },
      {
        label: '68.31 Real estate agencies',
        value: '68.31',
        indentLevel: 2,
      },
      {
        label: '68.32 Management of real estate on a fee or contract basis',
        value: '68.32',
        indentLevel: 2,
      },
    ],
  },
  {
    label: 'M - PROFESSIONAL, SCIENTIFIC AND TECHNICAL ACTIVITIES',
    options: [
      {
        label: '69 Legal and accounting activities',
        value: '69',
      },
      {
        label: '69.1 Legal activities',
        value: '69.1',
        indentLevel: 1,
      },
      {
        label: '69.10 Legal activities',
        value: '69.10',
        indentLevel: 2,
      },
      {
        label: '69.2 Accounting, bookkeeping and auditing activities; tax consultancy',
        value: '69.2',
        indentLevel: 1,
      },
      {
        label: '69.20 Accounting, bookkeeping and auditing activities; tax consultancy',
        value: '69.20',
        indentLevel: 2,
      },
      {
        label: '70 Activities of head offices; management consultancy activities',
        value: '70',
      },
      {
        label: '70.1 Activities of head offices',
        value: '70.1',
        indentLevel: 1,
      },
      {
        label: '70.10 Activities of head offices',
        value: '70.10',
        indentLevel: 2,
      },
      {
        label: '70.2 Management consultancy activities',
        value: '70.2',
        indentLevel: 1,
      },
      {
        label: '70.21 Public relations and communication activities',
        value: '70.21',
        indentLevel: 2,
      },
      {
        label: '70.22 Business and other management consultancy activities',
        value: '70.22',
        indentLevel: 2,
      },
      {
        label: '71 Architectural and engineering activities; technical testing and analysis',
        value: '71',
      },
      {
        label: '71.1 Architectural and engineering activities and related technical consultancy',
        value: '71.1',
        indentLevel: 1,
      },
      {
        label: '71.11 Architectural activities',
        value: '71.11',
        indentLevel: 2,
      },
      {
        label: '71.12 Engineering activities and related technical consultancy',
        value: '71.12',
        indentLevel: 2,
      },
      {
        label: '71.2 Technical testing and analysis',
        value: '71.2',
        indentLevel: 1,
      },
      {
        label: '71.20 Technical testing and analysis',
        value: '71.20',
        indentLevel: 2,
      },
      {
        label: '72 Scientific research and development',
        value: '72',
      },
      {
        label: '72.1 Research and experimental development on natural sciences and engineering',
        value: '72.1',
        indentLevel: 1,
      },
      {
        label: '72.11 Research and experimental development on biotechnology',
        value: '72.11',
        indentLevel: 2,
      },
      {
        label: '72.19 Other research and experimental development on natural sciences and engineering',
        value: '72.19',
        indentLevel: 2,
      },
      {
        label: '72.2 Research and experimental development on social sciences and humanities',
        value: '72.2',
        indentLevel: 1,
      },
      {
        label: '72.20 Research and experimental development on social sciences and humanities',
        value: '72.20',
        indentLevel: 2,
      },
      {
        label: '73 Advertising and market research',
        value: '73',
      },
      {
        label: '73.1 Advertising',
        value: '73.1',
        indentLevel: 1,
      },
      {
        label: '73.11 Advertising agencies',
        value: '73.11',
        indentLevel: 2,
      },
      {
        label: '73.12 Media representation',
        value: '73.12',
        indentLevel: 2,
      },
      {
        label: '73.2 Market research and public opinion polling',
        value: '73.2',
        indentLevel: 1,
      },
      {
        label: '73.20 Market research and public opinion polling',
        value: '73.20',
        indentLevel: 2,
      },
      {
        label: '74 Other professional, scientific and technical activities',
        value: '74',
      },
      {
        label: '74.1 Specialised design activities',
        value: '74.1',
        indentLevel: 1,
      },
      {
        label: '74.10 Specialised design activities',
        value: '74.10',
        indentLevel: 2,
      },
      {
        label: '74.2 Photographic activities',
        value: '74.2',
        indentLevel: 1,
      },
      {
        label: '74.20 Photographic activities',
        value: '74.20',
        indentLevel: 2,
      },
      {
        label: '74.3 Translation and interpretation activities',
        value: '74.3',
        indentLevel: 1,
      },
      {
        label: '74.30 Translation and interpretation activities',
        value: '74.30',
        indentLevel: 2,
      },
      {
        label: '74.9 Other professional, scientific and technical activities n.e.c.',
        value: '74.9',
        indentLevel: 1,
      },
      {
        label: '74.90 Other professional, scientific and technical activities n.e.c.',
        value: '74.90',
        indentLevel: 2,
      },
      {
        label: '75 Veterinary activities',
        value: '75',
      },
      {
        label: '75.0 Veterinary activities',
        value: '75.0',
        indentLevel: 1,
      },
      {
        label: '75.00 Veterinary activities',
        value: '75.00',
        indentLevel: 2,
      },
    ],
  },
  {
    label: 'N - ADMINISTRATIVE AND SUPPORT SERVICE ACTIVITIES',
    options: [
      {
        label: '77 Rental and leasing activities',
        value: '77',
      },
      {
        label: '77.1 Rental and leasing of motor vehicles',
        value: '77.1',
        indentLevel: 1,
      },
      {
        label: '77.11 Rental and leasing of cars and light motor vehicles',
        value: '77.11',
        indentLevel: 2,
      },
      {
        label: '77.12 Rental and leasing of trucks',
        value: '77.12',
        indentLevel: 2,
      },
      {
        label: '77.2 Rental and leasing of personal and household goods',
        value: '77.2',
        indentLevel: 1,
      },
      {
        label: '77.21 Rental and leasing of recreational and sports goods',
        value: '77.21',
        indentLevel: 2,
      },
      {
        label: '77.22 Rental of video tapes and disks',
        value: '77.22',
        indentLevel: 2,
      },
      {
        label: '77.29 Rental and leasing of other personal and household goods',
        value: '77.29',
        indentLevel: 2,
      },
      {
        label: '77.3 Rental and leasing of other machinery, equipment and tangible goods',
        value: '77.3',
        indentLevel: 1,
      },
      {
        label: '77.31 Rental and leasing of agricultural machinery and equipment',
        value: '77.31',
        indentLevel: 2,
      },
      {
        label: '77.32 Rental and leasing of construction and civil engineering machinery and equipment',
        value: '77.32',
        indentLevel: 2,
      },
      {
        label: '77.33 Rental and leasing of office machinery and equipment (including computers)',
        value: '77.33',
        indentLevel: 2,
      },
      {
        label: '77.34 Rental and leasing of water transport equipment',
        value: '77.34',
        indentLevel: 2,
      },
      {
        label: '77.35 Rental and leasing of air transport equipment',
        value: '77.35',
        indentLevel: 2,
      },
      {
        label: '77.39 Rental and leasing of other machinery, equipment and tangible goods n.e.c.',
        value: '77.39',
        indentLevel: 2,
      },
      {
        label: '77.4 Leasing of intellectual property and similar products, except copyrighted works',
        value: '77.4',
        indentLevel: 1,
      },
      {
        label: '77.40 Leasing of intellectual property and similar products, except copyrighted works',
        value: '77.40',
        indentLevel: 2,
      },
      {
        label: '78 Employment activities',
        value: '78',
      },
      {
        label: '78.1 Activities of employment placement agencies',
        value: '78.1',
        indentLevel: 1,
      },
      {
        label: '78.10 Activities of employment placement agencies',
        value: '78.10',
        indentLevel: 2,
      },
      {
        label: '78.2 Temporary employment agency activities',
        value: '78.2',
        indentLevel: 1,
      },
      {
        label: '78.20 Temporary employment agency activities',
        value: '78.20',
        indentLevel: 2,
      },
      {
        label: '78.3 Other human resources provision',
        value: '78.3',
        indentLevel: 1,
      },
      {
        label: '78.30 Other human resources provision',
        value: '78.30',
        indentLevel: 2,
      },
      {
        label: '79 Travel agency, tour operator and other reservation service and related activities',
        value: '79',
      },
      {
        label: '79.1 Travel agency and tour operator activities',
        value: '79.1',
        indentLevel: 1,
      },
      {
        label: '79.11 Travel agency activities',
        value: '79.11',
        indentLevel: 2,
      },
      {
        label: '79.12 Tour operator activities',
        value: '79.12',
        indentLevel: 2,
      },
      {
        label: '79.9 Other reservation service and related activities',
        value: '79.9',
        indentLevel: 1,
      },
      {
        label: '79.90 Other reservation service and related activities',
        value: '79.90',
        indentLevel: 2,
      },
      {
        label: '80 Security and investigation activities',
        value: '80',
      },
      {
        label: '80.1 Private security activities',
        value: '80.1',
        indentLevel: 1,
      },
      {
        label: '80.10 Private security activities',
        value: '80.10',
        indentLevel: 2,
      },
      {
        label: '80.2 Security systems service activities',
        value: '80.2',
        indentLevel: 1,
      },
      {
        label: '80.20 Security systems service activities',
        value: '80.20',
        indentLevel: 2,
      },
      {
        label: '80.3 Investigation activities',
        value: '80.3',
        indentLevel: 1,
      },
      {
        label: '80.30 Investigation activities',
        value: '80.30',
        indentLevel: 2,
      },
      {
        label: '81 Services to buildings and landscape activities',
        value: '81',
      },
      {
        label: '81.1 Combined facilities support activities',
        value: '81.1',
        indentLevel: 1,
      },
      {
        label: '81.10 Combined facilities support activities',
        value: '81.10',
        indentLevel: 2,
      },
      {
        label: '81.2 Cleaning activities',
        value: '81.2',
        indentLevel: 1,
      },
      {
        label: '81.21 General cleaning of buildings',
        value: '81.21',
        indentLevel: 2,
      },
      {
        label: '81.22 Other building and industrial cleaning activities',
        value: '81.22',
        indentLevel: 2,
      },
      {
        label: '81.29 Other cleaning activities',
        value: '81.29',
        indentLevel: 2,
      },
      {
        label: '81.3 Landscape service activities',
        value: '81.3',
        indentLevel: 1,
      },
      {
        label: '81.30 Landscape service activities',
        value: '81.30',
        indentLevel: 2,
      },
      {
        label: '82 Office administrative, office support and other business support activities',
        value: '82',
      },
      {
        label: '82.1 Office administrative and support activities',
        value: '82.1',
        indentLevel: 1,
      },
      {
        label: '82.11 Combined office administrative service activities',
        value: '82.11',
        indentLevel: 2,
      },
      {
        label: '82.19 Photocopying, document preparation and other specialised office support activities',
        value: '82.19',
        indentLevel: 2,
      },
      {
        label: '82.2 Activities of call centres',
        value: '82.2',
        indentLevel: 1,
      },
      {
        label: '82.20 Activities of call centres',
        value: '82.20',
        indentLevel: 2,
      },
      {
        label: '82.3 Organisation of conventions and trade shows',
        value: '82.3',
        indentLevel: 1,
      },
      {
        label: '82.30 Organisation of conventions and trade shows',
        value: '82.30',
        indentLevel: 2,
      },
      {
        label: '82.9 Business support service activities n.e.c.',
        value: '82.9',
        indentLevel: 1,
      },
      {
        label: '82.91 Activities of collection agencies and credit bureaus',
        value: '82.91',
        indentLevel: 2,
      },
      {
        label: '82.92 Packaging activities',
        value: '82.92',
        indentLevel: 2,
      },
      {
        label: '82.99 Other business support service activities n.e.c.',
        value: '82.99',
        indentLevel: 2,
      },
    ],
  },
  {
    label: 'O - PUBLIC ADMINISTRATION AND DEFENCE; COMPULSORY SOCIAL SECURITY',
    options: [
      {
        label: '84 Public administration and defence; compulsory social security',
        value: '84',
      },
      {
        label: '84.1 Administration of the State and the economic and social policy of the community',
        value: '84.1',
        indentLevel: 1,
      },
      {
        label: '84.11 General public administration activities',
        value: '84.11',
        indentLevel: 2,
      },
      {
        label:
          '84.12 Regulation of the activities of providing health care, education, cultural services and other social services, excluding social security',
        value: '84.12',
        indentLevel: 2,
      },
      {
        label: '84.13 Regulation of and contribution to more efficient operation of businesses',
        value: '84.13',
        indentLevel: 2,
      },
      {
        label: '84.2 Provision of services to the community as a whole',
        value: '84.2',
        indentLevel: 1,
      },
      {
        label: '84.21 Foreign affairs',
        value: '84.21',
        indentLevel: 2,
      },
      {
        label: '84.22 Defence activities',
        value: '84.22',
        indentLevel: 2,
      },
      {
        label: '84.23 Justice and judicial activities',
        value: '84.23',
        indentLevel: 2,
      },
      {
        label: '84.24 Public order and safety activities',
        value: '84.24',
        indentLevel: 2,
      },
      {
        label: '84.25 Fire service activities',
        value: '84.25',
        indentLevel: 2,
      },
      {
        label: '84.3 Compulsory social security activities',
        value: '84.3',
        indentLevel: 1,
      },
      {
        label: '84.30 Compulsory social security activities',
        value: '84.30',
        indentLevel: 2,
      },
    ],
  },
  {
    label: 'P - EDUCATION',
    options: [
      {
        label: '85 Education',
        value: '85',
      },
      {
        label: '85.1 Pre-primary education',
        value: '85.1',
        indentLevel: 1,
      },
      {
        label: '85.10 Pre-primary education',
        value: '85.10',
        indentLevel: 2,
      },
      {
        label: '85.2 Primary education',
        value: '85.2',
        indentLevel: 1,
      },
      {
        label: '85.20 Primary education',
        value: '85.20',
        indentLevel: 2,
      },
      {
        label: '85.3 Secondary education',
        value: '85.3',
        indentLevel: 1,
      },
      {
        label: '85.31 General secondary education',
        value: '85.31',
        indentLevel: 2,
      },
      {
        label: '85.32 Technical and vocational secondary education',
        value: '85.32',
        indentLevel: 2,
      },
      {
        label: '85.4 Higher education',
        value: '85.4',
        indentLevel: 1,
      },
      {
        label: '85.41 Post-secondary non-tertiary education',
        value: '85.41',
        indentLevel: 2,
      },
      {
        label: '85.42 Tertiary education',
        value: '85.42',
        indentLevel: 2,
      },
      {
        label: '85.5 Other education',
        value: '85.5',
        indentLevel: 1,
      },
      {
        label: '85.51 Sports and recreation education',
        value: '85.51',
        indentLevel: 2,
      },
      {
        label: '85.52 Cultural education',
        value: '85.52',
        indentLevel: 2,
      },
      {
        label: '85.53 Driving school activities',
        value: '85.53',
        indentLevel: 2,
      },
      {
        label: '85.59 Other education n.e.c.',
        value: '85.59',
        indentLevel: 2,
      },
      {
        label: '85.6 Educational support activities',
        value: '85.6',
        indentLevel: 1,
      },
      {
        label: '85.60 Educational support activities',
        value: '85.60',
        indentLevel: 2,
      },
    ],
  },
  {
    label: 'Q - HUMAN HEALTH AND SOCIAL WORK ACTIVITIES',
    options: [
      {
        label: '86 Human health activities',
        value: '86',
      },
      {
        label: '86.1 Hospital activities',
        value: '86.1',
        indentLevel: 1,
      },
      {
        label: '86.10 Hospital activities',
        value: '86.10',
        indentLevel: 2,
      },
      {
        label: '86.2 Medical and dental practice activities',
        value: '86.2',
        indentLevel: 1,
      },
      {
        label: '86.21 General medical practice activities',
        value: '86.21',
        indentLevel: 2,
      },
      {
        label: '86.22 Specialist medical practice activities',
        value: '86.22',
        indentLevel: 2,
      },
      {
        label: '86.23 Dental practice activities',
        value: '86.23',
        indentLevel: 2,
      },
      {
        label: '86.9 Other human health activities',
        value: '86.9',
        indentLevel: 1,
      },
      {
        label: '86.90 Other human health activities',
        value: '86.90',
        indentLevel: 2,
      },
      {
        label: '87 Residential care activities',
        value: '87',
      },
      {
        label: '87.1 Residential nursing care activities',
        value: '87.1',
        indentLevel: 1,
      },
      {
        label: '87.10 Residential nursing care activities',
        value: '87.10',
        indentLevel: 2,
      },
      {
        label: '87.2 Residential care activities for mental retardation, mental health and substance abuse',
        value: '87.2',
        indentLevel: 1,
      },
      {
        label: '87.20 Residential care activities for mental retardation, mental health and substance abuse',
        value: '87.20',
        indentLevel: 2,
      },
      {
        label: '87.3 Residential care activities for the elderly and disabled',
        value: '87.3',
        indentLevel: 1,
      },
      {
        label: '87.30 Residential care activities for the elderly and disabled',
        value: '87.30',
        indentLevel: 2,
      },
      {
        label: '87.9 Other residential care activities',
        value: '87.9',
        indentLevel: 1,
      },
      {
        label: '87.90 Other residential care activities',
        value: '87.90',
        indentLevel: 2,
      },
      {
        label: '88 Social work activities without accommodation',
        value: '88',
      },
      {
        label: '88.1 Social work activities without accommodation for the elderly and disabled',
        value: '88.1',
        indentLevel: 1,
      },
      {
        label: '88.10 Social work activities without accommodation for the elderly and disabled',
        value: '88.10',
        indentLevel: 2,
      },
      {
        label: '88.9 Other social work activities without accommodation',
        value: '88.9',
        indentLevel: 1,
      },
      {
        label: '88.91 Child day-care activities',
        value: '88.91',
        indentLevel: 2,
      },
      {
        label: '88.99 Other social work activities without accommodation n.e.c.',
        value: '88.99',
        indentLevel: 2,
      },
    ],
  },
  {
    label: 'R - ARTS, ENTERTAINMENT AND RECREATION',
    options: [
      {
        label: '90 Creative, arts and entertainment activities',
        value: '90',
      },
      {
        label: '90.0 Creative, arts and entertainment activities',
        value: '90.0',
        indentLevel: 1,
      },
      {
        label: '90.01 Performing arts',
        value: '90.01',
        indentLevel: 2,
      },
      {
        label: '90.02 Support activities to performing arts',
        value: '90.02',
        indentLevel: 2,
      },
      {
        label: '90.03 Artistic creation',
        value: '90.03',
        indentLevel: 2,
      },
      {
        label: '90.04 Operation of arts facilities',
        value: '90.04',
        indentLevel: 2,
      },
      {
        label: '91 Libraries, archives, museums and other cultural activities',
        value: '91',
      },
      {
        label: '91.0 Libraries, archives, museums and other cultural activities',
        value: '91.0',
        indentLevel: 1,
      },
      {
        label: '91.01 Library and archives activities',
        value: '91.01',
        indentLevel: 2,
      },
      {
        label: '91.02 Museums activities',
        value: '91.02',
        indentLevel: 2,
      },
      {
        label: '91.03 Operation of historical sites and buildings and similar visitor attractions',
        value: '91.03',
        indentLevel: 2,
      },
      {
        label: '91.04 Botanical and zoological gardens and nature reserves activities',
        value: '91.04',
        indentLevel: 2,
      },
      {
        label: '92 Gambling and betting activities',
        value: '92',
      },
      {
        label: '92.0 Gambling and betting activities',
        value: '92.0',
        indentLevel: 1,
      },
      {
        label: '92.00 Gambling and betting activities',
        value: '92.00',
        indentLevel: 2,
      },
      {
        label: '93 Sports activities and amusement and recreation activities',
        value: '93',
      },
      {
        label: '93.1 Sports activities',
        value: '93.1',
        indentLevel: 1,
      },
      {
        label: '93.11 Operation of sports facilities',
        value: '93.11',
        indentLevel: 2,
      },
      {
        label: '93.12 Activities of sports clubs',
        value: '93.12',
        indentLevel: 2,
      },
      {
        label: '93.13 Fitness facilities',
        value: '93.13',
        indentLevel: 2,
      },
      {
        label: '93.19 Other sports activities',
        value: '93.19',
        indentLevel: 2,
      },
      {
        label: '93.2 Amusement and recreation activities',
        value: '93.2',
        indentLevel: 1,
      },
      {
        label: '93.21 Activities of amusement parks and theme parks',
        value: '93.21',
        indentLevel: 2,
      },
      {
        label: '93.29 Other amusement and recreation activities',
        value: '93.29',
        indentLevel: 2,
      },
    ],
  },
  {
    label: 'S - OTHER SERVICE ACTIVITIES',
    options: [
      {
        label: '94 Activities of membership organisations',
        value: '94',
      },
      {
        label: '94.1 Activities of business, employers and professional membership organisations',
        value: '94.1',
        indentLevel: 1,
      },
      {
        label: '94.11 Activities of business and employers membership organisations',
        value: '94.11',
        indentLevel: 2,
      },
      {
        label: '94.12 Activities of professional membership organisations',
        value: '94.12',
        indentLevel: 2,
      },
      {
        label: '94.2 Activities of trade unions',
        value: '94.2',
        indentLevel: 1,
      },
      {
        label: '94.20 Activities of trade unions',
        value: '94.20',
        indentLevel: 2,
      },
      {
        label: '94.9 Activities of other membership organisations',
        value: '94.9',
        indentLevel: 1,
      },
      {
        label: '94.91 Activities of religious organisations',
        value: '94.91',
        indentLevel: 2,
      },
      {
        label: '94.92 Activities of political organisations',
        value: '94.92',
        indentLevel: 2,
      },
      {
        label: '94.99 Activities of other membership organisations n.e.c.',
        value: '94.99',
        indentLevel: 2,
      },
      {
        label: '95 Repair of computers and personal and household goods',
        value: '95',
      },
      {
        label: '95.1 Repair of computers and communication equipment',
        value: '95.1',
        indentLevel: 1,
      },
      {
        label: '95.11 Repair of computers and peripheral equipment',
        value: '95.11',
        indentLevel: 2,
      },
      {
        label: '95.12 Repair of communication equipment',
        value: '95.12',
        indentLevel: 2,
      },
      {
        label: '95.2 Repair of personal and household goods',
        value: '95.2',
        indentLevel: 1,
      },
      {
        label: '95.21 Repair of consumer electronics',
        value: '95.21',
        indentLevel: 2,
      },
      {
        label: '95.22 Repair of household appliances and home and garden equipment',
        value: '95.22',
        indentLevel: 2,
      },
      {
        label: '95.23 Repair of footwear and leather goods',
        value: '95.23',
        indentLevel: 2,
      },
      {
        label: '95.24 Repair of furniture and home furnishings',
        value: '95.24',
        indentLevel: 2,
      },
      {
        label: '95.25 Repair of watches, clocks and jewellery',
        value: '95.25',
        indentLevel: 2,
      },
      {
        label: '95.29 Repair of other personal and household goods',
        value: '95.29',
        indentLevel: 2,
      },
      {
        label: '96 Other personal service activities',
        value: '96',
      },
      {
        label: '96.0 Other personal service activities',
        value: '96.0',
        indentLevel: 1,
      },
      {
        label: '96.01 Washing and (dry-)cleaning of textile and fur products',
        value: '96.01',
        indentLevel: 2,
      },
      {
        label: '96.02 Hairdressing and other beauty treatment',
        value: '96.02',
        indentLevel: 2,
      },
      {
        label: '96.03 Funeral and related activities',
        value: '96.03',
        indentLevel: 2,
      },
      {
        label: '96.04 Physical well-being activities',
        value: '96.04',
        indentLevel: 2,
      },
      {
        label: '96.09 Other personal service activities n.e.c.',
        value: '96.09',
        indentLevel: 2,
      },
    ],
  },
  {
    label:
      'T - ACTIVITIES OF HOUSEHOLDS AS EMPLOYERS; UNDIFFERENTIATED GOODS- AND SERVICES-PRODUCING ACTIVITIES OF HOUSEHOLDS FOR OWN USE',
    options: [
      {
        label: '97 Activities of households as employers of domestic personnel',
        value: '97',
      },
      {
        label: '97.0 Activities of households as employers of domestic personnel',
        value: '97.0',
        indentLevel: 1,
      },
      {
        label: '97.00 Activities of households as employers of domestic personnel',
        value: '97.00',
        indentLevel: 2,
      },
      {
        label: '98 Undifferentiated goods- and services-producing activities of private households for own use',
        value: '98',
      },
      {
        label: '98.1 Undifferentiated goods-producing activities of private households for own use',
        value: '98.1',
        indentLevel: 1,
      },
      {
        label: '98.10 Undifferentiated goods-producing activities of private households for own use',
        value: '98.10',
        indentLevel: 2,
      },
      {
        label: '98.2 Undifferentiated service-producing activities of private households for own use',
        value: '98.2',
        indentLevel: 1,
      },
      {
        label: '98.20 Undifferentiated service-producing activities of private households for own use',
        value: '98.20',
        indentLevel: 2,
      },
    ],
  },
  {
    label: 'U - ACTIVITIES OF EXTRATERRITORIAL ORGANISATIONS AND BODIES',
    options: [
      {
        label: '99 Activities of extraterritorial organisations and bodies',
        value: '99',
      },
      {
        label: '99.0 Activities of extraterritorial organisations and bodies',
        value: '99.0',
        indentLevel: 1,
      },
      {
        label: '99.00 Activities of extraterritorial organisations and bodies',
        value: '99.00',
        indentLevel: 2,
      },
    ],
  },
];
