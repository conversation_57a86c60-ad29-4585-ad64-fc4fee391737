import React from 'react';

import { SingleSelect } from '~/ui';
import { getOptionsFromArray } from '~/utils/arrays';

const options = getOptionsFromArray([
  'ALL',
  'A - AGRICULTURE, FORESTRY AND FISHING',
  'B - <PERSON>NING AND QUARRYING',
  'C - <PERSON>NUFACTURING',
  'D - ELECTRICITY, GAS, STEAM AND AIR CONDITIONING SUPPLY',
  'E - WATER SUPPLY; SEWERAGE, WASTE MANAGEMENT AND REMEDIATION ACTIVITIES',
  'F - CONSTRUCTION',
  'G - WHO<PERSON>SALE AND RETAIL TRADE; REPAIR OF MOTOR VEHICLES AND MOTORCYCLES',
  'H - <PERSON><PERSON>NSPORTATION AND STORAGE',
  'I - ACCOMMODATION AND FOOD SERVICE ACTIVITIES',
  'J - INFORMATION AND COMMUNICATION',
  'K - FINANCIAL AND INSURANCE ACTIVITIES',
  'L - REA<PERSON> ESTATE ACTIVITIES',
  'M - PROFESSION<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> AND <PERSON><PERSON><PERSON><PERSON>CA<PERSON> ACTIVITIES',
  'N - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> AND <PERSON><PERSON>PO<PERSON> SERVICE ACTIVI<PERSON>ES',
  'O - PUBLIC ADMINISTRATION AND DEFENCE; COMPULSORY SOCIAL SECURITY',
  'P - EDUCATION',
  'Q - HUMAN HEALTH AND SOCIAL WORK ACTIVITIES',
  'R - ARTS, ENTERTAINMENT AND RECREATION',
  'S - OTHER SERVICE ACTIVITIES',
  'T - ACTIVITIES OF HOUSEHOLDS AS EMPLOYERS; UNDIFFERENTIATED GOODS- AND SERVICES-PRODUCING ACTIVITIES OF HOUSEHOLDS FOR OWN USE',
  'U - ACTIVITIES OF EXTRATERRITORIAL ORGANISATIONS AND BODIES',
]);

function NaceSectorGroupSingleSelect({ label = 'Sector group', sx, tooltip, value, disabled, width = 'm', onChange }) {
  return (
    <SingleSelect
      label={label}
      options={options}
      sx={sx}
      tooltip={tooltip}
      disabled={disabled}
      value={value}
      width={width}
      onChange={onChange}
    />
  );
}

export default NaceSectorGroupSingleSelect;
