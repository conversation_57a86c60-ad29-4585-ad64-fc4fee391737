import { useSelector } from 'react-redux';
import _ from 'lodash';

import { createCreditRatingDraft } from 'api';
import { creditRating } from 'reducers/creditRating.slice';
import { Button, Modal, TextInput } from 'ui';
import { errorHandler } from 'utils/errors';
import { showToast } from 'ui/components/Toast';

type SaveDraftModalPropsType = {
  isShowing: boolean;
  draftName: string;
  setDraftName: React.Dispatch<React.SetStateAction<string>>;
  setIsSaveDraftModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
};

const SaveDraftModal = ({ isShowing, draftName, setDraftName, setIsSaveDraftModalOpen }: SaveDraftModalPropsType) => {
  const creditRatingData = useSelector(creditRating);

  const onSaveDraft = async () => {
    try {
      const draft = _.pick(creditRatingData, ['company', 'closingDate', 'attributes']);

      await createCreditRatingDraft(draftName, { ...draft });

      setIsSaveDraftModalOpen(false);
      setDraftName('');
      showToast('Draft saved successfully.');
    } catch (err) {
      errorHandler(err);
    }
  };

  if (!isShowing) return null;

  return (
    <Modal
      actionButtons={<Button disabled={!draftName} text="Save" size="l" onClick={onSaveDraft} />}
      title="Save this draft"
      width="m"
      onHide={() => setIsSaveDraftModalOpen(false)}
    >
      <TextInput width="fullWidth" value={draftName} onChange={setDraftName} placeholder="Draft name" />
    </Modal>
  );
};

export default SaveDraftModal;
