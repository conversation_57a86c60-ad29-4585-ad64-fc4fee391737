import { useState } from 'react';

import { FlexLayout } from 'ui';
import { ButtonActionMenu } from 'components/Shared';

import SaveDraftModal from './SaveDraftModal';
import SavedDraftsModal from './SavedDraftsModal';

const CreditRatingDrafts = () => {
  const [draftName, setDraftName] = useState<string>('');
  const [isSaveDraftModalOpen, setIsSaveDraftModalOpen] = useState<boolean>(false);
  const [isSavedDraftsModalOpen, setIsSavedDraftsModalOpen] = useState<boolean>(false);

  const options = [
    { label: 'Save as draft', onClick: () => setIsSaveDraftModalOpen(true) },
    { label: 'See saved drafts', onClick: () => setIsSavedDraftsModalOpen(true) },
  ];

  return (
    <FlexLayout>
      <ButtonActionMenu options={options} buttonText="Save" />
      <SaveDraftModal
        isShowing={isSaveDraftModalOpen}
        draftName={draftName}
        setDraftName={setDraftName}
        setIsSaveDraftModalOpen={setIsSaveDraftModalOpen}
      />
      <SavedDraftsModal isShowing={isSavedDraftsModalOpen} setIsSavedDraftsModalOpen={setIsSavedDraftsModalOpen} />
    </FlexLayout>
  );
};

export default CreditRatingDrafts;
