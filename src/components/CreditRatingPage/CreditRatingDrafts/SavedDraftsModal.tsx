import { useContext, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

import { getCreditRatingDrafts, deleteCreditRatingDraft } from 'api';
import { Button, FlexLayout, Modal, RadioGroup, Text } from 'ui';
import { setCreditRatingData } from 'reducers/creditRating.slice';
import { formatDateString } from 'utils/dates';
import { UserInfoContext } from 'context/user';
import { errorHandler } from 'utils/errors';
import { CreditRatingDraftType } from 'types';
import { showToast } from 'ui/components/Toast';

type SavedDraftsModalPropsType = {
  isShowing: boolean;
  setIsSavedDraftsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
};

const SavedDraftsModal = ({ isShowing, setIsSavedDraftsModalOpen }: SavedDraftsModalPropsType) => {
  const dispatch = useDispatch();
  const { userInfo } = useContext(UserInfoContext);
  const [drafts, setDrafts] = useState<CreditRatingDraftType[]>([]);
  const [selectedDraft, setSelectedDraft] = useState<string | undefined>();

  const onDraftApply = () => {
    const savedDraft = drafts.find((savedDraft) => String(savedDraft.id) === selectedDraft);
    dispatch(setCreditRatingData(savedDraft));
    setIsSavedDraftsModalOpen(false);
    showToast('Draft applied successfully.');
  };

  const onDraftDelete = async () => {
    try {
      await deleteCreditRatingDraft(selectedDraft);
      const savedDrafts = await getCreditRatingDrafts();
      setDrafts(savedDrafts);
      setSelectedDraft(undefined);
      showToast('Draft deleted successfully.');
    } catch (error) {
      errorHandler(error);
    }
  };

  useEffect(() => {
    if (isShowing) {
      getCreditRatingDrafts().then(setDrafts).catch(errorHandler);
    }
  }, [isShowing]);

  if (!isShowing) return null;

  return (
    <Modal
      title="Saved drafts"
      width="s"
      onHide={() => setIsSavedDraftsModalOpen(false)}
      actionButtons={
        <FlexLayout space={4}>
          <Button variant="gray" text="Delete" disabled={!selectedDraft} onClick={onDraftDelete} />
          <Button text="Use" disabled={!selectedDraft} onClick={onDraftApply} />
        </FlexLayout>
      }
    >
      {drafts.length !== 0 ? (
        <FlexLayout sx={{ maxHeight: '600px', overflowY: 'scroll' }}>
          <RadioGroup
            options={drafts.map((savedDraft) => ({
              label: `${savedDraft.name} (${formatDateString(savedDraft.createdAt, userInfo.dateFormat, true)})`,
              value: String(savedDraft.id),
            }))}
            width="l"
            value={selectedDraft}
            onChange={setSelectedDraft}
            variant="column"
            radioVariant="secondary"
          />
        </FlexLayout>
      ) : (
        <Text color="bali-hai" variant="m-spaced">
          No saved drafts.
        </Text>
      )}
    </Modal>
  );
};

export default SavedDraftsModal;
