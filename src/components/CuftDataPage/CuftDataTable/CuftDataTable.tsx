import { useContext, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Table, Pagination } from 'ui';
import { UserInfoContext } from 'context/user';
import { cuftDataColumns } from 'reducers/tableColumns/cuftDataColumns.slice';
import { cuftDataFiltersSelector, updatePagination, updateField } from 'reducers/cuftDataFilters.slice';
import { DbCuftDataTypeWithCreditRatingsType } from 'types';

import CuftDataTableWrapper from './CuftDataTableWrapper';
import { getCuftDataColumns, getCuftTableData } from './CuftDataTable.utils';
import FilterCuftDataColumnsModal from './FilterCuftDataColumnsModal';

type CuftDataTablePropsType = {
  cuftData: DbCuftDataTypeWithCreditRatingsType[];
  numberOfCuftData: number;
  disabledUntilFilled: boolean;
  onExportCuftData: () => Promise<void>;
};

const CuftDataTable = ({
  cuftData,
  numberOfCuftData,
  disabledUntilFilled,
  onExportCuftData,
}: CuftDataTablePropsType) => {
  const dispatch = useDispatch();
  const { userInfo } = useContext(UserInfoContext);
  const [isColumnFilterModalShowing, setIsColumnFilterModalShowing] = useState<boolean>(false);
  const visibleColumns = useSelector(cuftDataColumns);
  const cuftDataFilters = useSelector(cuftDataFiltersSelector);
  const { limit, offset, sortBy } = cuftDataFilters;

  const onPageChange = ({ selected }: { selected: number }) => dispatch(updatePagination({ offset: selected }));

  return (
    <CuftDataTableWrapper
      disabledUntilFilled={disabledUntilFilled}
      onExportCuftData={onExportCuftData}
      setIsColumnFilterModalShowing={setIsColumnFilterModalShowing}
    >
      <Table
        serverSideSorting
        updateSorting={(id: string | null) => dispatch(updateField({ sortBy: id }))}
        sortedBy={sortBy}
        noEntriesText="No agreements for current filters."
        columns={getCuftDataColumns(visibleColumns, dispatch)}
        data={getCuftTableData(cuftData, userInfo)}
        isPaginationHidden
      />
      <Pagination
        canNextPage={numberOfCuftData - offset * limit > offset * limit}
        canPreviousPage={offset !== 0}
        pageCount={numberOfCuftData / limit}
        forcePage={offset}
        onPageChange={onPageChange}
        isShowing={numberOfCuftData > limit}
      />
      <FilterCuftDataColumnsModal
        isShowing={isColumnFilterModalShowing}
        onHide={() => setIsColumnFilterModalShowing(false)}
      />
    </CuftDataTableWrapper>
  );
};

export default CuftDataTable;
