import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { cuftDataColumns, updateColumns } from 'reducers/tableColumns/cuftDataColumns.slice';
import { Button, FlexLayout, Modal } from 'ui';

type FilterCuftDataColumnsModalType = {
  isShowing: boolean;
  onHide: React.Dispatch<React.SetStateAction<boolean>>;
};

function FilterCuftDataColumnsModal({ onHide, isShowing }: FilterCuftDataColumnsModalType) {
  const dispatch = useDispatch();
  const columns = useSelector(cuftDataColumns);
  const [columnsCopy, setColumnsCopy] = useState({
    ...Object.fromEntries(Object.entries(columns).filter(([key]) => key !== 'Remove')),
  });

  function handleOnSelectClick() {
    dispatch(updateColumns(columnsCopy));
    onHide(false);
  }

  type ColumnKeysType = keyof typeof columnsCopy;

  if (!isShowing) return null;

  return (
    <Modal
      actionButtons={<Button text="Select" onClick={handleOnSelectClick} />}
      title="Add or remove columns."
      width="s"
      onHide={onHide}
    >
      <FlexLayout alignItems="center" flexWrap="wrap" justifyContent="flex-start" space={4} sx={{ mt: -4 }}>
        {Object.keys(columnsCopy).map((column) => (
          <Button
            key={column}
            size="s"
            sx={{ mt: 4 }}
            text={column}
            variant={columnsCopy[column as ColumnKeysType] ? 'primary' : 'gray'}
            onClick={() => setColumnsCopy((_state) => ({ ..._state, [column]: !_state[column as ColumnKeysType] }))}
          />
        ))}
      </FlexLayout>
    </Modal>
  );
}

export default FilterCuftDataColumnsModal;
