import { Button, Card, FlexLayout, Text, WithTooltip } from 'ui';

type CuftDataTableWrapperPropsType = {
  setIsColumnFilterModalShowing: React.Dispatch<React.SetStateAction<boolean>>;
  disabledUntilFilled: boolean;
  onExportCuftData: () => Promise<void>;
  children: React.ReactNode;
};

const CuftDataTableWrapper = ({
  setIsColumnFilterModalShowing,
  disabledUntilFilled,
  onExportCuftData,
  children,
}: CuftDataTableWrapperPropsType) => {
  return (
    <Card
      pb={3}
      pt={6}
      title={
        <FlexLayout alignItems="center" justifyContent="space-between">
          <Text color="deep-sapphire" variant="xl-spaced-bold">
            Agreements
          </Text>
          <FlexLayout space={4}>
            <Button
              iconLeft="columns"
              size="s"
              text="Columns"
              variant="secondary"
              sx={{ height: '32px' }}
              onClick={() => setIsColumnFilterModalShowing(true)}
            />
            <WithTooltip
              disabled={disabledUntilFilled}
              label="cuftExportButton"
              tooltip="Select issue date and credit rating first."
            >
              <Button
                disabled={disabledUntilFilled}
                iconLeft="export"
                text="Export"
                size="s"
                variant="secondary"
                sx={{ height: '32px' }}
                onClick={onExportCuftData}
              />
            </WithTooltip>
          </FlexLayout>
        </FlexLayout>
      }
    >
      {children}
    </Card>
  );
};

export default CuftDataTableWrapper;
