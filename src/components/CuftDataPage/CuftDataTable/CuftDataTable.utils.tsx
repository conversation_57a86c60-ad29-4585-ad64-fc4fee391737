import type { AppDispatch } from 'store';

import { excludeCreditRatingIds } from 'reducers/cuftDataFilters.slice';
import type { DbCuftDataTypeWithCreditRatingsType, UserInfoType } from 'types';
import { Icon } from 'ui';
import { showToast } from 'ui/components/Toast';
import { formatDateString } from 'utils/dates';
import { displayNumber2 } from 'utils/strings';
import { getVisibleColumns } from 'utils/tables';

const getColumns = (dispatch: AppDispatch) => [
  { label: 'Borrower', sortBy: 'cuftBorrowerName', value: 'cuftBorrowerName', width: 250, wrapText: true },
  { label: 'Filing Company Name', sortBy: 'filingCompanyName', value: 'filingCompanyName', width: 250, wrapText: true },
  { label: 'Country', sortBy: 'cuftBorrowerCountry', value: 'cuftBorrowerCountry' },
  { label: 'Primary SIC', sortBy: 'primarySic', value: 'primarySic' },
  { label: 'Issue Date', sortBy: 'cuftTrancheExecutionDate', value: 'cuftTrancheExecutionDate' },
  { label: 'Maturity Date', sortBy: 'cuftTrancheMaturityDate', value: 'cuftTrancheMaturityDate' },
  { label: 'Tenor', sortBy: 'cuftTrancheTenor', value: 'cuftTrancheTenor', justifyContent: 'flex-end' },
  { label: 'Credit Rating', sortBy: 'creditRating', value: 'creditRating' },
  { label: 'Credit Spread', sortBy: 'creditSpread', value: 'creditSpread', justifyContent: 'flex-end' },
  { label: 'Currency', value: 'allCurrencies' },
  {
    label: "Moody's Obligor Credit Rating",
    sortBy: 'moodyPrincipalObligorCreditRating',
    value: 'moodyPrincipalObligorCreditRating',
  },
  {
    label: 'S&P Obligor Credit Rating',
    sortBy: 'spyPrincipalObligorCreditRating',
    value: 'spyPrincipalObligorCreditRating',
  },
  { label: 'Tranche Asset Class', sortBy: 'cuftTrancheAssetClass', value: 'cuftTrancheAssetClass' },
  { label: 'Tranche Type', sortBy: 'cuftTrancheType', value: 'cuftTrancheType' },
  {
    label: 'Tranche Primary Reference Rate',
    sortBy: 'cuftTranchePrimaryReferenceRate',
    value: 'cuftTranchePrimaryReferenceRate',
  },
  {
    label: 'Commitment Fee',
    sortBy: 'cuftTrancheCommitmentFee',
    value: 'cuftTrancheCommitmentFee',
  },
  {
    label: 'Annual Fee',
    sortBy: 'cuftTrancheAnnualFee',
    value: 'cuftTrancheAnnualFee',
  },
  {
    label: 'Utilization Fee',
    sortBy: 'cuftTrancheUtilisationFee',
    value: 'cuftTrancheUtilisationFee',
  },
  {
    label: 'Upfront Fee',
    sortBy: 'cuftTrancheUpfrontFee',
    value: 'cuftTrancheUpfrontFee',
  },
  {
    label: 'Remove',
    value: 'removeRow',
    renderCustomCell: ({ creditRatingId }: { creditRatingId: number }) => {
      return (
        <Icon
          color="bali-hai"
          icon="delete"
          onClick={() => {
            showToast('Agreement successfully removed.');
            dispatch(excludeCreditRatingIds(creditRatingId));
          }}
        />
      );
    },
  },
];

export function getCuftDataColumns(visibleColumns: any, dispatch: AppDispatch) {
  return getVisibleColumns({ columns: getColumns(dispatch), visibleColumns: { ...visibleColumns, Remove: true } });
}

const getCuftTableRowData = (cuftData: DbCuftDataTypeWithCreditRatingsType, userInfo: UserInfoType) => {
  const {
    id,
    cuftBorrowerName,
    cuftBorrowerCountry,
    filingCompanyName,
    primarySic,
    cuftTrancheExecutionDate,
    cuftTrancheMaturityDate,
    allCurrencies,
    cuftTrancheTenor,
    moodyPrincipalObligorCreditRating,
    spyPrincipalObligorCreditRating,
    cuftTrancheAssetClass,
    cuftTrancheType,
    cuftTranchePrimaryReferenceRate,
    creditRatings,
    cuftTrancheCommitmentFee,
    cuftTrancheAnnualFee,
    cuftTrancheUtilisationFee,
    cuftTrancheUpfrontFee,
  } = cuftData;

  return {
    id,
    cuftBorrowerName,
    cuftBorrowerCountry,
    cuftTrancheExecutionDate: formatDateString(cuftTrancheExecutionDate, userInfo.dateFormat),
    cuftTrancheMaturityDate: formatDateString(cuftTrancheMaturityDate, userInfo.dateFormat),
    cuftTrancheTenor: displayNumber2(cuftTrancheTenor, { decimalPoint: userInfo.decimalPoint, minDig: 0 }),
    creditRatingId: creditRatings.id,
    creditRating: creditRatings.creditRatingName,
    creditSpread: displayNumber2(creditRatings.creditRatingValue, { decimalPoint: userInfo.decimalPoint, minDig: 0 }),
    allCurrencies,
    moodyPrincipalObligorCreditRating,
    spyPrincipalObligorCreditRating,
    cuftTrancheAssetClass,
    cuftTrancheType,
    cuftTranchePrimaryReferenceRate,
    filingCompanyName,
    primarySic,
    cuftTrancheCommitmentFee: displayNumber2(cuftTrancheCommitmentFee, {
      decimalPoint: userInfo.decimalPoint,
      minDig: 0,
    }),
    cuftTrancheAnnualFee: displayNumber2(cuftTrancheAnnualFee, { decimalPoint: userInfo.decimalPoint, minDig: 0 }),
    cuftTrancheUtilisationFee: displayNumber2(cuftTrancheUtilisationFee, {
      decimalPoint: userInfo.decimalPoint,
      minDig: 0,
    }),
    cuftTrancheUpfrontFee: displayNumber2(cuftTrancheUpfrontFee, { decimalPoint: userInfo.decimalPoint, minDig: 0 }),
    removeRow: '',
  };
};

export const getCuftTableData = (data: DbCuftDataTypeWithCreditRatingsType[], userInfo: UserInfoType) => {
  return data.map((d) => getCuftTableRowData(d, userInfo));
};
