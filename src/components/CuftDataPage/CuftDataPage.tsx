import { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { subMonths } from 'date-fns';
import { saveAs } from 'file-saver';

import { exportCuftData, getCuftData, getCuftDataAvailableFilteringOptions } from 'api';
import { PageLayout } from 'ui';
import { useUnsavedChangesWarning } from 'hooks';
import { cuftDataFiltersSelector, resetCuftDataFilter } from 'reducers/cuftDataFilters.slice';
import { showErrorToast, showToast } from 'ui/components/Toast';
import { errorHandler, fullResponseErrorHandler } from 'utils/errors';
import {
  CuftDataSummaryStatisticsType,
  DbCuftDataTypeWithCreditRatingsType,
  CuftDataAvailableFiltersType,
} from 'types';

import CuftDataFilterForm from './CuftDataFilterForm';
import SummaryStatistics from './SummaryStatistics';
import CuftDataTable from './CuftDataTable';
import TopRightButtons from './TopRightButtons';

const CuftPage = () => {
  const dispatch = useDispatch();
  const [cuftData, setCuftData] = useState<DbCuftDataTypeWithCreditRatingsType[]>([]);
  const [summaryStatisticsData, setSummaryStatisticsData] = useState<CuftDataSummaryStatisticsType | null>(null);
  const [numberOfCuftData, setNumberOfCuftData] = useState<number>(0);
  const [cuftDataAvailableFilters, setCuftDataAvailableFilters] = useState<CuftDataAvailableFiltersType>({
    cuftDataAvailableBorrowerCountries: [],
    cuftDataAvailableTrancheAssetClasses: [],
    cuftDataAvailableCurrencies: [],
  });
  const cuftDataFilters = useSelector(cuftDataFiltersSelector);
  const {
    limit,
    offset,
    issueDate,
    numberOfMonthsBeforeIssueDate,
    tenor,
    numberOfYearsBeforeAndAfterTenor,
    creditRatings,
    currencies,
    countries,
    trancheAssetClasses,
    excludedCreditRatingIds,
    sortBy,
    isDirty,
  } = cuftDataFilters;
  const [Prompt] = useUnsavedChangesWarning({ isDirty });
  const disabledUntilFilled = !issueDate || creditRatings.length === 0;
  const errorMessageThatShouldNotShow = 'Select credit rating/s and issue date before exporting';
  const summaryStatisticsTitle = 'Summary Statistics';

  const clearAllData = () => {
    setCuftData([]);
    setSummaryStatisticsData(null);
    setNumberOfCuftData(0);
  };

  const getGetCuftDataDto = useCallback(() => {
    if (!issueDate) return;

    return {
      limit,
      offset,
      issueDate: new Date(issueDate!),
      dateMonthsBeforeIssueDate: subMonths(new Date(issueDate!), numberOfMonthsBeforeIssueDate),
      tenor,
      numberOfYearsBeforeAndAfterTenor,
      creditRatings,
      currencies,
      countries,
      trancheAssetClasses,
      excludedCreditRatingIds,
      sortBy,
    };
  }, [
    limit,
    offset,
    issueDate,
    numberOfMonthsBeforeIssueDate,
    tenor,
    numberOfYearsBeforeAndAfterTenor,
    creditRatings,
    currencies,
    countries,
    trancheAssetClasses,
    excludedCreditRatingIds,
    sortBy,
  ]);

  const onExportCuftData = async () => {
    const data = getGetCuftDataDto();
    if (!data) return showErrorToast(errorMessageThatShouldNotShow);

    try {
      const file = await exportCuftData(data);
      saveAs(file, file.name);
      showToast('Sheet successfully exported.');
    } catch (err: any) {
      fullResponseErrorHandler(err);
    }
  };

  useEffect(() => {
    // clears all after Clear All was clicked
    if (disabledUntilFilled) return clearAllData();

    const data = getGetCuftDataDto();
    if (!data) return showErrorToast(errorMessageThatShouldNotShow);

    getCuftData(data)
      .then(({ totalNumberOfCuftData, cuftData, summaryStatistics }) => {
        setNumberOfCuftData(totalNumberOfCuftData);
        setCuftData(cuftData);
        setSummaryStatisticsData(summaryStatistics);
      })
      .catch(errorHandler);
  }, [disabledUntilFilled, cuftDataFilters, sortBy, getGetCuftDataDto]);

  useEffect(() => {
    // clears all after Clear All was clicked
    if (disabledUntilFilled) return clearAllData();

    const data = getGetCuftDataDto();
    if (!data) return showErrorToast(errorMessageThatShouldNotShow);

    getCuftDataAvailableFilteringOptions(data)
      .then(({ uniqueBorrowerCountries, uniqueTrancheAssetClasses, uniqueCurrencies }) => {
        setCuftDataAvailableFilters({
          cuftDataAvailableBorrowerCountries: uniqueBorrowerCountries,
          cuftDataAvailableTrancheAssetClasses: uniqueTrancheAssetClasses,
          cuftDataAvailableCurrencies: uniqueCurrencies,
        });
      })
      .catch(errorHandler);
  }, [disabledUntilFilled, issueDate, creditRatings, getGetCuftDataDto]);

  useEffect(() => {
    return () => {
      dispatch(resetCuftDataFilter());
    };
  }, [dispatch]);

  return (
    <PageLayout
      title="CUFT Agreements"
      rightTitleContent={
        <TopRightButtons disabledUntilFilled={disabledUntilFilled} summaryStatisticsTitle={summaryStatisticsTitle} />
      }
    >
      <CuftDataFilterForm
        disabledUntilFilled={disabledUntilFilled}
        cuftDataAvailableFilters={cuftDataAvailableFilters}
      />

      <SummaryStatistics
        summaryStatisticsData={summaryStatisticsData}
        summaryStatisticsTitle={summaryStatisticsTitle}
      />

      <CuftDataTable
        cuftData={cuftData}
        numberOfCuftData={numberOfCuftData}
        disabledUntilFilled={disabledUntilFilled}
        onExportCuftData={onExportCuftData}
      />
      {Prompt}
    </PageLayout>
  );
};

export default CuftPage;
