import { useContext, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

import { getCuftDataSavedSearch } from 'api';
import { Button, FlexLayout, Modal, RadioGroup, Text } from 'ui';
import { setCuftDataFilter } from 'reducers/cuftDataFilters.slice';
import { formatDateString } from 'utils/dates';
import { UserInfoContext } from 'context/user';
import { errorHandler } from 'utils/errors';
import { CuftDataSavedSearchType } from 'types';
import { showToast } from 'ui/components/Toast';

type SavedSearchesModalPropsType = {
  isShowing: boolean;
  setIsSavedSearchesModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
};

const SavedSearchesModal = ({ isShowing, setIsSavedSearchesModalOpen }: SavedSearchesModalPropsType) => {
  const dispatch = useDispatch();
  const { userInfo } = useContext(UserInfoContext);
  const [savedSearches, setSavedSearches] = useState<CuftDataSavedSearchType[]>([]);
  const [selectedSavedSearch, setSelectedSavedSearch] = useState<string | undefined>();

  const onSavedSearchSave = () => {
    const savedSearch = savedSearches.find((savedSearch) => String(savedSearch.id) === selectedSavedSearch);
    dispatch(setCuftDataFilter(savedSearch?.search));
    setIsSavedSearchesModalOpen(false);
    showToast('Search options applied successfully.');
  };

  useEffect(() => {
    if (isShowing) {
      getCuftDataSavedSearch().then(setSavedSearches).catch(errorHandler);
    }
  }, [isShowing]);

  if (!isShowing) return null;

  return (
    <Modal
      title="Saved searches"
      width="s"
      onHide={() => setIsSavedSearchesModalOpen(false)}
      actionButtons={<Button text="Use" disabled={!selectedSavedSearch} onClick={onSavedSearchSave} />}
    >
      {savedSearches.length !== 0 ? (
        <FlexLayout sx={{ maxHeight: '600px', overflowY: 'scroll' }}>
          <RadioGroup
            options={savedSearches.map((savedSearch) => ({
              label: `${savedSearch.name} (${formatDateString(savedSearch.createdAt, userInfo.dateFormat, true)})`,
              value: String(savedSearch.id),
            }))}
            width="l"
            value={selectedSavedSearch}
            onChange={setSelectedSavedSearch}
            variant="column"
            radioVariant="secondary"
          />
        </FlexLayout>
      ) : (
        <Text color="bali-hai" variant="m-spaced">
          No saved searches.
        </Text>
      )}
    </Modal>
  );
};

export default SavedSearchesModal;
