import { useDispatch, useSelector } from 'react-redux';
import _ from 'lodash';

import { createCuftDataSavedSearch } from 'api';
import { cuftDataFiltersSelector, updateIsDirty } from 'reducers/cuftDataFilters.slice';
import { Button, Modal, TextInput } from 'ui';
import { errorHandler } from 'utils/errors';
import { showToast } from 'ui/components/Toast';

type SaveSearchModalPropsType = {
  isShowing: boolean;
  searchName: string;
  setSearchName: React.Dispatch<React.SetStateAction<string>>;
  setIsSaveSearchModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
};

const SaveSearchModal = ({
  isShowing,
  searchName,
  setSearchName,
  setIsSaveSearchModalOpen,
}: SaveSearchModalPropsType) => {
  const dispatch = useDispatch();
  const cuftDataFilters = useSelector(cuftDataFiltersSelector);

  const onSaveSearch = async () => {
    try {
      const filters = _.omit(cuftDataFilters, ['limit', 'offset', 'excludedCreditRatingIds', 'sortBy', 'isDirty']);

      await createCuftDataSavedSearch(searchName, { ...filters });
      dispatch(updateIsDirty(false));

      setIsSaveSearchModalOpen(false);
      setSearchName('');
      showToast('Search options saved successfully.');
    } catch (err) {
      errorHandler(err);
    }
  };

  if (!isShowing) return null;

  return (
    <Modal
      actionButtons={<Button disabled={!searchName} text="Save" size="l" onClick={onSaveSearch} />}
      title="Save this search"
      width="m"
      onHide={() => setIsSaveSearchModalOpen(false)}
    >
      <TextInput width="fullWidth" value={searchName} onChange={setSearchName} placeholder="Search name" />
    </Modal>
  );
};

export default SaveSearchModal;
