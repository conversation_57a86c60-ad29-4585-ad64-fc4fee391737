import { useState } from 'react';

import { FlexLayout } from 'ui';
import { ButtonActionMenu } from 'components/Shared';

import SaveSearchModal from './SaveSearchModal';
import SavedSearchesModal from './SavedSearchesModal';

const SaveSearch = () => {
  const [searchName, setSearchName] = useState<string>('');
  const [isSaveSearchModalOpen, setIsSaveSearchModalOpen] = useState<boolean>(false);
  const [isSavedSearchesModalOpen, setIsSavedSearchesModalOpen] = useState<boolean>(false);

  const options = [
    { label: 'Save this search', onClick: () => setIsSaveSearchModalOpen(true) },
    { label: 'See saved searches', onClick: () => setIsSavedSearchesModalOpen(true) },
  ];

  return (
    <FlexLayout>
      <ButtonActionMenu options={options} buttonText="Search Options" />
      <SaveSearchModal
        isShowing={isSaveSearchModalOpen}
        searchName={searchName}
        setSearchName={setSearchName}
        setIsSaveSearchModalOpen={setIsSaveSearchModalOpen}
      />
      <SavedSearchesModal
        isShowing={isSavedSearchesModalOpen}
        setIsSavedSearchesModalOpen={setIsSavedSearchesModalOpen}
      />
    </FlexLayout>
  );
};

export default SaveSearch;
