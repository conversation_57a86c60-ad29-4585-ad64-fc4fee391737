import { useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button, Card, DateInput, FlexLayout, Text } from 'ui';
import { cuftDataFiltersSelector, updateField } from 'reducers/cuftDataFilters.slice';
import { useOnClickOutside } from 'ui/hooks';

const IssueDateWithRangeButton = () => {
  const dispatch = useDispatch();
  const { issueDate, numberOfMonthsBeforeIssueDate } = useSelector(cuftDataFiltersSelector);
  const [isSelectDateRangeShowing, setIsSelectDateRangeShowing] = useState<boolean>(false);
  const dateRangeRef = useRef<HTMLElement>(null);
  useOnClickOutside(dateRangeRef, () => setIsSelectDateRangeShowing(false));

  const THREE_MONTHS = 3;
  const SIX_MONTHS = 6;
  const NINE_MONTHS = 9;
  const TWELVE_MONTHS = 12;

  return (
    <FlexLayout space={4}>
      <DateInput
        label="Issue Date"
        value={issueDate}
        width="fullWidth"
        sx={{ width: '100%' }}
        onChange={(issueDate: Date) => dispatch(updateField({ issueDate }))}
      />
      <FlexLayout ref={dateRangeRef} sx={{ position: 'relative' }}>
        <Button
          iconLeft={isSelectDateRangeShowing ? 'calendarDark' : 'calendarLight'}
          dataTestId="calendarButton"
          variant="gray"
          size="m2"
          text=""
          onClick={() => setIsSelectDateRangeShowing(!isSelectDateRangeShowing)}
          sx={{ mt: 'auto', height: 'input-height' }}
        />
        {isSelectDateRangeShowing && (
          <Card p={6} sx={{ position: 'absolute', top: '85px', zIndex: 10, width: 'max-content' }}>
            <FlexLayout flexDirection="column" space={4}>
              <Text color="shakespeare" variant="m-spaced-medium">
                Select date range
              </Text>
              <Text color="deep-sapphire" variant="s-spaced-medium">
                {`Records retrieved will have issue dates ranging from the entered date to ${numberOfMonthsBeforeIssueDate} months before it.`}
              </Text>
              <FlexLayout space={2}>
                <Button
                  text="3 MONTHS"
                  variant={numberOfMonthsBeforeIssueDate === THREE_MONTHS ? 'primary' : 'gray'}
                  size="s"
                  onClick={() => dispatch(updateField({ numberOfMonthsBeforeIssueDate: THREE_MONTHS }))}
                />
                <Button
                  text="6 MONTHS"
                  variant={numberOfMonthsBeforeIssueDate === SIX_MONTHS ? 'primary' : 'gray'}
                  size="s"
                  onClick={() => dispatch(updateField({ numberOfMonthsBeforeIssueDate: SIX_MONTHS }))}
                />
                <Button
                  text="9 MONTHS"
                  variant={numberOfMonthsBeforeIssueDate === NINE_MONTHS ? 'primary' : 'gray'}
                  size="s"
                  onClick={() => dispatch(updateField({ numberOfMonthsBeforeIssueDate: NINE_MONTHS }))}
                />
                <Button
                  text="12 MONTHS"
                  variant={numberOfMonthsBeforeIssueDate === TWELVE_MONTHS ? 'primary' : 'gray'}
                  size="s"
                  onClick={() => dispatch(updateField({ numberOfMonthsBeforeIssueDate: TWELVE_MONTHS }))}
                />
              </FlexLayout>
            </FlexLayout>
          </Card>
        )}
      </FlexLayout>
    </FlexLayout>
  );
};

export default IssueDateWithRangeButton;
