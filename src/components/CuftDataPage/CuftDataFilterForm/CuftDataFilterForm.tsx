import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { CreditRatingMultiSelect, CurrencyMultiSelect, CountryMultiSelect } from 'components/Shared';
import { cuftDataFiltersSelector, updateField, resetCuftDataFilter } from 'reducers/cuftDataFilters.slice';
import { Box, Button, Card, FlexLayout, WithTooltip } from 'ui';
import { CreditRatingValueType, CountryValueType, TrancheAssetClassType, CuftDataAvailableFiltersType } from 'types';

import TrancheAssetClassMultiSelect from './TrancheAssetClassMultiSelect';
import IssueDateWithRangeButton from './IssueDateWithRangeButton';
import TenorInputWithRangeButton from './TenorInputWithRangeButton';

type CuftDataFilterFormPropsType = {
  disabledUntilFilled: boolean;
  cuftDataAvailableFilters: CuftDataAvailableFiltersType;
};

const CuftDataFilterForm = ({ disabledUntilFilled, cuftDataAvailableFilters }: CuftDataFilterFormPropsType) => {
  const [isFilterFormShowing, setIsFilterFormShowing] = useState<boolean>(false);
  const dispatch = useDispatch();
  const { issueDate, creditRatings, currencies, countries, trancheAssetClasses } = useSelector(cuftDataFiltersSelector);

  return (
    <Card pb={6} pt={6}>
      <FlexLayout flexDirection="column" space={8}>
        <Box sx={{ display: 'grid', gridGap: 12, gridTemplateColumns: 'repeat(4, 1fr)' }}>
          <IssueDateWithRangeButton />
          <CreditRatingMultiSelect
            width="fullWidth"
            value={creditRatings}
            onChange={(creditRatings: CreditRatingValueType) => dispatch(updateField({ creditRatings }))}
          />
          <FlexLayout></FlexLayout>
          <FlexLayout justifyContent="flex-end" alignItems="flex-start" space={4}>
            <WithTooltip
              disabled={disabledUntilFilled}
              label="cuftFilterButton"
              tooltip="Select issue date and credit rating first."
            >
              <Button
                iconLeft="filter"
                size="s"
                text="Filter"
                variant="secondary"
                disabled={!issueDate || creditRatings.length === 0}
                sx={{ height: '32px' }}
                onClick={() => setIsFilterFormShowing(!isFilterFormShowing)}
              />
            </WithTooltip>
            <Button
              iconLeft="delete"
              size="s"
              text="Clear All"
              variant="secondary"
              sx={{ height: '32px' }}
              onClick={() => {
                setIsFilterFormShowing(false);
                dispatch(resetCuftDataFilter());
              }}
            />
          </FlexLayout>
        </Box>
        {isFilterFormShowing && (
          <Box sx={{ display: 'grid', gridGap: 12, gridTemplateColumns: 'repeat(4, 1fr)' }}>
            <TenorInputWithRangeButton />
            <CurrencyMultiSelect
              width="fullWidth"
              variant="short"
              availableOptions={cuftDataAvailableFilters.cuftDataAvailableCurrencies}
              value={currencies}
              onChange={(currencies: string[]) => dispatch(updateField({ currencies }))}
            />
            <CountryMultiSelect
              label="Borrower Country"
              width="fullWidth"
              availableOptions={cuftDataAvailableFilters.cuftDataAvailableBorrowerCountries}
              value={countries}
              onChange={(countries: CountryValueType[]) => dispatch(updateField({ countries }))}
            />
            <TrancheAssetClassMultiSelect
              width="fullWidth"
              includeSelectAll
              availableOptions={cuftDataAvailableFilters.cuftDataAvailableTrancheAssetClasses}
              value={trancheAssetClasses}
              onChange={(trancheAssetClasses: TrancheAssetClassType[]) =>
                dispatch(updateField({ trancheAssetClasses }))
              }
            />
          </Box>
        )}
      </FlexLayout>
    </Card>
  );
};

export default CuftDataFilterForm;
