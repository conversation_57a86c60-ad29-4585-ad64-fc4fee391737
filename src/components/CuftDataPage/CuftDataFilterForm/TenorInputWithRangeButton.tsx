import { useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button, Card, NumberInput, FlexLayout, Text } from 'ui';
import { cuftDataFiltersSelector, updateField } from 'reducers/cuftDataFilters.slice';
import { useOnClickOutside } from 'ui/hooks';

const TenorInputWithRangeButton = () => {
  const dispatch = useDispatch();
  const { tenor, numberOfYearsBeforeAndAfterTenor } = useSelector(cuftDataFiltersSelector);
  const [isSelectDateRangeShowing, setIsSelectDateRangeShowing] = useState<boolean>(false);
  const dateRangeRef = useRef<HTMLElement>(null);
  useOnClickOutside(dateRangeRef, () => setIsSelectDateRangeShowing(false));

  const ONE_YEAR = 1;
  const TWO_YEARS = 2;
  const THREE_YEARS = 3;
  const FOUR_YEARS = 4;
  const FIVE_YEARS = 5;
  const SIX_YEARS = 6;
  const yearWord = numberOfYearsBeforeAndAfterTenor === ONE_YEAR ? 'year' : 'years';

  return (
    <FlexLayout space={4}>
      <NumberInput
        placeholder="Enter number of years"
        label="Tenor"
        allowNegatives={false}
        value={tenor}
        width="fullWidth"
        sx={{ width: '100%' }}
        onChange={(tenor: number) => dispatch(updateField({ tenor }))}
      />
      <FlexLayout ref={dateRangeRef} sx={{ position: 'relative' }}>
        <Button
          iconLeft={isSelectDateRangeShowing ? 'calendarDark' : 'calendarLight'}
          dataTestId="calendarButton"
          variant="gray"
          size="m2"
          text=""
          onClick={() => setIsSelectDateRangeShowing(!isSelectDateRangeShowing)}
          sx={{ mt: 'auto', height: 'input-height' }}
        />
        {isSelectDateRangeShowing && (
          <Card p={6} sx={{ position: 'absolute', top: '85px', zIndex: 10, width: 'max-content' }}>
            <FlexLayout flexDirection="column" space={4}>
              <Text color="shakespeare" variant="m-spaced-medium">
                Select date range
              </Text>
              <Text color="deep-sapphire" variant="s-spaced-medium">
                {`Records retrieved will have tenors of the entered value plus and minus ${numberOfYearsBeforeAndAfterTenor} ${yearWord}.`}
              </Text>
              <FlexLayout space={2}>
                <Button
                  text="1 YEAR"
                  variant={numberOfYearsBeforeAndAfterTenor === ONE_YEAR ? 'primary' : 'gray'}
                  size="s"
                  onClick={() => dispatch(updateField({ numberOfYearsBeforeAndAfterTenor: ONE_YEAR }))}
                />
                <Button
                  text="2 YEARS"
                  variant={numberOfYearsBeforeAndAfterTenor === TWO_YEARS ? 'primary' : 'gray'}
                  size="s"
                  onClick={() => dispatch(updateField({ numberOfYearsBeforeAndAfterTenor: TWO_YEARS }))}
                />
                <Button
                  text="3 YEARS"
                  variant={numberOfYearsBeforeAndAfterTenor === THREE_YEARS ? 'primary' : 'gray'}
                  size="s"
                  onClick={() => dispatch(updateField({ numberOfYearsBeforeAndAfterTenor: THREE_YEARS }))}
                />
                <Button
                  text="4 YEARS"
                  variant={numberOfYearsBeforeAndAfterTenor === FOUR_YEARS ? 'primary' : 'gray'}
                  size="s"
                  onClick={() => dispatch(updateField({ numberOfYearsBeforeAndAfterTenor: FOUR_YEARS }))}
                />
                <Button
                  text="5 YEARS"
                  variant={numberOfYearsBeforeAndAfterTenor === FIVE_YEARS ? 'primary' : 'gray'}
                  size="s"
                  onClick={() => dispatch(updateField({ numberOfYearsBeforeAndAfterTenor: FIVE_YEARS }))}
                />
                <Button
                  text="6 YEARS"
                  variant={numberOfYearsBeforeAndAfterTenor === SIX_YEARS ? 'primary' : 'gray'}
                  size="s"
                  onClick={() => dispatch(updateField({ numberOfYearsBeforeAndAfterTenor: SIX_YEARS }))}
                />
              </FlexLayout>
            </FlexLayout>
          </Card>
        )}
      </FlexLayout>
    </FlexLayout>
  );
};

export default TenorInputWithRangeButton;
