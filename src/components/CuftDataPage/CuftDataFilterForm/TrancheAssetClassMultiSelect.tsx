import { MultiSelect } from 'ui';
import { TrancheAssetClassEnum } from 'enums/cuftData';
import { getOptionsFromArray } from 'utils/arrays';
import { getOptionsFromAvailableOptions } from 'utils/select';
import { SingleSelectWidthType, TrancheAssetClassType, DropdownOptionsType } from 'types';

type TrancheAssetClassMultiSelectPropsType = {
  label?: string;
  tooltip?: string;
  width?: SingleSelectWidthType;
  disabled?: boolean;
  includeSelectAll?: boolean;
  availableOptions: Array<TrancheAssetClassType>;
  value: TrancheAssetClassType[] | undefined;
  onChange: Function;
};

const options: DropdownOptionsType<TrancheAssetClassType> = getOptionsFromArray(Object.values(TrancheAssetClassEnum));

function TrancheAssetClassMultiSelect({
  label = 'Tier',
  tooltip,
  width = 'm',
  disabled,
  includeSelectAll,
  availableOptions,
  value,
  onChange,
}: TrancheAssetClassMultiSelectPropsType) {
  return (
    <MultiSelect
      label={label}
      options={getOptionsFromAvailableOptions(options, availableOptions)}
      tooltip={tooltip}
      width={width}
      disabled={disabled}
      includeSelectAll={includeSelectAll}
      nameSingular="Asset Class"
      namePlural="Asset Classes"
      value={value}
      onChange={onChange}
    />
  );
}

export default TrancheAssetClassMultiSelect;
