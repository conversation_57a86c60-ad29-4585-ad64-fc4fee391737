import { useState } from 'react';
import html2canvas from 'html2canvas';
import { saveAs } from 'file-saver';

import { Button, FlexLayout, WithTooltip } from 'ui';
import { showErrorToast } from 'ui/components/Toast';
import { errorHandler } from 'utils/errors';

import SavedSearch from './SavedSearch';

type TopRightButtonsPropsType = {
  disabledUntilFilled: boolean;
  summaryStatisticsTitle: 'Summary Statistics';
};

const TopRightButtons = ({ disabledUntilFilled, summaryStatisticsTitle }: TopRightButtonsPropsType) => {
  const [summaryIsLoading, setSummaryIsLoading] = useState(false);

  const screenshotSummaryStatistics = async () => {
    setSummaryIsLoading(true);
    try {
      const summaryStatisticsContainer = document.getElementById(summaryStatisticsTitle);
      if (!summaryStatisticsContainer) {
        throw new Error('No statistics to export.');
      }

      const canvas = await html2canvas(summaryStatisticsContainer);
      canvas.toBlob(
        (blob) => {
          if (!blob) {
            return showErrorToast('Generate summary failed.');
          }
          saveAs(blob!, 'Summary Statistics.png');
        },
        'image/png',
        1
      );
    } catch (err: any) {
      errorHandler(err);
    } finally {
      setSummaryIsLoading(false);
    }
  };

  return (
    <FlexLayout space={4}>
      <SavedSearch />
      <WithTooltip
        disabled={disabledUntilFilled}
        label="cuftGenerateSummaryButton"
        tooltip="Select issue date and credit rating first."
      >
        <Button
          loading={summaryIsLoading}
          size="s"
          text="Generate a summary"
          variant="secondary"
          disabled={disabledUntilFilled || summaryIsLoading}
          onClick={screenshotSummaryStatistics}
        />
      </WithTooltip>
    </FlexLayout>
  );
};

export default TopRightButtons;
