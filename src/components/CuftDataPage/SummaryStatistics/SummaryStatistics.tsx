import { Box, Card, Text } from 'ui';
import { CuftDataSummaryStatisticsType } from 'types';

import SummaryStatisticsHorizontalView from './SummaryStatisticsHorizontalView';
import SummaryStatisticsGraph from './SummaryStatisticsGraph';

type SummaryStatisticsPropsType = {
  summaryStatisticsData: CuftDataSummaryStatisticsType | null;
  summaryStatisticsTitle: 'Summary Statistics';
};

const SummaryStatistics = ({ summaryStatisticsData, summaryStatisticsTitle }: SummaryStatisticsPropsType) => {
  if (!summaryStatisticsData || summaryStatisticsData.numberOfObservations === 0) {
    return (
      <Card p={6} title={summaryStatisticsTitle}>
        <Text color="bali-hai" variant="m-spaced">
          No statistics for current filters.
        </Text>
      </Card>
    );
  }

  return (
    <Box id={summaryStatisticsTitle} sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
      <SummaryStatisticsHorizontalView title={summaryStatisticsTitle} summaryStatisticsData={summaryStatisticsData} />
      <SummaryStatisticsGraph summaryStatisticsData={summaryStatisticsData} />
    </Box>
  );
};

export default SummaryStatistics;
