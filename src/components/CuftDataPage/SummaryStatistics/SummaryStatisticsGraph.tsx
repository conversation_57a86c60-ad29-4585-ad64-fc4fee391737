import { useContext, useMemo } from 'react';
import { Bar } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, Tooltip, BarElement, ChartOptions } from 'chart.js';

import { Box, Card } from 'ui';
import { UserInfoContext } from 'context/user';
import { displayNumber2 } from 'utils/strings';
import { CuftDataSummaryStatisticsType } from 'types';
ChartJS.register(CategoryScale, LinearScale, BarElement, Tooltip);

type SummaryStatisticsGraphPropsType = {
  summaryStatisticsData: CuftDataSummaryStatisticsType | null;
};

const SummaryStatisticsGraph = ({ summaryStatisticsData }: SummaryStatisticsGraphPropsType) => {
  const { userInfo } = useContext(UserInfoContext);
  const numberDisplayOptions = { decimalPoint: userInfo.decimalPoint, defaultValue: '-', minDig: 2 };

  const options: ChartOptions<'bar'> = {
    maintainAspectRatio: false,
    plugins: {
      tooltip: {
        callbacks: {
          label: (tooltipItem) => `${displayNumber2(tooltipItem.raw, numberDisplayOptions)} bps`,
        },
      },
      legend: { display: false },
    },
    scales: {
      y: {
        type: 'linear',
        ticks: { maxTicksLimit: 8, callback: (value) => `${value} bps` },
      },
    },
    // plugins: { legend: false },
  };

  const memoizedData = useMemo(() => {
    if (!summaryStatisticsData) return { labels: [], datasets: [] };

    return {
      labels: ['Minimum', 'Lower quartile', 'Median', 'Upper Quartile', 'Maximum'],
      datasets: [
        {
          data: [
            summaryStatisticsData.minimum,
            summaryStatisticsData.lowerQuartile,
            summaryStatisticsData.median,
            summaryStatisticsData.upperQuartile,
            summaryStatisticsData.maximum,
          ],
          backgroundColor: '#12246C',
        },
      ],
    };
  }, [summaryStatisticsData]);

  return (
    <Card p={6} title="Interquartile Range">
      <Box sx={{ height: '310px' }}>
        <Bar options={options} data={memoizedData} />
      </Box>
    </Card>
  );
};

export default SummaryStatisticsGraph;
