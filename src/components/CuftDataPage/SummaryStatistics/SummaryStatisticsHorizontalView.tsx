import { useContext } from 'react';

import { Card } from 'ui';
import { displayNumber2 } from 'utils/strings';
import { UserInfoContext } from 'context/user';
import { CuftDataSummaryStatisticsType } from 'types';

import { ReportCharacteristicsColumn } from '../../ReportViewPage/ReportCharacteristics';

type SummaryStatisticsHorizontalViewPropsType = {
  title: string;
  summaryStatisticsData: CuftDataSummaryStatisticsType;
};

const SummaryStatisticsHorizontalView = ({
  title,
  summaryStatisticsData,
}: SummaryStatisticsHorizontalViewPropsType) => {
  const { userInfo } = useContext(UserInfoContext);
  const numberDisplayOptions = { decimalPoint: userInfo.decimalPoint, minDig: 2 };

  return (
    <Card p={6} title={title}>
      <ReportCharacteristicsColumn
        data={[
          { label: 'Maximum', value: `${displayNumber2(summaryStatisticsData.maximum, numberDisplayOptions)} bps` },
          {
            label: 'Upper Quartile',
            value: `${displayNumber2(summaryStatisticsData.upperQuartile, numberDisplayOptions)} bps`,
          },
          { label: 'Median', value: `${displayNumber2(summaryStatisticsData.median, numberDisplayOptions)} bps` },
          {
            label: 'Lower quartile',
            value: `${displayNumber2(summaryStatisticsData.lowerQuartile, numberDisplayOptions)} bps`,
          },
          { label: 'Minimum', value: `${displayNumber2(summaryStatisticsData.minimum, numberDisplayOptions)} bps` },
          { label: 'Number of observations', value: summaryStatisticsData.numberOfObservations },
        ]}
      />
    </Card>
  );
};

export default SummaryStatisticsHorizontalView;
