import { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';

import { FlexLayout, PageLayout, Tabs } from 'ui';
import { routesEnum } from 'routes';
import { useQuery } from 'hooks';
import ClientFeaturesComponent from 'components/ClientFeaturesPage/ClientFeaturesComponent';

import SettingsPage from './SettingsPage';
import ClientSettings from './ClientSettings';
import TemplatesPage from './TemplatesPage';
import Changelog from './Changelog';

const USER_SETTINGS = 'User Settings';
const CLIENT_SETTINGS = 'Client settings';
const FEATURES = 'Features';
const TEMPLATES = 'Templates';
const CHANGE_LOG = 'Change Log';

const tabs = [
  { label: USER_SETTINGS, value: USER_SETTINGS },
  { label: CLIENT_SETTINGS, value: CLIENT_SETTINGS },
  { label: FEATURES, value: FEATURES },
  { label: TEMPLATES, value: TEMPLATES },
  // { label: CHANGE_LOG, value: CHANGE_LOG },
];

const SettingsFeaturesPage = () => {
  const history = useHistory();
  const query = useQuery();
  const [selectedTab, setSelectedTab] = useState(USER_SETTINGS);

  const onTabSelect = (tabName: string) => history.replace(`${routesEnum.SETTINGS}?tab=${tabName}`);

  useEffect(() => setSelectedTab(query.get('tab') || USER_SETTINGS), [query]);

  return (
    <PageLayout title="Profile & Settings">
      <FlexLayout alignItems="center" justifyContent="space-between">
        <Tabs selectedTab={selectedTab} tabs={tabs} onTabSelect={onTabSelect} />
      </FlexLayout>
      {selectedTab === USER_SETTINGS && <SettingsPage />}
      {selectedTab === CLIENT_SETTINGS && <ClientSettings />}
      {selectedTab === FEATURES && <ClientFeaturesComponent isClientView={true} />}
      {selectedTab === TEMPLATES && <TemplatesPage />}
      {selectedTab === CHANGE_LOG && <Changelog />}
    </PageLayout>
  );
};

export default SettingsFeaturesPage;
