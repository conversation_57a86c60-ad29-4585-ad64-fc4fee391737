import type { DateFormatType, SingleSelectWidthType } from 'types';
import { SingleSelect } from 'ui';
import { getOptionsFromArray } from 'utils/arrays';

const dateFormats = ['YYYY-MM-DD', 'DD-MM-YYYY', 'MM-DD-YYYY', 'DD MONTH YYYY'];
const options = getOptionsFromArray(dateFormats);

type DateFormatSingleSelectProps = {
  label?: string;
  width?: SingleSelectWidthType;
  value: DateFormatType;
  onChange: Function;
  tooltip?: string;
  disabled?: boolean;
};

const DateFormatSingleSelect = ({
  label = 'Date format',
  value,
  width = 'm',
  onChange,
  tooltip,
  disabled,
}: DateFormatSingleSelectProps) => (
  <SingleSelect
    label={label}
    options={options}
    disabled={disabled}
    tooltip={tooltip}
    value={value}
    width={width}
    onChange={onChange}
  />
);

export default DateFormatSingleSelect;
