import { TemplateFileLabelsEnum } from 'enums/templateFiles';
import type { SingleSelectWidthType, TemplateFileTypeType } from 'types';
import { SingleSelect } from 'ui';

type OptionsType = { label: string; value: TemplateFileLabelsEnum }[];

const fileTypeToOptionsMapper = {
  loan: [
    {
      label: 'Legal Agreement (fixed)',
      value: TemplateFileLabelsEnum.LOAN_FIXED_AGREEMENT,
    },
    {
      label: 'Legal Agreement (fixed no prepayment)',
      value: TemplateFileLabelsEnum.LOAN_FIXED_AGREEMENT_NO_PREPAYMENT,
    },
    {
      label: 'Legal Agreement (float)',
      value: TemplateFileLabelsEnum.LOAN_FLOAT_AGREEMENT,
    },
    {
      label: 'Report (standalone, lender perspective)',
      value: TemplateFileLabelsEnum.LOAN_STANDALONE_REPORT,
    },
    {
      label: 'Report (implicit support adj., lender perspective)',
      value: TemplateFileLabelsEnum.LOAN_ADJUSTED_REPORT,
    },
    {
      label: 'Report (standalone)',
      value: TemplateFileLabelsEnum.LOAN_STANDALONE_REPORT_NO_LENDER_PERSPECTIVE,
    },
    {
      label: 'Report (implicit support adj.)',
      value: TemplateFileLabelsEnum.LOAN_ADJUSTED_REPORT_NO_LENDER_PERSPECTIVE,
    },
    {
      label: 'Report (standalone prepayment)',
      value: TemplateFileLabelsEnum.LOAN_PREPAYMENT_STANDALONE_REPORT,
    },
    {
      label: 'Report (implicit support adj. prepayment)',
      value: TemplateFileLabelsEnum.LOAN_PREPAYMENT_ADJUSTED_REPORT,
    },
    {
      label: 'Report (standalone, no lender perspective prepayment)',
      value: TemplateFileLabelsEnum.LOAN_PREPAYMENT_STANDALONE_REPORT_NO_LENDER_PERSPECTIVE,
    },
    {
      label: 'Report (implicit support adj., no lender perspective prepayment)',
      value: TemplateFileLabelsEnum.LOAN_PREPAYMENT_ADJUSTED_REPORT_NO_LENDER_PERSPECTIVE,
    },
  ],
  b2bLoan: [
    {
      label: 'Legal Agreement (fixed)',
      value: TemplateFileLabelsEnum.B2B_LOAN_FIXED_AGREEMENT,
    },
    {
      label: 'Legal Agreement (float)',
      value: TemplateFileLabelsEnum.B2B_LOAN_FLOAT_AGREEMENT,
    },
    {
      label: 'Report (standalone)',
      value: TemplateFileLabelsEnum.B2B_LOAN_STANDALONE_REPORT,
    },
    {
      label: 'Report (implicit support adj.)',
      value: TemplateFileLabelsEnum.B2B_LOAN_ADJUSTED_REPORT,
    },
  ],
  guarantee: [
    {
      label: 'Legal Agreement',
      value: TemplateFileLabelsEnum.GUARANTEE_AGREEMENT,
    },
    {
      label: 'Report (yield - expected loss approach) (standalone)',
      value: TemplateFileLabelsEnum.GUARANTEE_YIELD_EXPECTED_LOSS_STANDALONE_REPORT,
    },
    {
      label: 'Report (yield - expected loss approach) (implicit support adj.)',
      value: TemplateFileLabelsEnum.GUARANTEE_YIELD_EXPECTED_LOSS_ADJUSTED_REPORT,
    },
    {
      label: 'Report (security approach) (standalone)',
      value: TemplateFileLabelsEnum.GUARANTEE_SECURITY_STANDALONE_REPORT,
    },
    {
      label: 'Report (security approach) (implicit support adj.)',
      value: TemplateFileLabelsEnum.GUARANTEE_SECURITY_ADJUSTED_REPORT,
    },
  ],
} as const;

const getOptions = (fileType: TemplateFileTypeType | null): Readonly<OptionsType> => {
  if (!fileType) return [];

  return fileTypeToOptionsMapper[fileType];
};

type FileLabelSingleSelectProps = {
  label?: string;
  width?: SingleSelectWidthType;
  disabled: boolean;
  fileType: TemplateFileTypeType | null;
  value: TemplateFileLabelsEnum | null;
  onChange: React.Dispatch<React.SetStateAction<TemplateFileLabelsEnum | null>>;
};

const FileLabelSingleSelect = ({
  label = 'File label',
  width = 'fullWidth',
  disabled,
  fileType,
  value,
  onChange,
}: FileLabelSingleSelectProps) => (
  <SingleSelect
    label={label}
    options={getOptions(fileType)}
    width={width}
    disabled={disabled}
    value={value}
    onChange={onChange}
  />
);

export default FileLabelSingleSelect;
