import { SingleSelect } from 'ui';
import type { SingleSelectWidthType, TemplateFileTypeType } from 'types';

type OptionsType = {
  label: 'Loan' | 'Back-to-back Loan' | 'Guarantee';
  value: TemplateFileTypeType;
}[];

const options: OptionsType = [
  { label: 'Loan', value: 'loan' },
  { label: 'Back-to-back Loan', value: 'b2bLoan' },
  { label: 'Guarantee', value: 'guarantee' },
];

type FileTypeSingleSelectProps = {
  label?: string;
  width?: SingleSelectWidthType;
  value: TemplateFileTypeType | null;
  onChange: React.Dispatch<React.SetStateAction<TemplateFileTypeType | null>>;
};

const FileTypeSingleSelect = ({
  label = 'File type',
  width = 'fullWidth',
  value,
  onChange,
}: FileTypeSingleSelectProps) => (
  <SingleSelect label={label} options={options} value={value} width={width} onChange={onChange} />
);

export default FileTypeSingleSelect;
