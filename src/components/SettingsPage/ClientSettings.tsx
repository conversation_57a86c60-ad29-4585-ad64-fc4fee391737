import { useContext, useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';

import { updateClient } from 'api';
import { UserInfoContext } from 'context/user';
import { isAdmin, rolesEnum } from 'enums';
import { useUnsavedChangesWarning } from 'hooks';
import { DateFormatType, DecimalPointType } from 'types';
import { Box, Button, Card, FlexLayout, RadioGroup, Text } from 'ui';
import { showToast } from 'ui/components/Toast';
import { errorHandler } from 'utils/errors';
import { DateFormatSingleSelect, DecimalPointSingleSelect } from './SingleSelects';

const ClientSettings = () => {
  const { userInfo, setUserInfo } = useContext(UserInfoContext);
  const [isLoanApproachCalculated, setIsLoanApproachCalculated] = useState(false);
  const [isCreditRatingAnonymized, setIsCreditRatingAnonymized] = useState(false);
  const [dateFormat, setDateFormat] = useState<DateFormatType>('YYYY-MM-DD');
  const [decimalPoint, setDecimalPoint] = useState<DecimalPointType>('dot');
  const [isDirty, setIsDirty] = useState(false);
  const history = useHistory();
  const [Prompt] = useUnsavedChangesWarning({ isDirty });

  const onSave = async () => {
    try {
      const client = await updateClient({
        isLoanApproachCalculated,
        isCreditRatingAnonymized,
        dateFormat,
        decimalPoint,
      });
      setUserInfo({ ...userInfo, client: { ...userInfo.client, ...client } });
      showToast('Successfully updated.');
      setIsDirty(false);
    } catch (error) {
      errorHandler(error);
    }
  };

  const onInputChange = (setter: React.Dispatch<React.SetStateAction<boolean>>) => (value: boolean) => {
    setIsDirty(true);
    setter(value);
  };

  useEffect(() => {
    setIsLoanApproachCalculated(userInfo.client.isLoanApproachCalculated);
    setIsCreditRatingAnonymized(userInfo.client.isCreditRatingAnonymized);
    setDateFormat(userInfo.client.dateFormat);
    setDecimalPoint(userInfo.client.decimalPoint);
  }, [userInfo]);

  return (
    <>
      <Card p={6} space={4}>
        <Text variant="2l-spaced" color="deep-sapphire">
          Loans
        </Text>
        <Box
          sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(3, 1fr)' }}
          disabled={!isAdmin(userInfo.role)}
        >
          <RadioGroup
            label="Lender perspective"
            options={[
              { label: 'On', value: true },
              { label: 'Off', value: false },
            ]}
            tooltip="
              When the lender perspective is enabled, the software evaluates the benchmark<br /> 
              from both the lender's and the borrower's viewpoints, applying whichever entity<br />
              has the higher loan cost. <br /><br />
              This ensures the lender does not lend below their own cost. When the <br />
              lender perspective is disabled, only the borrower's viewpoint is considered,<br />
              and its loan cost is applied in the benchmarking analysis."
            width="fullWidth"
            value={isLoanApproachCalculated}
            onChange={onInputChange(setIsLoanApproachCalculated)}
          />
        </Box>
      </Card>
      <Card p={6} space={4}>
        <Text variant="2l-spaced" color="deep-sapphire">
          Credit ratings
        </Text>
        <Box
          sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(3, 1fr)' }}
          disabled={!isAdmin(userInfo.role)}
        >
          <RadioGroup
            label="Anonymize credit ratings"
            options={[
              { label: 'On', value: true },
              { label: 'Off', value: false },
            ]}
            tooltip="
              Set the default state for anonymizing credit ratings.<br /> 
              This can still be adjusted manually in the Credit Rating section."
            width="fullWidth"
            value={isCreditRatingAnonymized}
            onChange={onInputChange(setIsCreditRatingAnonymized)}
          />
        </Box>
      </Card>

      <Card p={6} space={4}>
        <Text variant="2l-spaced" color="deep-sapphire">
          Date &amp; Number Formats {'(document exports)'}
        </Text>
        <FlexLayout space={8}>
          <DateFormatSingleSelect
            value={dateFormat as DateFormatType}
            onChange={(dateFormat: DateFormatType) => setDateFormat(dateFormat)}
            tooltip="Sets how dates appear in exported reports and documents <br /> for you company’s entire account. <br />
            This setting won't change how dates display in your interface."
            disabled={userInfo.role === rolesEnum.USER}
          />
          <DecimalPointSingleSelect
            value={decimalPoint as DecimalPointType}
            onChange={(decimalPoint: DecimalPointType) => setDecimalPoint(decimalPoint)}
            tooltip="Sets how numbers format in exported reports and documents <br /> for your company’s entire account (e.g., 1,234.56 vs 1.234,56). <br /> This setting won't change how numbers display in your interface."
            disabled={userInfo.role === rolesEnum.USER}
          />
        </FlexLayout>
      </Card>

      <FlexLayout alignItems="flex-end" flexGrow="1" justifyContent="space-between">
        <Button text="Back" variant="gray" onClick={history.goBack} />
        <Button text="Save" disabled={!isAdmin(userInfo.role)} onClick={onSave} />
      </FlexLayout>
      {Prompt}
    </>
  );
};

export default ClientSettings;
