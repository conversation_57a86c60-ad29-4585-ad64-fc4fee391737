import { saveAs } from 'file-saver';

import { getStandardTemplateFile } from 'api';
import { UserInfoContext } from 'context/user';
import { TemplateFileLabelsEnum } from 'enums/templateFiles';
import { useContext } from 'react';
import { Box, Card, FlexLayout, Icon, Text } from 'ui';
import { showToast } from 'ui/components/Toast';
import { errorHandler } from 'utils/errors';

const StandardTemplates = () => {
  const { userInfo } = useContext(UserInfoContext);

  const onDownloadStandardTemplate = (typeOfFile: TemplateFileLabelsEnum) => async () => {
    try {
      const response = await getStandardTemplateFile(typeOfFile);
      saveAs(response, response.name);
      showToast('File successfully downloaded.');
    } catch (err: any) {
      errorHandler(err);
    }
  };

  return (
    <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
      <Card p={6} space={4}>
        <Text variant="s-spaced" color="shakespeare">
          Standard templates - Loans
        </Text>
        <FlexLayout alignItems="center" justifyContent="space-between">
          <FlexLayout space={4} onClick={onDownloadStandardTemplate(TemplateFileLabelsEnum.LOAN_FIXED_AGREEMENT)}>
            <Text variant="m-spaced-medium">Legal Agreement (fixed)</Text>
            <Icon icon="download" size="s" color="shakespeare" />
          </FlexLayout>
        </FlexLayout>
        {userInfo.features.prepayment && (
          <FlexLayout alignItems="center" justifyContent="space-between">
            <FlexLayout
              space={4}
              onClick={onDownloadStandardTemplate(TemplateFileLabelsEnum.LOAN_FIXED_AGREEMENT_NO_PREPAYMENT)}
            >
              <Text variant="m-spaced-medium">Legal Agreement (fixed no prepayment)</Text>
              <Icon icon="download" size="s" color="shakespeare" />
            </FlexLayout>
          </FlexLayout>
        )}
        <FlexLayout alignItems="center" justifyContent="space-between">
          <FlexLayout space={4} onClick={onDownloadStandardTemplate(TemplateFileLabelsEnum.LOAN_FLOAT_AGREEMENT)}>
            <Text variant="m-spaced-medium">Legal Agreement (float)</Text>
            <Icon icon="download" size="s" color="shakespeare" />
          </FlexLayout>
        </FlexLayout>
        <FlexLayout alignItems="center" justifyContent="space-between">
          <FlexLayout space={4} onClick={onDownloadStandardTemplate(TemplateFileLabelsEnum.LOAN_STANDALONE_REPORT)}>
            <Text variant="m-spaced-medium">Transfer Pricing Report Standalone Credit Rating</Text>
            <Icon icon="download" size="s" color="shakespeare" />
          </FlexLayout>
        </FlexLayout>
        <FlexLayout alignItems="center" justifyContent="space-between">
          <FlexLayout space={4} onClick={onDownloadStandardTemplate(TemplateFileLabelsEnum.LOAN_ADJUSTED_REPORT)}>
            <Text variant="m-spaced-medium">Transfer Pricing Report Implicit Support Adjusted Credit Rating</Text>
            <Icon icon="download" size="s" color="shakespeare" />
          </FlexLayout>
        </FlexLayout>
        <FlexLayout alignItems="center" justifyContent="space-between">
          <FlexLayout
            space={4}
            onClick={onDownloadStandardTemplate(TemplateFileLabelsEnum.LOAN_STANDALONE_REPORT_NO_LENDER_PERSPECTIVE)}
          >
            <Text variant="m-spaced-medium">
              Transfer Pricing Report Standalone Credit Rating No Lender Perspective
            </Text>
            <Icon icon="download" size="s" color="shakespeare" />
          </FlexLayout>
        </FlexLayout>
        <FlexLayout alignItems="center" justifyContent="space-between">
          <FlexLayout
            space={4}
            onClick={onDownloadStandardTemplate(TemplateFileLabelsEnum.LOAN_ADJUSTED_REPORT_NO_LENDER_PERSPECTIVE)}
          >
            <Text variant="m-spaced-medium">
              Transfer Pricing Report Implicit Support Adjusted Credit Rating No Lender Perspective
            </Text>
            <Icon icon="download" size="s" color="shakespeare" />
          </FlexLayout>
        </FlexLayout>
        {userInfo.features.prepayment && (
          <>
            <FlexLayout alignItems="center" justifyContent="space-between">
              <FlexLayout
                space={4}
                onClick={onDownloadStandardTemplate(TemplateFileLabelsEnum.LOAN_PREPAYMENT_STANDALONE_REPORT)}
              >
                <Text variant="m-spaced-medium">Report Standalone Prepayment</Text>
                <Icon icon="download" size="s" color="shakespeare" />
              </FlexLayout>
            </FlexLayout>
            <FlexLayout alignItems="center" justifyContent="space-between">
              <FlexLayout
                space={4}
                onClick={onDownloadStandardTemplate(TemplateFileLabelsEnum.LOAN_PREPAYMENT_ADJUSTED_REPORT)}
              >
                <Text variant="m-spaced-medium">Report Implicit Support Adjusted Prepayment</Text>
                <Icon icon="download" size="s" color="shakespeare" />
              </FlexLayout>
            </FlexLayout>
            <FlexLayout alignItems="center" justifyContent="space-between">
              <FlexLayout
                space={4}
                onClick={onDownloadStandardTemplate(
                  TemplateFileLabelsEnum.LOAN_PREPAYMENT_STANDALONE_REPORT_NO_LENDER_PERSPECTIVE
                )}
              >
                <Text variant="m-spaced-medium">Report Standalone No Lender Perspective Prepayment</Text>
                <Icon icon="download" size="s" color="shakespeare" />
              </FlexLayout>
            </FlexLayout>
            <FlexLayout alignItems="center" justifyContent="space-between">
              <FlexLayout
                space={4}
                onClick={onDownloadStandardTemplate(
                  TemplateFileLabelsEnum.LOAN_PREPAYMENT_ADJUSTED_REPORT_NO_LENDER_PERSPECTIVE
                )}
              >
                <Text variant="m-spaced-medium">Report Implicit Support Adjusted No Lender Perspective Prepayment</Text>
                <Icon icon="download" size="s" color="shakespeare" />
              </FlexLayout>
            </FlexLayout>
          </>
        )}
      </Card>
      <Card p={6} space={4}>
        <Text variant="s-spaced" color="shakespeare">
          Standard templates - Guarantees
        </Text>
        <FlexLayout alignItems="center" justifyContent="space-between">
          <FlexLayout space={4} onClick={onDownloadStandardTemplate(TemplateFileLabelsEnum.GUARANTEE_AGREEMENT)}>
            <Text variant="m-spaced-medium">Legal Agreement</Text>
            <Icon icon="download" size="s" color="shakespeare" />
          </FlexLayout>
        </FlexLayout>
        <FlexLayout alignItems="center" justifyContent="space-between">
          <FlexLayout
            space={4}
            onClick={onDownloadStandardTemplate(TemplateFileLabelsEnum.GUARANTEE_YIELD_EXPECTED_LOSS_STANDALONE_REPORT)}
          >
            <Text variant="m-spaced-medium">
              Transfer Pricing Report Yield - Expected Loss Approach Standalone Credit Rating
            </Text>
            <Icon icon="download" size="s" color="shakespeare" />
          </FlexLayout>
        </FlexLayout>
        <FlexLayout alignItems="center" justifyContent="space-between">
          <FlexLayout
            space={4}
            onClick={onDownloadStandardTemplate(TemplateFileLabelsEnum.GUARANTEE_YIELD_EXPECTED_LOSS_ADJUSTED_REPORT)}
          >
            <Text variant="m-spaced-medium">
              Transfer Pricing Report Yield - Expected Loss Approach Implicit Support Adjusted Credit Rating
            </Text>
            <Icon icon="download" size="s" color="shakespeare" />
          </FlexLayout>
        </FlexLayout>
        <FlexLayout alignItems="center" justifyContent="space-between">
          <FlexLayout
            space={4}
            onClick={onDownloadStandardTemplate(TemplateFileLabelsEnum.GUARANTEE_SECURITY_STANDALONE_REPORT)}
          >
            <Text variant="m-spaced-medium">Transfer Pricing Report Security Approach Standalone Credit Rating</Text>
            <Icon icon="download" size="s" color="shakespeare" />
          </FlexLayout>
        </FlexLayout>
        <FlexLayout alignItems="center" justifyContent="space-between">
          <FlexLayout
            space={4}
            onClick={onDownloadStandardTemplate(TemplateFileLabelsEnum.GUARANTEE_SECURITY_ADJUSTED_REPORT)}
          >
            <Text variant="m-spaced-medium">
              Transfer Pricing Report Security Approach Implicit Support Adjusted Credit Rating
            </Text>
            <Icon icon="download" size="s" color="shakespeare" />
          </FlexLayout>
        </FlexLayout>
      </Card>
    </Box>
  );
};

export default StandardTemplates;
