import { useContext, useEffect, useState } from 'react';

import { getAllClientTemplateFiles, uploadClientTemplateFile } from 'api';
import { TemplateFileLabelsEnum, TemplateFileTypeEnum } from 'enums/templateFiles';
import { useCompanies } from 'hooks';
import { TemplateFileType, TemplateFileTypeType, TemplateTypeType } from 'types';
import { LoadingSpinner } from 'ui';
import { showErrorToast, showToast } from 'ui/components/Toast';
import { errorHandler } from 'utils/errors';

import { UserInfoContext } from 'context';
import { verifyFilePlaceholders } from './clientTemplateFieldVerifyUtil';
import ClientTemplatesTable from './ClientTemplatesTable';
import { getTemplateFilesByType } from './ClientTemplatesTable/ClientTemplatesTable.utils';
import UploadTemplateModal from './UploadTemplateModal';

type AllTemplatesType = {
  clientTemplates: TemplateFileType[];
  countryTemplates: TemplateFileType[];
  companyTemplates: TemplateFileType[];
};

const ClientTemplates = () => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [selectedTemplateFiles, setSelectedTemplateFiles] = useState<TemplateFileType[]>([]);
  const [isUploadTemplateModalShowing, setIsUploadTemplateModalShowing] = useState<boolean>(false);
  const [uploadedFile, setUploadedFile] = useState<File>();
  const [isFileUploading, setIsFileUploading] = useState<boolean>(false);
  const [fileLabel, setFileLabel] = useState<TemplateFileLabelsEnum | null>(null);
  const [fileType, setFileType] = useState<TemplateFileTypeType | null>(null);
  const [country, setCountry] = useState<string | null>(null);
  const [company, setCompany] = useState<any>(null);
  const [templateType, setTemplateType] = useState<TemplateTypeType>(TemplateFileTypeEnum.CLIENT);
  const [allTemplates, setAllTemplates] = useState<AllTemplatesType>({
    clientTemplates: [],
    countryTemplates: [],
    companyTemplates: [],
  });
  useCompanies();
  const {
    userInfo: { features: userFeatures },
  } = useContext(UserInfoContext);

  const onImportFileClick = (file: File) => {
    if (!file) return showErrorToast('File uploaded failed. Please try again.');

    setUploadedFile(file);
    setIsUploadTemplateModalShowing(true);
  };

  const handleOnUploadClick = async () => {
    if (!uploadedFile) return showErrorToast('Please upload file.');
    if (!fileLabel) return showErrorToast('Please select label.');
    if (!fileType) return showErrorToast('Please select file type.');
    setIsFileUploading(true);

    try {
      await verifyFilePlaceholders(uploadedFile, fileLabel, userFeatures);
      await uploadClientTemplateFile(uploadedFile, fileLabel, fileType, country, company ? company.id : null);
      await getAllClientTemplateFiles().then((allTemplates) => {
        const templatesByType = getTemplateFilesByType(allTemplates);
        setAllTemplates(templatesByType);
        if (templateType === TemplateFileTypeEnum.CLIENT) setSelectedTemplateFiles(templatesByType.clientTemplates);
        else if (templateType === TemplateFileTypeEnum.COUNTRY) {
          setSelectedTemplateFiles(templatesByType.countryTemplates);
        } else if (templateType === TemplateFileTypeEnum.COMPANY) {
          setSelectedTemplateFiles(templatesByType.companyTemplates);
        }
      });
      showToast('Template uploaded successfully.');
      setIsUploadTemplateModalShowing(false);
      setFileType(null);
      setCountry(null);
      setCompany(null);
    } catch (err: any) {
      errorHandler(err);
    } finally {
      setIsFileUploading(false);
    }
  };

  useEffect(() => {
    getAllClientTemplateFiles()
      .then((allTemplates) => {
        const templatesByType = getTemplateFilesByType(allTemplates);
        setAllTemplates(templatesByType);
        setSelectedTemplateFiles(templatesByType.clientTemplates);
      })
      .catch(errorHandler)
      .finally(() => setIsLoading(false));
  }, []);

  useEffect(() => {
    if (templateType === TemplateFileTypeEnum.CLIENT) setSelectedTemplateFiles(allTemplates.clientTemplates);
    else if (templateType === TemplateFileTypeEnum.COUNTRY) setSelectedTemplateFiles(allTemplates.countryTemplates);
    else if (templateType === TemplateFileTypeEnum.COMPANY) setSelectedTemplateFiles(allTemplates.companyTemplates);
  }, [templateType, allTemplates]);

  if (isLoading) return <LoadingSpinner />;

  return (
    <>
      <ClientTemplatesTable
        templateType={templateType}
        setTemplateType={setTemplateType}
        templateFiles={selectedTemplateFiles}
        setTemplateFiles={setSelectedTemplateFiles}
        setIsUploadTemplateModalShowing={setIsUploadTemplateModalShowing}
        onImportFileClick={onImportFileClick}
      />
      <UploadTemplateModal
        uploadedFile={uploadedFile}
        isFileUploading={isFileUploading}
        fileLabel={fileLabel}
        setFileLabel={setFileLabel}
        fileType={fileType}
        setFileType={setFileType}
        country={country}
        setCountry={setCountry}
        company={company}
        setCompany={setCompany}
        isShowing={isUploadTemplateModalShowing}
        handleOnUploadClick={handleOnUploadClick}
        handleOnHide={() => setIsUploadTemplateModalShowing(false)}
      />
    </>
  );
};

export default ClientTemplates;
