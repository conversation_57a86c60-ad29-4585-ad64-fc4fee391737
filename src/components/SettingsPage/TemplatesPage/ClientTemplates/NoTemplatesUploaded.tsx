import { Button, FileInput, FlexLayout, Text, WithTooltip } from 'ui';

type NoTemplatesUploadedPropsType = {
  isShowing: boolean;
  setIsUploadTemplateModalShowing: React.Dispatch<React.SetStateAction<boolean>>;
  onImportFileClick: Function;
};

const NoTemplatesUploaded = ({
  isShowing,
  setIsUploadTemplateModalShowing,
  onImportFileClick,
}: NoTemplatesUploadedPropsType) => {
  if (!isShowing) return null;

  return (
    <FlexLayout flexDirection="column" alignItems="center" justifyContent="center" space={4}>
      <Text variant="2l-spaced" color="deep-sapphire">
        Upload template
      </Text>
      <Text variant="m-spaced-medium" color="bali-hai">
        Upload your template here
      </Text>

      <WithTooltip label="firstTemplateFileInput" tooltip="File must be .docx" disabled>
        <FileInput onChange={onImportFileClick} accept=".docx">
          <Button
            iconLeft="upload"
            variant="secondary"
            size="s"
            onClick={() => setIsUploadTemplateModalShowing(true)}
            text="Upload Template"
          />
        </FileInput>
      </WithTooltip>
    </FlexLayout>
  );
};

export default NoTemplatesUploaded;
