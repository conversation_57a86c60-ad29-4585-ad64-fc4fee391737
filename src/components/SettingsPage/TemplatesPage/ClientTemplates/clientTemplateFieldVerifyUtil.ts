import Docxtemplater from 'docxtemplater';
import { saveAs } from 'file-saver';
import <PERSON><PERSON><PERSON><PERSON> from 'pizzip';

import { TemplateFileLabelsEnum } from 'enums/templateFiles';
import { FeatureNameType } from 'types';
import { showErrorToast } from 'ui/components/Toast';

const {
  LOAN_FIXED_AGREEMENT,
  LOAN_FLOAT_AGREEMENT,
  LOAN_FIXED_AGREEMENT_NO_PREPAYMENT,
  LOAN_STANDALONE_REPORT,
  LOAN_STANDALONE_REPORT_NO_LENDER_PERSPECTIVE,
  LOAN_ADJUSTED_REPORT,
  LOAN_ADJUSTED_REPORT_NO_LENDER_PERSPECTIVE,
  LOAN_PREPAYMENT_STANDALONE_REPORT,
  LOAN_PREPAYMENT_ADJUSTED_REPORT,
  LOAN_PREPAYMENT_STANDALONE_REPORT_NO_LENDER_PERSPECTIVE,
  LOA<PERSON>_PREPAYMENT_ADJUSTED_REPORT_NO_LENDER_PERSPECTIVE,
  GUARANTEE_AGREEMENT,
  G<PERSON><PERSON>ANTEE_YIELD_EXPECTED_LOSS_STANDALONE_REPORT,
  GUARANTEE_YIELD_EXPECTED_LOSS_ADJUSTED_REPORT,
  GUARANTEE_SECURITY_STANDALONE_REPORT,
  GUARANTEE_SECURITY_ADJUSTED_REPORT,
  B2B_LOAN_FIXED_AGREEMENT,
  B2B_LOAN_FLOAT_AGREEMENT,
  B2B_LOAN_STANDALONE_REPORT,
  B2B_LOAN_ADJUSTED_REPORT,
} = TemplateFileLabelsEnum;

const missingFieldsTextHeader =
  'Missing fields\nThe template was uploaded successfully, however you are missing some fields. If that was intended you can ignore this file. \n\nThe following fields are missing:\n';

const answerFields = [
  'answer1',
  'answer2',
  'answer3',
  'answer4',
  'answer5',
  'answer6',
  'answer7',
  'answer8',
  'answer9',
  'answer10',
  'answer11',
];

const answerFieldsWithAsterix = [
  'answer1*',
  'answer2*',
  'answer3*',
  'answer4*',
  'answer5*',
  'answer6*',
  'answer7*',
  'answer8*',
  'answer9*',
  'answer10*',
  'answer11*',
];

const fixedLoanAgreementFields = [
  'lender',
  'lender country',
  'borrower',
  'borrower country',
  'currency',
  'amount',
  'issue date',
  'maturity date',
  'seniority',
  'payment frequency',
  'rate',
  'unique id',
];

const prepaymentFields = ['prepayment', 'prepayment premium'];

const floatLoanAgreementFields = [...fixedLoanAgreementFields, 'reference rate', 'reference rate maturity'];

const loanReportStandaloneFieldsNoLenderPerspective = [
  'lender',
  'lender country',
  'BORROWER',
  'borrower',
  'borrower country',
  'borrower industry',
  'borrower rating',
  'selected entity',
  'selected entity country',
  'selected entity industry',
  'selected entity rating',
  'selected rating lower',
  'selected rating upper',
  'tenor',
  'tenor shorter',
  'tenor longer',
  'CURRENCY',
  'currency',
  'AMOUNT',
  'amount',
  'issue date',
  'maturity date',
  'seniority',
  'payment frequency',
  'reference rate',
  'reference rate maturity',
  'loan type',
  'interest label',
  'interest name',
  'interest type',
  'borrower base',
  'TL',
  'SL',
  'LL',
  'TU',
  'SU',
  'LU',
  'base',
  'SM',
  'LM',
  'upper bound',
  'lower bound',
  'report date',
  'data date',
  'data geography',
  'data geography borrower',
];

const loanReportStandaloneFields = [
  ...loanReportStandaloneFieldsNoLenderPerspective,
  'lender rating',
  'lender industry',
  'lender base',
  'data geography lender',
];

const guaranteeCommonFields = [
  'amount',
  'AMOUNT',
  'currency',
  'CURRENCY',
  'issue date',
  'payment frequency',
  'termination date',
  'period',
  'period shorter',
  'period longer',
  'guarantor country',
  'guarantor',
  'principal country',
  'principal industry',
  'principal rating lower',
  'principal rating upper',
  'principal rating',
  'principal',
  'PRINCIPAL',
  'report date',
  'data date',
  'data geography principal',
];

const guaranteeYieldExpectedLossApproachFields = [
  ...guaranteeCommonFields,
  'seniority',
  'guarantor industry',
  'guarantor rating lower',
  'guarantor rating upper',
  'guarantor rating',
  'GTU',
  'GSU',
  'GLU',
  'GSL',
  'GLL',
  'guarantor base',
  'GSM',
  'GLM',
  'PTU',
  'PSU',
  'PLU',
  'PTL',
  'PSL',
  'PLL',
  'principal base',
  'PSM',
  'PLM',
  'lgd',
  'pd',
  'el',
  'spread',
  'data geography guarantor',
];

const guaranteeSecurityApproachFields = [
  ...guaranteeCommonFields,
  'se',
  'un',
  'su',
  'ss',
  'us',
  'ss adj',
  'us adj',
  'PSLSS',
  'PSLUN',
  'PSLSU',
  'PSMSS',
  'PSMUN',
  'PSMSU',
  'PSUSS',
  'PSUUN',
  'PSUSU',
  'PTLSS',
  'PTLUN',
  'PTLSU',
  'PTUSS',
  'PTUUN',
  'PTUSU',
  'PLLSS',
  'PLLUN',
  'PLLSU',
  'PLMSS',
  'PLMUN',
  'PLMSU',
  'PLUSS',
  'PLUUN',
  'PLUSU',
];

const adjustedFieldsNoLenderPerspective = [
  'consolidated group rating',
  'borrower assessment',
  'selected entity rating adj',
  'selected rating lower adj',
  'selected rating upper adj',
  ...answerFields,
];

const adjustedFields = [...adjustedFieldsNoLenderPerspective, 'lender assessment', ...answerFieldsWithAsterix];

const guaranteeCommonAdjustedFields = ['consolidated group rating', 'principal assessment', ...answerFields];

const loanReportAdjustedFields = [...loanReportStandaloneFields, ...adjustedFields].filter(
  (field) => !['selected entity rating', 'selected rating lower', 'selected rating upper', 'loan type'].includes(field)
);

const loanReportAdjustedNoLenderPerspectiveFields = [
  ...loanReportStandaloneFieldsNoLenderPerspective,
  ...adjustedFieldsNoLenderPerspective,
].filter(
  (field) => !['selected entity rating', 'selected rating lower', 'selected rating upper', 'loan type'].includes(field)
);

const guaranteeYieldExpectedLossApproachAdjustedFields = [
  ...guaranteeYieldExpectedLossApproachFields,
  ...guaranteeCommonAdjustedFields,
  ...answerFieldsWithAsterix,
  'guarantor rating adj',
  'principal rating adj',
  'pd adj',
  'guarantor assessment',
].filter((field) => !['pd'].includes(field));

const guaranteeSecurityApproachAdjustedFields = [...guaranteeSecurityApproachFields, ...guaranteeCommonAdjustedFields];

const getFieldsToVerify = (
  fileLabel: TemplateFileLabelsEnum,
  features: { [key in FeatureNameType]: boolean }
): string[] => {
  if (fileLabel === LOAN_FIXED_AGREEMENT) {
    if (features.prepayment) return [...fixedLoanAgreementFields, ...prepaymentFields];
    return fixedLoanAgreementFields;
  }
  if (fileLabel === LOAN_FLOAT_AGREEMENT) return floatLoanAgreementFields;
  if (fileLabel === LOAN_FIXED_AGREEMENT_NO_PREPAYMENT) {
    if (features.prepayment) return [...fixedLoanAgreementFields, ...prepaymentFields];
    return fixedLoanAgreementFields;
  }

  if (fileLabel === LOAN_STANDALONE_REPORT) return loanReportStandaloneFields;
  if (fileLabel === LOAN_STANDALONE_REPORT_NO_LENDER_PERSPECTIVE) return loanReportStandaloneFieldsNoLenderPerspective;

  if (fileLabel === LOAN_PREPAYMENT_STANDALONE_REPORT) return [...loanReportStandaloneFields, ...prepaymentFields];
  if (fileLabel === LOAN_PREPAYMENT_STANDALONE_REPORT_NO_LENDER_PERSPECTIVE) {
    return [...loanReportStandaloneFieldsNoLenderPerspective, ...prepaymentFields];
  }

  if (fileLabel === LOAN_ADJUSTED_REPORT) return loanReportAdjustedFields;
  if (fileLabel === LOAN_ADJUSTED_REPORT_NO_LENDER_PERSPECTIVE) return loanReportAdjustedNoLenderPerspectiveFields;

  if (fileLabel === LOAN_PREPAYMENT_ADJUSTED_REPORT) return [...loanReportAdjustedFields, ...prepaymentFields];
  if (fileLabel === LOAN_PREPAYMENT_ADJUSTED_REPORT_NO_LENDER_PERSPECTIVE) {
    return [...loanReportAdjustedNoLenderPerspectiveFields, ...prepaymentFields];
  }

  if (fileLabel === GUARANTEE_AGREEMENT) return [];
  if (fileLabel === GUARANTEE_YIELD_EXPECTED_LOSS_STANDALONE_REPORT) return guaranteeYieldExpectedLossApproachFields;
  if (fileLabel === GUARANTEE_YIELD_EXPECTED_LOSS_ADJUSTED_REPORT) {
    return guaranteeYieldExpectedLossApproachAdjustedFields;
  }
  if (fileLabel === GUARANTEE_SECURITY_STANDALONE_REPORT) return guaranteeSecurityApproachFields;
  if (fileLabel === GUARANTEE_SECURITY_ADJUSTED_REPORT) return guaranteeSecurityApproachAdjustedFields;

  showErrorToast('B2B Loan template fields are not verified yet, but can still be used.');
  if (fileLabel === B2B_LOAN_FIXED_AGREEMENT) return [];
  if (fileLabel === B2B_LOAN_FLOAT_AGREEMENT) return [];
  if (fileLabel === B2B_LOAN_STANDALONE_REPORT) return [];
  if (fileLabel === B2B_LOAN_ADJUSTED_REPORT) return [];

  throw new Error('Not a valid file type');
};

export const verifyFilePlaceholders = async (
  file: File,
  fileLabel: TemplateFileLabelsEnum,
  features: { [key in FeatureNameType]: boolean }
) => {
  const buffer = await file.arrayBuffer();
  const zip = new PizZip(buffer);
  const docx = new Docxtemplater(zip);

  const fieldsToVerify = getFieldsToVerify(fileLabel, features);

  const wordText = docx.getFullText();
  let missingFieldsText = '';
  for (const field of fieldsToVerify) {
    if (!wordText.includes(`{${field}}`)) missingFieldsText += `${field}\n`;
  }
  if (missingFieldsText) {
    missingFieldsText = missingFieldsTextHeader + missingFieldsText;
    const blob = new Blob([missingFieldsText], { type: 'text/plain;charset=utf-8' });
    saveAs(blob, 'Missing fields.txt');
  }
};
