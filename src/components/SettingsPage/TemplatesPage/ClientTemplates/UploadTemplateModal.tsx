import React, { useEffect, useState } from 'react';

import { FlexLayout, Box, Button, Icon, Modal, Text, RadioGroup } from 'ui';
import { TemplateFileLabelsEnum, TemplateFileTypeEnum } from 'enums/templateFiles';
import { EmbeddedCompanyType, TemplateFileTypeType } from 'types';

import { FileLabelSingleSelect, FileTypeSingleSelect } from '../../SingleSelects';
import { CompanySingleSelect, CountrySingleSelect } from 'components/Shared';

type UploadTemplateModalPropsType = {
  isShowing?: boolean;
  dataTestId?: string;
  uploadedFile: File | undefined;
  isFileUploading: boolean;
  fileLabel: TemplateFileLabelsEnum | null;
  setFileLabel: React.Dispatch<React.SetStateAction<TemplateFileLabelsEnum | null>>;
  fileType: TemplateFileTypeType | null;
  setFileType: React.Dispatch<React.SetStateAction<TemplateFileTypeType | null>>;
  country: string | null;
  setCountry: React.Dispatch<React.SetStateAction<string | null>>;
  company: EmbeddedCompanyType;
  setCompany: React.Dispatch<React.SetStateAction<any>>;
  handleOnUploadClick: React.MouseEventHandler<HTMLButtonElement>;
  handleOnHide: React.MouseEventHandler<HTMLButtonElement>;
};

function UploadTemplateModal({
  isShowing = true,
  dataTestId,
  uploadedFile,
  isFileUploading,
  fileLabel,
  setFileLabel,
  fileType,
  setFileType,
  country,
  setCountry,
  company,
  setCompany,
  handleOnUploadClick,
  handleOnHide,
}: UploadTemplateModalPropsType) {
  const [templateType, setTemplateType] = useState(TemplateFileTypeEnum.CLIENT);
  useEffect(() => {
    setFileLabel(null);
  }, [fileType, setFileLabel]);

  if (!isShowing) return null;

  return (
    <Modal
      actionButtons={
        <Button
          text="Upload"
          dataTestId={dataTestId}
          disabled={!fileLabel || !fileType}
          loading={isFileUploading}
          onClick={handleOnUploadClick}
        />
      }
      title={'Upload template'}
      onHide={handleOnHide}
    >
      <FlexLayout alignItems="center" space={2}>
        <Icon icon="doc" size="m" />
        <Text color="bali-hai" variant="2l-spaced" shouldTruncate sx={{ fontStyle: 'italic', flexGrow: '1' }}>
          {uploadedFile?.name}
        </Text>
      </FlexLayout>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
        <FileTypeSingleSelect value={fileType} onChange={setFileType} />
        <FileLabelSingleSelect value={fileLabel} onChange={setFileLabel} disabled={!fileType} fileType={fileType} />
      </Box>
      <FlexLayout flexDirection="column" space={6}>
        <RadioGroup
          label="Template Type"
          options={[
            { label: 'Group', value: TemplateFileTypeEnum.CLIENT },
            { label: 'Country', value: TemplateFileTypeEnum.COUNTRY },
            { label: 'Company', value: TemplateFileTypeEnum.COMPANY },
          ]}
          width="fullWidth"
          value={templateType}
          onChange={setTemplateType}
        />
        <CountrySingleSelect
          isShowing={templateType === TemplateFileTypeEnum.COUNTRY}
          value={country}
          width="fullWidth"
          onChange={setCountry}
        />
        <CompanySingleSelect
          isShowing={templateType === TemplateFileTypeEnum.COMPANY}
          label="Company"
          value={company?.id}
          width="fullWidth"
          onChange={setCompany}
          disabled={false}
        />
      </FlexLayout>
    </Modal>
  );
}

export default UploadTemplateModal;
