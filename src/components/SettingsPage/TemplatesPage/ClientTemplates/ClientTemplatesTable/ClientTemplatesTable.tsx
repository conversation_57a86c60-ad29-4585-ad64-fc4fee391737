import { useContext } from 'react';

import { Button, Card, FileInput, FlexLayout, Table, Tabs, Text, WithTooltip } from 'ui';
import { TabType, TemplateFileType, TemplateTypeType } from 'types';
import { UserInfoContext } from 'context/user';
import { TemplateFileTypeEnum } from 'enums/templateFiles';

import { getColumns, getTemplateFilesTableData, renderTableActionColumn } from './ClientTemplatesTable.utils';
import NoTemplatesUploaded from '../NoTemplatesUploaded';

type ClientTemplatesTablePropsType = {
  templateType: TemplateTypeType;
  setTemplateType: React.Dispatch<React.SetStateAction<TemplateTypeType>>;
  templateFiles: TemplateFileType[];
  setTemplateFiles: React.Dispatch<React.SetStateAction<TemplateFileType[]>>;
  setIsUploadTemplateModalShowing: React.Dispatch<React.SetStateAction<boolean>>;
  onImportFileClick: Function;
};

const ClientTemplatesTable = ({
  templateType,
  setTemplateType,
  templateFiles,
  setTemplateFiles,
  setIsUploadTemplateModalShowing,
  onImportFileClick,
}: ClientTemplatesTablePropsType) => {
  const { userInfo } = useContext(UserInfoContext);

  const tabs: TabType<TemplateTypeType>[] = [
    { isEnabled: true, label: 'Group', value: TemplateFileTypeEnum.CLIENT },
    { isEnabled: true, label: 'Country', value: TemplateFileTypeEnum.COUNTRY },
    { isEnabled: true, label: 'Company', value: TemplateFileTypeEnum.COMPANY },
  ];

  const onTabSelect = (selectedTab: TemplateTypeType) => setTemplateType(selectedTab);

  return (
    <Card
      pb={3}
      pt={6}
      title={
        <FlexLayout justifyContent="space-between" alignItems="center" sx={{ lineHeight: 0 }}>
          <FlexLayout space={2} flexDirection="column">
            <Text color="deep-sapphire" variant="2l-spaced">
              Your templates
            </Text>
            <Text sx={{ marginTop: '12px' }}>
              Templates uploaded here will be applied instead of the default standard templates.
            </Text>
            <Text sx={{ marginTop: '16px' }}>
              Custom templates will be applied with the following priority: Company &gt; Country &gt; Group.
            </Text>
          </FlexLayout>
          <WithTooltip label="templateFileInput" tooltip="File must be .docx" disabled>
            <FileInput onChange={onImportFileClick} accept=".docx">
              <Button
                isShowing={templateFiles.length !== 0}
                text="Upload"
                variant="secondary"
                size="s"
                iconLeft="upload"
                onClick={() => setIsUploadTemplateModalShowing(true)}
              />
            </FileInput>
          </WithTooltip>
        </FlexLayout>
      }
    >
      <FlexLayout>
        <Tabs
          selectedTab={templateType}
          tabs={tabs}
          onTabSelect={onTabSelect}
          colorSelected="shakespeare"
          minWidth="tab-width-small"
          height="input-height-text"
        />
      </FlexLayout>
      <NoTemplatesUploaded
        isShowing={templateFiles.length === 0}
        setIsUploadTemplateModalShowing={setIsUploadTemplateModalShowing}
        onImportFileClick={onImportFileClick}
      />
      <FlexLayout flexDirection="column">
        <Table
          isShowing={templateFiles.length !== 0}
          actionColumn={(templateFile: TemplateFileType) =>
            renderTableActionColumn(templateFile, templateFiles, setTemplateFiles)
          }
          columns={getColumns(templateType)}
          data={getTemplateFilesTableData(templateFiles, userInfo)}
          isSearchable
        />
      </FlexLayout>
    </Card>
  );
};

export default ClientTemplatesTable;
