import { saveAs } from 'file-saver';

import { getClientTemplateFile, deleteClientTemplateFile } from 'api';
import { ThreeDotActionMenu } from 'components/Shared';
import { formatDateString } from 'utils/dates';
import type { TemplateFileType, TemplateTypeType, UserInfoType } from 'types';
import { showErrorToast, showToast } from 'ui/components/Toast';
import { TemplateFileTypeEnum } from 'enums/templateFiles';

export const getColumns = (templateType: TemplateTypeType) => {
  const columns = [
    { label: 'Document name', sortBy: 'documentName', value: 'documentName', width: 250 },
    { label: 'Label', sortBy: 'label', value: 'label', width: 250 },
    { label: 'Type', sortBy: 'type', value: 'type' },
    { label: 'Date added', sortBy: 'dateAdded', value: 'dateAdded' },
  ];

  if (templateType === TemplateFileTypeEnum.COUNTRY) {
    columns.push({ label: 'Country', sortBy: 'country', value: 'country' });
  }
  if (templateType === TemplateFileTypeEnum.COMPANY) {
    columns.push({ label: 'Company', sortBy: 'company', value: 'company' });
  }

  return columns;
};

const getTemplateFileTableData = (templateFile: TemplateFileType, userInfo: UserInfoType) => {
  const typeToTypeNameMapper = {
    loan: 'Loan',
    b2bLoan: 'Back-to-back Loan',
    guarantee: 'Guarantee',
  } as const;

  const { id, name, label, type, country, company, createdAt } = templateFile;

  return {
    id,
    documentName: name,
    label,
    type: typeToTypeNameMapper[type],
    country,
    company: company ? company.name : null,
    dateAdded: formatDateString(createdAt, userInfo.dateFormat),
  };
};

export const getTemplateFilesTableData = (data: TemplateFileType[], userInfo: UserInfoType) =>
  data.map((d) => getTemplateFileTableData(d, userInfo));

export const getTemplateFilesByType = (allTemplates: TemplateFileType[]) => {
  const templatesByType: {
    clientTemplates: TemplateFileType[];
    countryTemplates: TemplateFileType[];
    companyTemplates: TemplateFileType[];
  } = { clientTemplates: [], countryTemplates: [], companyTemplates: [] };

  for (const template of allTemplates) {
    if (template.companyId) templatesByType.companyTemplates.push(template);
    else if (template.country) templatesByType.countryTemplates.push(template);
    else templatesByType.clientTemplates.push(template);
  }

  return templatesByType;
};

export const renderTableActionColumn = (
  templateFile: TemplateFileType,
  templateFiles: TemplateFileType[],
  setTemplateFiles: React.Dispatch<React.SetStateAction<TemplateFileType[]>>
) => {
  const options = [
    {
      label: 'Download',
      onClick: () => {
        getClientTemplateFile(templateFile.id)
          .then((file: File) => {
            saveAs(file, file.name);
            showToast('Template downloaded successfully.');
          })
          .catch(() => showErrorToast());
      },
    },
    {
      label: 'Delete',
      onClick: () => {
        deleteClientTemplateFile(templateFile.id)
          .then(() => {
            setTemplateFiles(templateFiles.filter((t) => t.id !== templateFile.id));
            showToast('Template deleted successfully.');
          })
          .catch(() => showErrorToast());
      },
    },
  ];

  return <ThreeDotActionMenu options={options} />;
};
