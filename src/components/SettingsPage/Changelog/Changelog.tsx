import { Box, Card, FlexLayout, Text } from 'ui';

const Bullet = ({ marginTop = '8px' }: { marginTop?: string }) => (
  <Box bg="bali-hai" sx={{ marginTop, height: '4px', minWidth: '4px', borderRadius: 'round' }} />
);

const BulletPoint = ({ text }: { text: string }) => {
  return (
    <FlexLayout space={2}>
      <Bullet />
      <Text color="deep-sapphire" sx={{ lineHeight: '21px' }}>
        {text}
      </Text>
    </FlexLayout>
  );
};

const ChangelogTitle = ({ date }: { date: string }) => {
  return (
    <FlexLayout space={2} alignItems="center">
      <Bullet marginTop="inherit" />
      <Text color="deep-sapphire" variant="m-spaced-medium">
        Changes on
      </Text>
      <Text color="bali-hai" variant="m-spaced-medium">
        {date}
      </Text>
    </FlexLayout>
  );
};

const Tag = ({ bg }: { bg: 'scandal' | 'oasis' }) => {
  return (
    <FlexLayout
      py={1}
      bg={bg}
      alignItems="center"
      justifyContent="center"
      sx={{ padding: '6px 8px', width: 'max-content', borderRadius: 'm', marginBottom: '24px !important' }}
    >
      <Text variant="xs-spaced">{bg === 'scandal' ? '✅ New' : '🔨 Fixed'}</Text>
    </FlexLayout>
  );
};

const AddedTag = () => <Tag bg="scandal" />;

// const FixedTag = () => <Tag bg="oasis" />;

const Changelog = () => {
  return (
    <>
      <Card p={6}>
        <ChangelogTitle date="August 10, 2023" />
        <FlexLayout space={4} flexDirection="column" pl={8} sx={{ maxWidth: '800px' }}>
          <AddedTag />
          <BulletPoint text="Transaction flow illustration included for loans and guarantees" />
          <BulletPoint text="Option to export the Notes section to the automatically generated TP report added. Placeholder for this is {note}" />
          <BulletPoint text="Display of default cash pooling payment frequency set to a daily" />
          <BulletPoint text="Rates in cash pool updated to show four decimal places" />
          <BulletPoint text="Additional tool-tips, notifications, and warning messages added throughout the system" />
        </FlexLayout>
      </Card>
      <Card p={6}>
        <ChangelogTitle date="April 28, 2023" />
        <FlexLayout space={4} flexDirection="column" pl={8} sx={{ maxWidth: '800px' }}>
          <AddedTag />
          <BulletPoint text='New pricing approach "Security Approach" added for Guarantees' />
          <BulletPoint text='The original Guarantee approach "Yield - Expected Loss" updated to calculate and use cumulative probability of default matching the period of the guarantee' />
          <BulletPoint text="The order of income statement and balance sheet inputs in Credit Rating updated to reflect the order of standard financial statements. The Excel input template is also updated to reflect these changes." />
        </FlexLayout>
      </Card>
    </>
  );
};

export default Changelog;
