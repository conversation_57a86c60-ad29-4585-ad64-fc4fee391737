import React from 'react';

import { displayNumber2 } from '~/utils/strings';
import { isAdmin, NOTIFICATION_ACTIONS } from '~/enums';
import { ThreeDotActionMenu, DateTime } from '~/components/Shared';
import { setNotification } from '~/reducers/notifications.slice';

export function getAuditTrailData({ auditTrails = [], unit, currency, userInfo }) {
  const numberDisplayOptions = { decimalPoint: userInfo.decimalPoint, defaultValue: '-' };

  return auditTrails.map((auditTrail) => {
    const {
      id,
      name,
      updatedAt,
      debitInterestRate,
      creditInterestRate,
      operatingCost,
      operatingCostMarkup,
      assessment,
      overnightRate,
    } = auditTrail;

    return {
      id,
      name,
      createdAt: <DateTime withTime>{updatedAt}</DateTime>,
      debitRate: `${displayNumber2(debitInterestRate, { ...numberDisplayOptions, unit })}`,
      creditRate: `${displayNumber2(creditInterestRate, { ...numberDisplayOptions, unit })}`,
      operatingExpenses: `${displayNumber2(operatingCost, { ...numberDisplayOptions, currency })}`,
      operatingExpensesMarkup: `${displayNumber2(operatingCostMarkup, { ...numberDisplayOptions, unit: '%' })}`,
      assessment,
      overnightRate,
    };
  });
}

export function getAuditTrailColumns(isFixed, isNordic) {
  const columns = [{ label: 'Cash Pool Name', value: 'name' }];

  if (!isNordic)
    columns.push({ label: 'Credit Rate', value: 'creditRate' }, { label: 'Debit Rate', value: 'debitRate' });

  if (!isFixed) columns.push({ label: 'Overnight Rate', value: 'overnightRate' });

  columns.push(
    { label: 'Assessment', value: 'assessment' },
    { label: 'Operating Expenses', value: 'operatingExpenses' },
    { label: 'Operating Expenses Markup', value: 'operatingExpensesMarkup' },
    { label: 'Created', value: 'createdAt' }
  );

  return columns;
}

export function renderActionColumn(item, userInfo, setIsDeleteModalShowing, setSelectedItem, dispatch) {
  const options = [];

  if (isAdmin(userInfo.role)) {
    options.push({
      label: 'Delete',
      onClick: () => {
        setIsDeleteModalShowing(true);
        setSelectedItem(item);
      },
    });
  } else {
    options.push({
      label: 'Notify admin to delete the audit trail',
      onClick: () =>
        dispatch(
          setNotification({
            action: NOTIFICATION_ACTIONS.DELETE_AUDIT_TRAIL,
            title: 'Notify admin to delete the audit trail',
            id: item.id,
          })
        ),
    });
  }

  return <ThreeDotActionMenu options={options} />;
}
