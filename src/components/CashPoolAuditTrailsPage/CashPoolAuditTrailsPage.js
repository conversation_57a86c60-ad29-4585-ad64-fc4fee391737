import React, { useContext, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import { deleteCashPoolAuditTrail, getCashPoolAuditTrailAll, getCashPool } from '~/api';
import { DeleteModal } from '~/components/Modals';
import { UserInfoContext } from '~/context/user';
import { NORDIC } from '~/enums';
import { Button, Card, FlexLayout, LoadingSpinner, PageLayout, Table } from '~/ui';
import { showToast, showErrorToast } from '~/ui/components/Toast';
import { getAuditTrailColumns, getAuditTrailData, renderActionColumn } from './CashPoolAuditTrailsPage.utils';
import { cashPoolSelector, resetCashPool, setCashPool } from '~/reducers/cashPool.slice';
import { errorHandler } from '~/utils/errors';

function CashPoolAuditTrailsPage() {
  const dispatch = useDispatch();
  const history = useHistory();
  const { cashPoolId } = useParams();
  const [isLoading, setIsLoading] = useState(true);
  const [auditTrails, setAuditTrails] = useState([]);
  const [isDeleteModalShowing, setIsDeleteModalShowing] = useState(false);
  const [selectedItem, setSelectedItem] = useState();
  const { userInfo } = useContext(UserInfoContext);
  const cashPool = useSelector(cashPoolSelector);

  const isNordic = cashPool.type === NORDIC;
  const interestType = cashPool.interestType;
  const isFixed = interestType === 'fixed';
  const unit = isFixed ? '%' : ' bps';
  const currency = cashPool.currencies;

  useEffect(() => {
    getCashPoolAuditTrailAll({ cashPoolId }).then(setAuditTrails).catch(errorHandler);
  }, [cashPoolId]);

  useEffect(() => {
    getCashPool({ cashPoolId })
      .then((cashPool) => {
        dispatch(setCashPool(cashPool));
        setIsLoading(false);
      })
      .catch(errorHandler);

    return () => dispatch(resetCashPool());
  }, [cashPoolId, history, dispatch]);

  function onItemDelete() {
    deleteCashPoolAuditTrail({ cashPoolId, auditTrailId: selectedItem.id })
      .then(() => {
        setAuditTrails(auditTrails.filter((auditTrail) => auditTrail.id !== selectedItem.id));
        setIsDeleteModalShowing(false);
        setSelectedItem(null);
        showToast('Audit trail has been successfully deleted.');
      })
      .catch(() => showErrorToast());
  }

  if (isLoading) return <LoadingSpinner />;

  return (
    <>
      <PageLayout title="Audit Trail">
        <Card pb={3} pt={6} spaces={0}>
          <Table
            actionColumn={(item) =>
              renderActionColumn(item, userInfo, setIsDeleteModalShowing, setSelectedItem, dispatch)
            }
            columns={getAuditTrailColumns(isFixed, isNordic)}
            data={getAuditTrailData({ auditTrails, unit, currency, userInfo })}
            isSearchable
            onItemClick={({ id }) => history.push(`/cash-pools/${cashPoolId}/audit-trail/${id}`)}
          />
        </Card>
        <FlexLayout alignItems="flex-end" flexGrow="1" justifyContent="space-between">
          <Button text="Back" variant="gray" onClick={history.goBack} />
        </FlexLayout>
      </PageLayout>
      {isDeleteModalShowing && (
        <DeleteModal
          item="audit trail"
          handleOnDeleteClick={onItemDelete}
          handleOnHide={() => {
            setIsDeleteModalShowing(false);
            setSelectedItem(null);
          }}
        />
      )}
    </>
  );
}

export default CashPoolAuditTrailsPage;
