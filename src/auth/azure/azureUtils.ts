const azureOauthUrl = `${process.env.REACT_APP_AZURE_LOGIN_URL}/organizations/oauth2/v2.0`;
export const AZURE_LOGOUT_URL = `${azureOauthUrl}/logout`;

export function getCustomSubdomain() {
  const hostname = window.location.hostname;
  const parts = hostname.split('.');

  if (parts.length === 3) {
    return null;
  } else if (parts.length > 3) {
    return parts[0];
  } else {
    return null;
  }
}

function getRedirectUri() {
  const baseRedirectUri = process.env.REACT_APP_AZURE_REDIRECT_URI!;
  const hostname = window.location.hostname;

  const parts = hostname.split('.');

  let customSubdomain = null;
  if (parts.length > 3) {
    customSubdomain = parts[0];
  }

  if (!customSubdomain) {
    return baseRedirectUri;
  }

  const url = new URL(baseRedirectUri);

  const newHostname = `${customSubdomain}.${url.hostname}`;
  url.hostname = newHostname;

  return url.toString();
}

export const getAzureLoginUrl = () => {
  const azureLoginUrl = `${azureOauthUrl}/authorize?`;

  const customSubdomain = getCustomSubdomain();

  const searchParams = new URLSearchParams({
    client_id:
      customSubdomain === 'mars' ? process.env.REACT_APP_AZURE_MARS_CLIENT_ID! : process.env.REACT_APP_AZURE_CLIENT_ID!,
    redirect_uri: getRedirectUri(),
    scope:
      customSubdomain === 'mars' ? process.env.REACT_APP_AZURE_MARS_API_SCOPE! : process.env.REACT_APP_AZURE_API_SCOPE!,
    response_mode: 'query',
    response_type: 'code',
  });

  return `${azureLoginUrl}${searchParams}`;
};
