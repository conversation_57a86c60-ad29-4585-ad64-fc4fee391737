export type LoanTypeType = 'Bullet' | 'Balloon';

export type PaymentFrequencyType = 'Monthly' | 'Quarterly' | 'Semi-annual' | 'Annual';

export type LoanPricingApproachType = 'implicit' | 'stand-alone' | 'implicit non-standard' | 'stand-alone non-standard';
export type GuaranteePricingApproachType = 'implicit' | 'stand-alone';

export type SeniorityType = 'Senior Secured' | 'Unsubordinated' | 'Subordinated';

export type ReportStatusType = 'Draft' | 'Final';

export type ReportsTabValueType = 'loan' | 'guarantee';

type EmbeddedCompanyCreditRatingType = {
  rating: string;
  ratingAdj: string | null;
  probabilityOfDefault: number;
  probabilityOfDefaultAdj: number | null;
};

// columns `lender` and `borrower` in Loans and `guarantor` and `principal` in Guarantees
export type EmbeddedCompanyType = {
  id: number;
  parentCompanyId: number;
  name: string;
  industry: string;
  country: string;
  creditRating: EmbeddedCompanyCreditRatingType;
};

export type PrincipalEmbeddedCompanyType = EmbeddedCompanyType & {
  creditRating: EmbeddedCompanyCreditRatingType &
    Partial<{
      cumulativeProbabilityOfDefault: number;
      cumulativeProbabilityOfDefaultAdj: number | null;
    }>;
};

export type LoanGuaranteeReportType = {
  finalInterestRate: number;
  basisPoints?: number;
  estimateReferenceRate?: number;
  isWhtEnabled: boolean;
  whtInterestRate: number;
  approach: string;
  lowerBound?: number;
  midPoint?: number;
  upperBound?: number;
};
