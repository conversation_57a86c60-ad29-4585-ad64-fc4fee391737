import { ClientFeatureType, FeatureNameType } from './clientFeature.types';
import { DateFormatType } from './various.types';

import type { DecimalPointType } from 'types';

export type RoleType = 'user' | 'admin' | 'superadmin';

export type SocialLoginType = {
  provider: 'azure';
  credential: string;
};

export type UserType = {
  id: number;
  clientId: number;
  username: string;
  email: string;
  fullName: string;
  role: RoleType;
  areNotificationsMuted: boolean;
  dateFormat: DateFormatType;
  timezone: string;
  decimalPoint: DecimalPointType;
  createdAt: Date;
  updatedAt: Date;
  lastLogin: Date | null;
  socialLogins?: SocialLoginType[];
};

export type UpdateUserSettingsDataType = {
  fullName: string;
  dateFormat: DateFormatType;
  decimalPoint: DecimalPointType;
  timezone: string;
  oldPassword?: string;
  newPassword?: string;
};

export type UserInfoType = UserType & {
  client: {
    id: number;
    name: string;
    industry: string;
    clientFeatures: ClientFeatureType[];
    isLoanApproachCalculated: boolean;
    isCreditRatingAnonymized: boolean;
    dateFormat: DateFormatType;
    decimalPoint: DecimalPointType;
  };
  features: {
    [key in FeatureNameType]: any;
  };
};

export type CreateUserType = { fullName: string; clientId: number; role: RoleType };
