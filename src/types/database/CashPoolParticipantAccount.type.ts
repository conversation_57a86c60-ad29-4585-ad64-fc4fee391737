export interface CashPoolParticipantAccount {
  id: number;
  cashPoolParticipantId: number;
  balance: number | null;
  currency: string | null;
  creditInterestRate: number | null;
  debitInterestRate: number | null;
  adjustedCreditInterestRate: number | null;
  adjustedDebitInterestPaid: number | null;
  adjustedDebitInterestRate: number | null;
  adjustedCreditInterestReceived: number | null;
  netInterestBenefit: number | null;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
  topCurrencyAccountId: number;
  cirWithOvernightRate: number | null;
  dirWithOvernightRate: number | null;
}
