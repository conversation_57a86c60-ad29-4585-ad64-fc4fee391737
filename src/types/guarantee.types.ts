import {
  PaymentFrequencyType,
  ReportStatusType,
  SeniorityType,
  EmbeddedCompanyType,
  PrincipalEmbeddedCompanyType,
  LoanGuaranteeReportType,
  GuaranteePricingApproachType,
} from './report.types';
import { lenderOrGuarantorTypeEnum, pricingMethodologyEnum } from '../enums';

export type GuaranteeType = {
  id: number;
  issueDate: Date;
  terminationDate: Date;
  currency: string;
  amount: number;
  paymentFrequency: PaymentFrequencyType;
  seniority: SeniorityType;
  report: LoanGuaranteeReportType;
  createdAt: Date;
  updatedAt: Date;
  pricingApproach: GuaranteePricingApproachType;
  pricingMethodology: typeof pricingMethodologyEnum[keyof typeof pricingMethodologyEnum];
  guarantor: EmbeddedCompanyType;
  embeddedGuarantor: EmbeddedCompanyType;
  principal: PrincipalEmbeddedCompanyType;
  embeddedPrincipal: PrincipalEmbeddedCompanyType;
  clientId: number;
  editable: boolean;
  status: ReportStatusType;
  note: string;
  isPortfolio: boolean;
  createdBy: string;
  updatedBy: string;
  finalizedBy: string;
  calculationLog: any;
  movedToAnalysesDate: Date;
  totalInterest: number;
  isThirdParty: boolean;
  originalIssueDate: Date;
  deletedAt: Date;
};

export type GuaranteeTableItemType = {
  id: number;
  guarantorName: string;
  principalName: string;
  currency: string;
  amount: string;
  guarantorRatingStandalone: string;
  guarantorProbabilityOfDefault: string;
  guarantorRatingImplicitSupportAdj: string;
  principalRatingStandalone: string;
  principalProbabilityOfDefault: string;
  principalRatingImplicitSupportAdj: string;
  principalCumulativeProbabilityOfDefault?: string;
  principalProbabilityOfDefaultImplicitSupportAdj: string;
  issueDate: Date;
  terminationDate: Date;
  period: string;
  isPortfolio: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date;
  editable: boolean;
  status: ReportStatusType | 'Expired';
  finalInterestRate: string;
  report: LoanGuaranteeReportType;
  paymentFrequency: PaymentFrequencyType;
  lowerBound: string;
  midPoint: string;
  upperBound: string;
  guarantorType: `${lenderOrGuarantorTypeEnum}`;
};
