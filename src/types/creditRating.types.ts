import { ReportStatusType, EmbeddedCompanyType } from './report.types';
import { CreditRatingValueEnum } from 'enums/creditRating';

export type CreditRatingValueType = keyof typeof CreditRatingValueEnum;

export type CreditRatingType = {
  id: number;
  clientId: number;
  createdAt: Date;
  updatedAt: Date;
  creditRating: { rating: CreditRatingValueType };
  isPortfolio: boolean;
  editable: boolean;
  company: EmbeddedCompanyType;
  status: ReportStatusType;
  note: string;
  closingDate: Date;
  createdBy: string;
  updatedBy: string;
  finalizedBy: string;
  probabilityOfDefault: number;
  deletedAt: Date;
};

export type CreditRatingTableItemType = {
  id: number;
  name: string;
  isTemplateUploaded: boolean;
  fiscalYear: Date;
  rating: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date;
  editable: boolean;
  isPortfolio: boolean;
  status: ReportStatusType | 'Expired';
  probabilityOfDefault: string;
};
