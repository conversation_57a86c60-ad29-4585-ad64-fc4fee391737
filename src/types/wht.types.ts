import { whtEnums } from 'enums';

import { WHTRecipientType } from './whtRecipient.types';
import { WHTOriginType } from './whtOrigin.types';
import { LoanType } from './loan.types';

export type WHTRecipientWithOriginType = WHTRecipientType & {
  origin: WHTOriginType;
};

export type GetWHTRatesBody = {
  origin: string;
  recipient: string;
};

export type WHTApproachesType = keyof typeof whtEnums.WHT_APPROACHES;

export type WHTPayment = {
  id: number;
  loanId: number;
  paymentId: number;
  paymentNumber: number;
  isPaid: boolean;
  paymentAmount: number | null;
  totalNumberOfPayments: number;
  loan: LoanType;
  createdAt: Date;
  updatedAt: Date;
};
