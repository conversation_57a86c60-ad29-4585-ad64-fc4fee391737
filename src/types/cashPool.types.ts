import { CashPoolParticipant } from './database/CashPoolParticipant.type';
import { CashPoolParticipantAccount } from './database/CashPoolParticipantAccount.type';
import { Company } from './database/Company.type';

export type InTableParticipantType = Company &
  Pick<CashPoolParticipantAccount, 'currency' | 'creditInterestRate' | 'debitInterestRate'> & {
    value?: true;
    accountId?: number;
    generateInterestStatementData: boolean;
    uniqueId?: string;
  };

export type ParticipantRatesType = {
  participantId: number;
  debitRate: number;
  creditRate: number;
  calculationLog: any;
};

export type ExternalId = {
  externalId: string;
  cashPoolAccountId: number;
};

export type ExcludedId = {
  excludedId: string;
  cashPoolAccountId: number;
};

export type AccountType = CashPoolParticipantAccount & {
  companyId: number;
  error?: any;
  participant: CashPoolParticipant & { company: Company };
  externalIds: ExternalId[];
  excludedIds: ExcludedId[];
};

export type CompanyDataType = Pick<Company, 'id' | 'country' | 'industry' | 'name'> &
  Pick<CashPoolParticipantAccount, 'creditInterestRate' | 'debitInterestRate' | 'currency'> & {
    generateInterestStatementData: boolean;
    rating: string;
    ratingAdj: string;
    cirString: string;
    dirString: string;
    currencyString: string;
    isSelected: boolean;
    accountId?: number;
    externalIds?: ExternalId[];
    excludedIds?: ExcludedId[];
    uniqueId?: string;
    balance?: number | null;
  };
