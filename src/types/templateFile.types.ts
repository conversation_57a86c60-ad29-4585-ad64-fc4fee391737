import { FileContentTypeEnum } from 'enums/files';
import { TemplateFileLabelsEnum, TemplateFileTypeEnum } from 'enums/templateFiles';
import { EmbeddedCompanyType } from './report.types';

export type TemplateFileTypeType = 'loan' | 'b2bLoan' | 'guarantee';

export type TemplateFileType = {
  id: number;
  clientId: number;
  name: string;
  label: TemplateFileLabelsEnum;
  type: TemplateFileTypeType;
  extension: string;
  mimeType: FileContentTypeEnum;
  country: string;
  companyId: number;
  company: EmbeddedCompanyType;
  createdAt: Date;
  updatedAt: Date;
};

export type TemplateTypeType = `${TemplateFileTypeEnum}`;
