import { Company } from './database/Company.type';

export type ClientType = {
  id: number;
  name: string;
  emailDomains: Array<string>;
  industry: string;
  numberOfUsers: number | string;
  updatedAt: string;
  createdAt: string;
};

export type CreateClientDataType = {
  name: string;
  emailDomains: Array<string>;
  industry: string;
  companiesToCreate?: Array<Company>;
};

export enum Client {
  MARS = 'mars',
  GUNVOR = 'gunvor',
}
