import { GeographyDataType } from './various.types';

export type ClientFeatureStateType = {
  featureAvailability: {
    cashPool: boolean;
    cashPoolNumber: boolean;
    creditRating: boolean;
    creditRatingNumber: boolean;
    currency: boolean;
    financingAdvisory: boolean;
    geographyData: boolean;
    guarantee: boolean;
    guaranteeNumber: boolean;
    loan: boolean;
    backToBackLoan: boolean;
    loanNumber: boolean;
    backToBackLoanNumber: boolean;
    loanGuaranteeNumber: boolean;
    payment: boolean;
    userNumber: boolean;
    cuftData: boolean;
    isTemplateCashPoolBatchUpload: boolean;
    prepayment: boolean;
  };
  cashPoolNumber: number;
  creditRatingNumber: number;
  currency: {
    currencies: string[];
  };
  geographyData: GeographyDataType;
  guaranteeNumber: number;
  loanNumber: number;
  backToBackLoanNumber: number;
  loanGuaranteeNumber: number;
  userNumber: number;
};

export type ClientFeatureStateTypeWithErrors = ClientFeatureStateType & { errors: any };

export type FeatureNameType = keyof ClientFeatureStateType['featureAvailability'];

export type FeatureType = {
  id: number;
  name: FeatureNameType;
  fullName: string;
  path?: string;
  isModule: boolean;
  note: string;
  createdAt: Date;
  updatedAt: Date;
};

export type ClientFeatureType = {
  id: number;
  clientId: number;
  featureId: number;
  isEnabled: boolean;
  values: any;
  createdAt: Date;
  updatedAt: Date;
};

export type ClientFeatureWithFeatureType = ClientFeatureType & { feature: FeatureType };

export type ClientFeatureUsedNumbersType = {
  usedLoansNumber: number;
  usedGuaranteesNumber: number;
  usedBackToBackLoansNumber: number;
  usedCreditRatingsNumber: number;
  usedUsersNumber: number;
  usedCashPoolsNumber: number;
};
