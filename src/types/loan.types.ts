import {
  PaymentFrequencyType,
  ReportStatusType,
  LoanTypeType,
  SeniorityType,
  EmbeddedCompanyType,
  LoanGuaranteeReportType,
  LoanPricingApproachType,
} from './report.types';
import { lenderOrGuarantorTypeEnum, b2bLoansEnums } from 'enums';

type LoanRateType =
  | {
      type: 'fixed';
    }
  | {
      type: 'float';
      referenceRate: string;
      referenceRateMaturity: string;
    };

export type LoanType = {
  id: number;
  issueDate: Date;
  maturityDate: Date;
  currency: string;
  amount: number;
  paymentFrequency: PaymentFrequencyType;
  rateType: LoanRateType;
  seniority: SeniorityType;
  report: LoanGuaranteeReportType;
  createdAt: Date;
  updatedAt: Date;
  pricingApproach: LoanPricingApproachType;
  lender: EmbeddedCompanyType;
  embeddedLender: EmbeddedCompanyType;
  borrower: EmbeddedCompanyType;
  embeddedBorrower: EmbeddedCompanyType;
  clientId: number;
  editable: boolean;
  status: ReportStatusType;
  note: string;
  isPortfolio: boolean;
  isApproachCalculated: boolean;
  createdBy: string;
  updatedBy: string;
  finalizedBy: string;
  calculationLog: any;
  type: LoanTypeType;
  movedToAnalysesDate: Date;
  totalInterest: number;
  isThirdParty: boolean;
  originalIssueDate: Date;
  deletedAt: Date;
};

export type LoanTableItemType = {
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date;
  id: number;
  lenderName: string;
  borrowerName: string;
  currency: string;
  amount: string;
  principalRepayment: string;
  rateType: 'Fixed' | 'Float';
  loanType: LoanTypeType;
  dayCount: string;
  paymentFrequency: PaymentFrequencyType;
  borrowerRatingStandalone: string;
  borrowerRatingImplicitAdj: string;
  tenor: string;
  isPortfolio: boolean;
  issueDate: Date;
  maturityDate: Date;
  editable: boolean;
  status: ReportStatusType | 'Expired';
  seniority: SeniorityType;
  finalInterestRate: string;
  report: LoanGuaranteeReportType;
  lowerBound: string;
  midPoint: string;
  upperBound: string;
  lenderType: `${lenderOrGuarantorTypeEnum}`;
};

export type LoanRateTypeType = 'fixed' | 'float';

export type BetaType = (typeof b2bLoansEnums.betaTypes)[keyof typeof b2bLoansEnums.betaTypes];

export type BetaRegionsType = (typeof b2bLoansEnums.betaRegions)[keyof typeof b2bLoansEnums.betaRegions];

type B2BLoanCapmType = Partial<{
  requiredRateOfReturn: number;
  riskFreeRate: number;
  riskFreeRateSource: string;
  beta: number;
  betaSource: string;
  betaIndustrySector: string;
  betaRegion: string;
  betaType: 'Beta' | 'Unlevered Beta' | 'Unlevered Beta Corrected for Cash';
  equityRiskPremium: number;
  equityRiskPremiumSource: string;
  equityRiskPremiumCountry: string;
}>;

type B2BLoanCapmOverrideType = B2BLoanCapmType &
  Partial<{
    riskFreeRateIssueDate: Date;
    riskFreeRateTenor: number;
    riskFreeRateCurrency: string;
  }>;

type B2BLoanExpectedLossType = Partial<{
  expectedLoss: number;
  probabilityOfDefault: number;
  probabilityOfDefaultSource: string;
  probabilityOfDefaultType: 'Cumulative' | 'Annualized';
  lossGivenDefault: number;
  lossGivenDefaultSource: string;
}>;

type OverrideTogglesType = {
  riskFreeRate: boolean;
  beta: boolean;
  equityRiskPremium: boolean;
  probabilityOfDefault: boolean;
  lossGivenDefault: boolean;
};

export type StandardRemunerationOptionType =
  | {
      id: number;
      markup: number;
      operationalCost: number;
      type: 'Operating cost & Markup';
    }
  | { id: number; margin: number; type: '%' | 'basis points' };

type StandardRemunerationType = Record<string, StandardRemunerationOptionType>;

export type B2BLoanType = {
  id: number;
  issueDate: Date;
  maturityDate: Date;
  currency: string;
  amount: number;
  paymentFrequency: PaymentFrequencyType;
  rateType: LoanRateType;
  seniority: SeniorityType;
  report: LoanGuaranteeReportType;
  createdAt: Date;
  updatedAt: Date;
  pricingApproach: LoanPricingApproachType;
  lenders: Array<EmbeddedCompanyType>;
  borrowers: Array<EmbeddedCompanyType>;
  riskTakerId: number;
  capm: B2BLoanCapmType;
  capmRecommendation: B2BLoanCapmType;
  capmOverride: B2BLoanCapmOverrideType;
  expectedLoss: B2BLoanExpectedLossType;
  expectedLossRecommendation: B2BLoanExpectedLossType;
  expectedLossOverride: B2BLoanExpectedLossType;
  overrideToggles: OverrideTogglesType;
  standardRemuneration: StandardRemunerationType;
  clientId: number;
  editable: boolean;
  status: ReportStatusType;
  note: string;
  isPortfolio: boolean;
  createdBy: string;
  updatedBy: string | null;
  finalizedBy: string;
  calculationLog: any;
  type: LoanTypeType;
  movedToAnalysesDate: Date;
  totalInterest: number;
  originalIssueDate: Date;
  deletedAt: Date;
};
