import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  Lender: true,
  Borrower: true,
  'Loan type': true,
  Currency: true,
  'Interest amount': true,
  'Interest due': true,
  Status: true, // paid / unpaid
  'Interest number': true,

  'Rate type': false,
  'Interest frequency': false,
  'Issue date': false,
  'Maturity date': false,
  Rate: false,
  'Principal amount': false,
  Interest: false,
  'Reference rate': false,
};

export const loanPaymentColumnsSlice = createSlice({
  name: 'loanPaymentColumns',
  initialState,
  reducers: {
    resetForm: () => {
      return initialState;
    },
    setColumns: (state, action) => {
      return { ...state, ...action.payload };
    },
    updateColumns: (_state, action) => {
      return action.payload;
    },
  },
});

export default loanPaymentColumnsSlice.reducer;

export const { resetForm, setColumns, updateColumns } = loanPaymentColumnsSlice.actions;

export const loanPaymentColumns = (state) => state.loanPaymentColumns;
