import axios, { AxiosRequestConfig } from 'axios';

import { getAccessToken, removeAccessToken, removeAuthMethod, setAccessToken } from 'auth';
import { routesEnum } from 'routes';

import Configuration from './configuration';

type ResponseDestructuredDataType = {
  config: AxiosRequestConfig;
  data: any;
};

let isRefreshing = false;
let failedQueue: any[] = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};

export default function createService() {
  const instance = axios.create({
    baseURL: Configuration.backendEndpoint,
    withCredentials: true,
  });

  instance.interceptors.request.use(
    async (config: AxiosRequestConfig) => {
      config.headers.authorization = `Bearer ${getAccessToken()}`;
      config.headers['X-Request-Origin'] = window.location.origin;

      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  instance.interceptors.response.use(
    async (response) => {
      const { config, data }: ResponseDestructuredDataType = response;

      return config.fullResponse ? response : data;
    },
    async (error) => {
      const originalRequest = error.config;

      if (error.response?.status === 403) {
        removeAccessToken();
        removeAuthMethod();
        window.location.href = routesEnum.LOGIN;
        return Promise.reject(error);
      }

      if (error.response?.status === 401 && !originalRequest._retry) {
        if (isRefreshing) {
          return new Promise((resolve, reject) => {
            failedQueue.push({ resolve, reject });
          })
            .then((token) => {
              originalRequest.headers['authorization'] = `Bearer ${token}`;
              return instance(originalRequest);
            })
            .catch((err) => Promise.reject(err));
        }

        originalRequest._retry = true;
        isRefreshing = true;

        try {
          const response = await axios.post(
            `${Configuration.backendEndpoint}/user/refresh-token`,
            {},
            { withCredentials: true }
          );

          const newAccessToken = response.data.accessToken;
          setAccessToken(newAccessToken);

          processQueue(null, newAccessToken);
          originalRequest.headers['authorization'] = `Bearer ${newAccessToken}`;

          return instance(originalRequest);
        } catch (refreshError) {
          processQueue(refreshError, null);
          removeAccessToken();
          removeAuthMethod();
          window.location.href = routesEnum.LOGIN;
          return Promise.reject(refreshError);
        } finally {
          isRefreshing = false;
        }
      }

      return Promise.reject(error);
    }
  );

  return {
    get: instance.get,
    post: instance.post,
    delete: instance.delete,
    put: instance.put,
    patch: instance.patch,
  };
}
