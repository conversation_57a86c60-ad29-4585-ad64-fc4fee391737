import { backend } from 'services';
import type { ClientType, CreateClientDataType, DateFormatType, DecimalPointType } from 'types';

import { apiPaths } from './routes';

export function getClient(clientId: string): Promise<ClientType> {
  return backend.get(`${apiPaths.client}/${clientId}`);
}

export function getClients(): Promise<ClientType[]> {
  return backend.get(apiPaths.client);
}

export function createClient(data: CreateClientDataType): Promise<ClientType> {
  return backend.post(apiPaths.client, data);
}

export function updateClient(data: {
  isLoanApproachCalculated: boolean;
  isCreditRatingAnonymized: boolean;
  dateFormat: DateFormatType;
  decimalPoint: DecimalPointType;
}): Promise<ClientType> {
  return backend.patch(apiPaths.client, data);
}

export function deleteClient(id: number) {
  return backend.delete(`${apiPaths.client}/${id}`);
}
