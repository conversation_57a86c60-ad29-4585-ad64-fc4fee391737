import { reportEnum } from '~/enums';

import { getB2BLoansColumns } from '~/components/Shared/ReportsTables/BackToBackLoansTable/BackToBackLoansTable.utils';
import { getGuaranteesColumns } from '~/components/Shared/ReportsTables/GuaranteesTable/GuaranteesTable.utils';
import { getLoansColumns } from '~/components/Shared/ReportsTables/LoansTable/LoansTable.utils';

export function getTableColumnOrder(reportType) {
  const allColumnsVisible = {};

  switch (reportType) {
    case reportEnum.LOAN: {
      const loanColumnLabels = [
        'Lender',
        'Borrower',
        'Currency',
        'Amount',
        'Principal repayment',
        'Rate type',
        'Loan type',
        'Day count',
        'Interest compounding frequency',
        'Borrower rating standalone',
        'Borrower rating implicit adj.',
        'Issue date',
        'Maturity date',
        'Tenor (yr.)',
        'Pricing approach',
        'Created',
        'Updated',
        'Deleted',
        'Seniority',
        'Borrower prepayment option',
        'Prepayment premium',
        'Status',
        'Rate',
        'Lower bound',
        'Base',
        'Upper bound',
        'Lender Type',
        'Note',
        'Unique ID',
      ];

      loanColumnLabels.forEach((label) => {
        allColumnsVisible[label] = true;
      });

      const loanColumns = getLoansColumns(allColumnsVisible);
      return loanColumns.map((column) => column.label);
    }

    case reportEnum.GUARANTEE: {
      const guaranteeColumnLabels = [
        'Guarantor',
        'Principal',
        'Currency',
        'Amount',
        'Issue date',
        'Termination date',
        'Fee payment frequency',
        'Guarantor rating standalone',
        'Guarantor probability of default (1 yr.)',
        'Guarantor rating implicit adj.',
        'Principal rating standalone',
        'Principal probability of default (1 yr.)',
        'Principal cumulative probability of default',
        'Principal rating implicit adj.',
        'Created',
        'Updated',
        'Deleted',
        'Tenor (yr.)',
        'Pricing approach',
        'Status',
        'Rate',
        'Lower bound',
        'Base',
        'Upper bound',
        'Guarantor Type',
        'Note',
        'Unique ID',
      ];

      guaranteeColumnLabels.forEach((label) => {
        allColumnsVisible[label] = true;
      });

      const guaranteeColumns = getGuaranteesColumns(allColumnsVisible);
      return guaranteeColumns.map((column) => column.label);
    }

    case reportEnum.BACK_TO_BACK_LOAN: {
      const b2bLoanColumnLabels = [
        'Primary Lender',
        'Ultimate Borrower',
        'Currency',
        'Amount',
        'Principal repayment',
        'Rate type',
        'Loan type',
        'Day count',
        'Interest compounding frequency',
        'Ultimate Borrower rating standalone',
        'Ultimate Borrower rating implicit adj.',
        'Issue date',
        'Maturity date',
        'Tenor (yr.)',
        'Pricing approach',
        'Created',
        'Updated',
        'Deleted',
        'Seniority',
        'Status',
        'Rate',
        'Lower bound',
        'Base',
        'Upper bound',
        'Lender Type',
        'Note',
      ];

      b2bLoanColumnLabels.forEach((label) => {
        allColumnsVisible[label] = true;
      });

      const b2bColumns = getB2BLoansColumns(allColumnsVisible);
      return b2bColumns.map((column) => column.label);
    }

    default:
      return [];
  }
}

export function getOrderedColumnKeys(columnState, reportType) {
  const tableOrder = getTableColumnOrder(reportType);
  const availableColumns = Object.keys(columnState);

  return tableOrder.filter((columnLabel) => availableColumns.includes(columnLabel));
}
